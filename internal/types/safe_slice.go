package types

import (
	"sync"
)

type SafeSlice[T comparable] struct {
	slice []T
	mut   *sync.RWMutex
}

func NewSafeSlice[T comparable]() SafeSlice[T] {
	return SafeSlice[T]{
		slice: make([]T, 0),
		mut:   new(sync.RWMutex),
	}
}

func NewSafeSliceFrom[T comparable](items ...T) SafeSlice[T] {
	return SafeSlice[T]{
		slice: items,
		mut:   new(sync.RWMutex),
	}
}

func (s *SafeSlice[T]) Add(items ...T) {
	s.mut.Lock()
	defer s.mut.Unlock()

	s.slice = append(s.slice, items...)
}

func (s *SafeSlice[T]) Len() int {
	s.mut.RLock()
	defer s.mut.RUnlock()

	return len(s.slice)
}

func (s *SafeSlice[T]) Slice() []T {
	s.mut.Lock()
	defer s.mut.Unlock()

	return s.slice
}
