package types

import (
	"strconv"
	"sync"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestStringList_Add(t *testing.T) {
	underTest := NewStringList(make([]string, 0, 10))

	size := 20

	var wg sync.WaitGroup
	wg.Add(size)
	for i := 1; i <= size; i++ {
		go func(item string) {
			defer wg.Done()
			underTest.Add(item)
		}(strconv.Itoa(i))
	}
	wg.Wait()

	require.Equal(t, 20, len(underTest.Get()))
}
