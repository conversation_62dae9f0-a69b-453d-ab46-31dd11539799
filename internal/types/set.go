package types

import (
	"strings"
	"sync"
)

type zeroByte struct{}

// Deprecated: use sets.Of instead
type StringSet struct {
	set  map[string]zeroByte
	lock *sync.RWMutex
}

func NewStringSet(elements ...string) StringSet {
	out := StringSet{
		lock: new(sync.RWMutex),
		set:  make(map[string]zeroByte),
	}
	out.Add(elements...)
	return out
}

func (s StringSet) IsInitialized() bool {
	return s.set != nil && s.lock != nil
}

func (s StringSet) Add(elements ...string) {
	s.lock.Lock()
	defer s.lock.Unlock()

	for _, e := range elements {
		s.set[e] = zeroByte{}
	}
}

func (s StringSet) Remove(elements ...string) {
	s.lock.Lock()
	defer s.lock.Unlock()

	for _, e := range elements {
		delete(s.set, e)
	}
}

func (s StringSet) GetElements() []string {
	s.lock.RLock()
	defer s.lock.RUnlock()

	keys := make([]string, len(s.set))
	i := 0
	for k := range s.set {
		keys[i] = k
		i++
	}
	return keys
}

func (s StringSet) Has(element string) bool {
	s.lock.RLock()
	defer s.lock.RUnlock()

	return s.has(element)
}

func (s StringSet) has(element string) bool {
	_, exists := s.set[element]
	return exists
}

// HasAll the argument set is a subset of S
// i.e. let A be the set of the arguments ; A ⊆ S
func (s StringSet) HasAll(elements ...string) bool {
	s.lock.RLock()
	defer s.lock.RUnlock()

	return s.hasAll(elements...)
}

func (s StringSet) hasAll(elements ...string) bool {
	for _, e := range elements {
		if !s.has(e) {
			return false
		}
	}
	return true
}

// HasAny there exists at least one element of the argument set that is also an element of S
// i.e. the intersection of the argument set and S is not empty
// i.e. ∃x | x ∈ A && x ∈ S where A is the set of the arguments
func (s StringSet) HasAny(elements ...string) bool {
	s.lock.RLock()
	defer s.lock.RUnlock()

	for _, e := range elements {
		if s.has(e) {
			return true
		}
	}
	return false
}

func (s StringSet) Equal(elements ...string) bool {
	s.lock.RLock()
	defer s.lock.RUnlock()

	if NewStringSet(elements...).count() != s.count() {
		return false
	}

	return s.hasAll(elements...)
}

func (s StringSet) Minus(in StringSet) StringSet {
	s.lock.RLock()
	in.lock.RLock()
	defer s.lock.RUnlock()
	defer in.lock.RUnlock()

	out := NewStringSet()
	for k := range s.set {
		if !in.has(k) {
			out.Add(k)
		}
	}
	return out
}

func (s StringSet) Intersect(in StringSet) StringSet {
	s.lock.RLock()
	in.lock.RLock()
	defer s.lock.RUnlock()
	defer in.lock.RUnlock()

	out := NewStringSet()
	for k := range s.set {
		if in.has(k) {
			out.Add(k)
		}
	}
	return out
}

func (s StringSet) Count() int {
	s.lock.RLock()
	defer s.lock.RUnlock()

	return s.count()
}

func (s StringSet) count() int {
	return len(s.set)
}

func (s StringSet) Clone() StringSet {
	out := NewStringSet()
	out.Add(s.GetElements()...)
	return out
}

func (s *StringSet) PopToSlice() []string {
	s.lock.Lock()
	defer func() {
		s.set = make(map[string]zeroByte)
		s.lock.Unlock()
	}()

	keys := make([]string, len(s.set))
	i := 0
	for k := range s.set {
		keys[i] = k
		i++
	}
	return keys
}

func (s *StringSet) Decode(value string) error {
	// convention case "-" intentionally leave blank
	// db config case set eg. `os.Setenv("DRIVER_UPDATE_PROFILE_WHITELIST", "")` intentionally leave blank
	// db config case remove eg. `os.Unsetenv("DRIVER_UPDATE_PROFILE_WHITELIST"))` intentionally leave blank
	if value == "-" || value == "" {
		*s = NewStringSet()
		return nil
	}

	csvList := strings.Split(value, ",")
	for i := range csvList {
		csvList[i] = strings.TrimSpace(csvList[i])
	}
	*s = NewStringSet(csvList...)
	return nil
}

// Set[T] is set of Ts, implemented using Go std map.
// Use SetSafe[T] if you need thread-safe implementation.
type Set[T comparable] map[T]struct{}

func NewSet[T comparable]() Set[T] {
	return make(Set[T])
}

func NewSetFrom[T comparable](items ...T) Set[T] {
	set := make(Set[T])
	for i := range items {
		set.Add(items[i])
	}

	return set
}

// DiffAll returns all the differences in s1 and s2
func DiffAll[T comparable](s1, s2 Set[T]) Set[T] {
	diff := NewSet[T]()
	for k := range s1 {
		if s2.Contains(k) {
			continue
		}

		diff.Add(k)
	}

	for k := range s2 {
		if s1.Contains(k) {
			continue
		}

		diff.Add(k)
	}

	return diff
}

// Diff returns set of s1-s2
func Diff[T comparable](s1, s2 Set[T]) Set[T] {
	diff := NewSet[T]()
	for k := range s1 {
		if s2.Contains(k) {
			continue
		}

		diff.Add(k)
	}

	return diff
}

func (s Set[T]) Contains(item T) bool {
	_, ok := s[item]
	return ok
}

// ContainsAll returns whether s contains all of items,
// i.e. that items is subset of s
func (s Set[T]) ContainsAll(items ...T) bool {
	for i := range items {
		if !s.Contains(items[i]) {
			return false
		}
	}

	return true
}

// ContainsExact returns whether s contains only values from items
func (s Set[T]) ContainsExact(items ...T) bool {
	itemsSet := NewSetFrom(items...)
	if len(s) != len(itemsSet) {
		return false
	}

	for value := range s {
		if !itemsSet.Contains(value) {
			return false
		}
	}

	return true
}

func (s Set[T]) Add(items ...T) {
	for i := range items {
		s[items[i]] = struct{}{}
	}
}

func (s Set[T]) Remove(targets ...T) {
	for i := range targets {
		delete(s, targets[i])
	}
}

func (s Set[T]) Diff(other Set[T]) Set[T] {
	return Diff(s, other)
}

func (s Set[T]) DiffAll(other Set[T]) Set[T] {
	return DiffAll(s, other)
}

func (s Set[T]) Slice() []T {
	slice := make([]T, len(s))
	i := 0
	for item := range s {
		slice[i] = item
		i++
	}

	return slice
}

func (s Set[T]) Len() int {
	return len(s)
}

func (s Set[T]) Clone() Set[T] {
	cloned := make(Set[T])
	for v := range s {
		cloned[v] = struct{}{}
	}

	return cloned
}

// SetSafe[T] wraps Set[T] with mutex locks.
// Use Set[T] if it's going to be used in single-threaded use case.
type SetSafe[T comparable] struct {
	set Set[T]
	mut *sync.RWMutex
}

func NewSetSafe[T comparable]() SetSafe[T] {
	return SetSafe[T]{
		set: make(Set[T]),
		mut: new(sync.RWMutex),
	}
}

func NewSetSafeFrom[T comparable](items ...T) SetSafe[T] {
	return SetSafe[T]{
		set: NewSetFrom(items...),
		mut: new(sync.RWMutex),
	}
}

func (s *SetSafe[T]) Contains(item T) bool {
	s.mut.RLock()
	defer s.mut.RUnlock()

	return s.set.Contains(item)
}

func (s *SetSafe[T]) ContainsAll(items ...T) bool {
	s.mut.RLock()
	defer s.mut.RUnlock()

	return s.set.ContainsAll(items...)
}

func (s *SetSafe[T]) ContainsExact(item ...T) bool {
	s.mut.RLock()
	defer s.mut.RUnlock()

	return s.set.ContainsExact(item...)
}

func (s *SetSafe[T]) Add(items ...T) {
	s.mut.Lock()
	defer s.mut.Unlock()

	s.set.Add(items...)
}

func (s *SetSafe[T]) Remove(targets ...T) {
	s.mut.Lock()
	defer s.mut.Unlock()

	s.set.Remove(targets...)
}

func (s *SetSafe[T]) Slice() []T {
	s.mut.RLock()
	defer s.mut.RUnlock()

	return s.set.Slice()
}

func (s *SetSafe[T]) Len() int {
	s.mut.RLock()
	defer s.mut.Unlock()

	return s.set.Len()
}

func (s *SetSafe[T]) Clone() SetSafe[T] {
	s.mut.RLock()
	defer s.mut.RUnlock()

	return SetSafe[T]{
		set: s.set.Clone(),
		mut: new(sync.RWMutex),
	}
}
