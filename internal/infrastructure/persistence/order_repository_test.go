package persistence

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/absinthe/database/v2/mock_mongodb"
	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore/mock_redis"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

func TestDataStoreOrderRepository_CreateOrder(t *testing.T) {
	ctx := context.Background()

	t.Run("called Insert to orders collection", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		repo, deps := newMockOrderRepositoryWithConfig(ctrl, OrderConfig{
			OrderExpirationDuration: 9 * time.Minute,
			OrderCacheTTL:           8 * time.Minute,
		})

		deps.datastore.EXPECT().
			Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.cache.EXPECT().
			Set(ctx, "LMF-1231451", gomock.Any(), 8*time.Minute).
			Return(redis.NewStatusResult("1", nil))

		t := time.Date(2019, time.September, 9, 0, 0, 0, 0, time.UTC)

		repo.CreateOrder(context.Background(), &model.Order{
			Quote: model.Quote{
				QuoteID:   "LMFQ-1231451",
				CreatedAt: t,
				UpdatedAt: t,
			},
			OrderID: "LMF-1231451",
		})
	})

	t.Run("insertion fail when request got canceled/timeout", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		repo, _ := newMockOrderRepositoryWithConfig(ctrl, OrderConfig{
			OrderExpirationDuration: 9 * time.Minute,
			OrderCacheTTL:           8 * time.Minute,
		})

		t := time.Date(2019, time.September, 9, 0, 0, 0, 0, time.UTC)

		timeoutCtx, cancel := context.WithTimeout(context.Background(), 1*time.Nanosecond)
		defer cancel()

		err := repo.CreateOrder(timeoutCtx, &model.Order{
			Quote: model.Quote{
				QuoteID:   "LMFQ-1231451",
				CreatedAt: t,
				UpdatedAt: t,
			},
			OrderID: "LMF-1231451",
		})
		require.ErrorIs(tt, err, context.DeadlineExceeded)
	})

	t.Run("update UpdatedAt and CreatedAt before insert", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		repo, deps := newMockOrderRepositoryWithConfig(ctrl, OrderConfig{
			OrderExpirationDuration: 9 * time.Minute,
			OrderCacheTTL:           10 * time.Minute,
		})

		t := time.Date(2019, time.September, 9, 0, 0, 0, 0, time.UTC)

		deps.datastore.EXPECT().
			Insert(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, order *model.Order, opts ...repository.Option) error {
				require.True(tt, order.UpdatedAt.After(t))
				require.True(tt, order.CreatedAt.After(t))
				require.Equal(tt, 9*time.Minute, order.ExpireAt.Sub(order.CreatedAt))
				return nil
			})
		deps.cache.EXPECT().
			Set(ctx, "LMF-1231451", gomock.Any(), 10*time.Minute).
			Return(redis.NewStatusResult("1", nil))

		repo.CreateOrder(context.Background(), &model.Order{
			Quote: model.Quote{
				QuoteID:   "LMFQ-1231451",
				CreatedAt: t,
				UpdatedAt: t,
			},
			OrderID: "LMF-1231451",
		})
	})

	t.Run("initiate status and history before insert", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		repo, deps := newMockOrderRepositoryWithConfig(ctrl, OrderConfig{
			OrderExpirationDuration: 9 * time.Minute,
			OrderCacheTTL:           10 * time.Minute,
		})
		t := time.Date(2019, time.September, 9, 0, 0, 0, 0, time.UTC)

		deps.datastore.EXPECT().
			Insert(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, order *model.Order, opts ...repository.Option) error {
				require.Equal(tt, model.StatusAssigningDriver, order.Status)
				require.False(tt, order.History[string(model.StatusAssigningDriver)].IsZero())
				return nil
			})
		deps.cache.EXPECT().
			Set(ctx, "LMF-1231451", gomock.Any(), 10*time.Minute).
			Return(redis.NewStatusResult("1", nil))

		repo.CreateOrder(context.Background(), &model.Order{
			Quote: model.Quote{
				QuoteID:   "LMFQ-1231451",
				CreatedAt: t,
				UpdatedAt: t,
			},
			OrderID: "LMF-1231451",
		})
	})
}

func TestDataStoreOrderRepository_UpdateOrder(t *testing.T) {
	t.Parallel()

	ctx := context.Background()

	t.Run("update order to both data store and cache", func(tt *testing.T) {
		tt.Parallel()

		order := &model.Order{
			OrderID: "LMF-1234",
			Status:  model.StatusCanceled,
			History: map[string]time.Time{},
		}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		repo, deps := newMockOrderRepository(ctrl)

		deps.datastore.EXPECT().
			Replace(gomock.Any(), bson.M{"order_id": order.OrderID}, order).
			Return(nil)
		deps.cache.EXPECT().
			Set(ctx, order.OrderID, gomock.Any(), gomock.Any()).
			Return(redis.NewStatusResult("1", nil))

		repo.UpdateOrder(context.Background(), order)
	})

	t.Run("add order to order history", func(tt *testing.T) {
		tt.Parallel()

		order := &model.Order{
			OrderID: "LMF-1234",
			Status:  model.StatusCanceled,
			History: map[string]time.Time{},
		}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		repo, deps := newMockOrderRepository(ctrl)

		deps.datastore.EXPECT().
			Replace(gomock.Any(), bson.M{"order_id": order.OrderID}, order).
			DoAndReturn(func(_ context.Context, q bson.M, order *model.Order, opts ...repository.Option) error {
				timestamp, ok := order.History[string(model.StatusCanceled)]
				require.True(tt, ok, "order status should add to history.")
				require.False(tt, timestamp.IsZero(), "history should stamp with time.Now().")
				return nil
			})
		deps.cache.EXPECT().
			Set(ctx, order.OrderID, gomock.Any(), gomock.Any()).
			Return(redis.NewStatusResult("1", nil))

		repo.UpdateOrder(context.Background(), order)
	})
}

func TestDataStoreOrderRepository_Find(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	repo, deps := newMockOrderRepository(ctrl)

	deps.datastore.EXPECT().
		FindOne(gomock.Any(), bson.M{"order_id": "LMF-1231451"}, gomock.AssignableToTypeOf(&model.Order{})).
		SetArg(2, model.Order{
			OrderID: "LMF-1231451",
		}).
		Return(nil)

	order, err := repo.Get(context.Background(), "LMF-1231451")
	require.NoError(t, err)
	require.NotNil(t, order)
	require.Equal(t, "LMF-1231451", order.OrderID)
}

func TestDataStoreOrderRepository_CurrentStatus(t *testing.T) {
	t.Parallel()

	const orderID = "LMF-1231451"

	t.Run("get status from order.Status", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		repo, deps := newMockOrderRepository(ctrl)

		deps.datastore.EXPECT().
			FindOne(gomock.Any(), bson.M{"order_id": orderID}, gomock.AssignableToTypeOf(&model.Order{})).
			SetArg(2, model.Order{
				OrderID: orderID,
				Status:  model.StatusAssigningDriver,
			}).
			Return(nil)

		status, err := repo.CurrentStatus(context.Background(), orderID)
		require.NoError(t, err)
		require.Equal(t, model.StatusAssigningDriver, status)
	})

	t.Run("return nil and error when found some error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		repo, deps := newMockOrderRepository(ctrl)

		deps.datastore.EXPECT().
			FindOne(gomock.Any(), bson.M{"order_id": orderID}, gomock.AssignableToTypeOf(&model.Order{})).
			Return(errors.New("found some error"))

		status, err := repo.CurrentStatus(context.Background(), orderID)
		require.Error(t, err)
		require.Equal(t, model.Status(""), status)
	})
}

type orderRepositoryDeps struct {
	datastore           *mock_mongodb.MockDataStoreInterface
	deliveringDataStore *mock_mongodb.MockDataStoreInterface
	cache               *mock_redis.MockUniversalClient
}

func newMockOrderRepository(ctrl *gomock.Controller) (*MongoOrderRepository, *orderRepositoryDeps) {
	return newMockOrderRepositoryWithConfig(ctrl, defaultOrderConfig())
}

func newMockOrderRepositoryWithConfig(ctrl *gomock.Controller, config OrderConfig) (*MongoOrderRepository, *orderRepositoryDeps) {
	ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
	revisionDatastore := mock_mongodb.NewMockDataStoreInterface(ctrl)
	cache := mock_redis.NewMockUniversalClient(ctrl)
	revisionCfg := NewAtomicRevisionConfig(RevisionConfig{})
	txnHelper := mock_transaction.NewMockTxnHelper(ctrl)

	deps := &orderRepositoryDeps{
		datastore: ds,
		cache:     cache,
	}

	return NewMongoOrderRepository(deps.datastore, revisionDatastore, deps.cache, config, revisionCfg, txnHelper), deps
}

// default config for comment tests.
func defaultOrderConfig() OrderConfig {
	return OrderConfig{
		OrderExpirationDuration: 10 * time.Minute,
		OrderCacheTTL:           10 * time.Minute,
		DriverLastAttemptTTL:    12 * time.Hour,
	}
}
