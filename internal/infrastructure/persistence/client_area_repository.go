package persistence

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/localcache"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

const (
	clientAreaKeyID          = "client_area_id"
	clientAreaKeyServiceType = "service_type"
	clientAreaKeyArea        = "area"
	clientAreaKeyCreatedAt   = "created_at"
)

type MongoClientAreaRepository struct {
	ds      ClientAreaDataStore
	lcCache localcache.Caches
}

func (pr *MongoClientAreaRepository) Create(ctx context.Context, entity *model.ClientArea) error {
	if pr.ds.IsExist(ctx, bson.M{clientAreaKeyArea: entity.Area(), clientAreaKeyServiceType: entity.ServiceType()}) {
		return repository.ErrClientAreaDuplicate
	}
	return pr.ds.Insert(ctx, entity)
}

func (pr *MongoClientAreaRepository) Get(ctx context.Context, id string) (*model.ClientArea, error) {
	var result model.ClientArea
	if err := pr.ds.FindOne(ctx, bson.M{clientAreaKeyID: id}, &result); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	return &result, nil
}

func (pr *MongoClientAreaRepository) GetByRegionAndService(ctx context.Context, serviceType model.Service, region string) (*model.ClientArea, error) {
	var result model.ClientArea
	if err := pr.ds.FindOne(ctx, bson.M{clientAreaKeyArea: region, clientAreaKeyServiceType: serviceType}, &result); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	return &result, nil
}

func (pr *MongoClientAreaRepository) FindWithQueryAndSort(ctx context.Context, query repository.ClientAreaQuery, skip, limit int, sort []string, opts ...repository.Option) ([]model.ClientArea, error) {
	var result []model.ClientArea
	sel := bson.M{}
	if query.Area != "" {
		sel["area"] = primitive.Regex{Pattern: query.Area, Options: "i"}
	}
	if query.ServiceArea != "" {
		sel["service_areas"] = query.ServiceArea
	}

	if err := pr.ds.FindAndSort(ctx, sel, skip, limit, sort, &result, repository.ToDBOptions(opts)...); err != nil {
		return []model.ClientArea{}, err
	}

	return result, nil
}

func (pr *MongoClientAreaRepository) CountWithQuery(ctx context.Context, query repository.ClientAreaQuery, opts ...repository.Option) (int, error) {
	var count int
	sel := bson.M{}
	if query.Area != "" {
		sel["area"] = primitive.Regex{Pattern: query.Area, Options: "i"}
	}
	if query.ServiceArea != "" {
		sel["service_areas"] = query.ServiceArea
	}

	err := pr.ds.Count(ctx, sel, &count, repository.ToDBOptions(opts)...)
	if err != nil {
		return -1, err
	}

	return count, nil
}

func (pr *MongoClientAreaRepository) Find(ctx context.Context, skip, limit int) ([]model.ClientArea, error) {
	var result []model.ClientArea

	if err := pr.ds.FindAndSort(ctx, bson.M{}, skip, limit, []string{fmt.Sprintf("-%s", clientAreaKeyCreatedAt)}, &result); err != nil {
		return []model.ClientArea{}, err
	}

	return result, nil
}

func (pr *MongoClientAreaRepository) Count(ctx context.Context) (int, error) {
	var count int

	err := pr.ds.Count(ctx, bson.M{}, &count)
	if err != nil {
		return -1, err
	}

	return count, nil
}

func (pr *MongoClientAreaRepository) Update(ctx context.Context, entity *model.ClientArea) error {
	entity.SetUpdatedAt(time.Now().UTC())
	return pr.ds.Replace(ctx, bson.M{clientAreaKeyID: entity.ID()}, entity)
}

func (pr *MongoClientAreaRepository) Delete(ctx context.Context, entity *model.ClientArea) error {
	return pr.ds.Remove(ctx, bson.M{clientAreaKeyID: entity.ID()})
}

func (pr *MongoClientAreaRepository) GetServiceTypesInRegionWithCache(ctx context.Context, region string) ([]model.Service, error) {
	key := cache.ClientAreaServiceAreaKey(region)
	res, err := pr.lcCache.Get(ctx, key, func(ctx context.Context) (interface{}, error) {
		clientAreas, err := pr.FindWithQueryAndSort(ctx, repository.ClientAreaQuery{
			ServiceArea: region,
		}, 0, 0, []string{}, repository.WithReadSecondaryPreferred)
		if err != nil {
			return nil, err
		}

		return clientAreas, nil
	})
	if err != nil {
		return nil, fmt.Errorf("cannot find client areas: %w", err)
	}
	return model.ClientAreas(res.([]model.ClientArea)).UniqueServiceTypes(), nil
}

func ProvideMongoClientAreaRepository(ds ClientAreaDataStore, lcCache localcache.Caches, meter metric.Meter) *repository.ProxyClientAreaRepository {
	return repository.NewLatencyProxyClientAreaRepository(&MongoClientAreaRepository{
		ds:      ds,
		lcCache: lcCache,
	}, meter)
}

type ClientAreaDataStore mongodb.DataStoreInterface

func ProvideClientAreaDataStore(conn *mongodb.Conn) ClientAreaDataStore {
	return mongodb.NewDataStoreWithConn(conn, "client_areas")
}
