package persistence

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/require"
	"github.com/twpayne/go-geom"
	"github.com/uber/h3-go"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore/mock_redis"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestRedisDriverLocationRepository(t *testing.T) {
	ctx := context.Background()

	t.Run("UpdateDriverLocation", func(t *testing.T) {
		underTest, deps, finish := newRedisDriverLocationRepository(t)
		defer finish()

		req := repository.UpdateDriverLocationRequest{
			DriverID: "Driver0001",
			Location: model.LocationWithUpdatedAt{
				Lat: 13.123456,
				Lng: 100.123456,
			},
		}

		expectLyftHash := h3.ToString(h3.FromGeo(h3.GeoCoord{
			Latitude:  req.Location.Lat,
			Longitude: req.Location.Lng,
		}, underTest.atomCfg.Get().LyftIndexResolution))

		deps.pipeline.EXPECT().
			Set(gomock.Any(), "lyft_driver_location:"+req.DriverID, gomock.Any(), time.Duration(0)).
			Return(nil)

		deps.pipeline.EXPECT().
			Exec(gomock.Any()).
			Return(nil, nil)

		deps.redisClient.EXPECT().
			ZAdd(gomock.Any(), "lyft_geo_index_ids:"+expectLyftHash, gomock.Any()).
			Return(redis.NewIntResult(1, nil))

		deps.redisClient.EXPECT().
			ZRemRangeByScore(gomock.Any(), "lyft_geo_index_ids:"+expectLyftHash, "-inf", gomock.Any()).
			Return(redis.NewIntResult(1, nil))

		deps.redisClient.EXPECT().
			ZRemRangeByScore(gomock.Any(), "lyft_bike_geo_index_ids:"+expectLyftHash, "-inf", gomock.Any()).
			Return(redis.NewIntResult(1, nil))

		err := underTest.UpdateDriverLocation(ctx, req)

		require.NoError(t, err)
	})

	t.Run("UpdateDriverLocation with Bike service", func(t *testing.T) {
		underTest, deps, finish := newRedisDriverLocationRepositoryWithConfig(t, &AtomicDriverLocationConfig{
			config: DriverLocationConfig{
				MapDistanceEnabled:       true,
				MapDistanceMaxAttempt:    2,
				MapDistanceRetryDelay:    time.Second,
				LyftIndexResolution:      6,
				LyftIndexChildResolution: 9,
				UpdatedTimeout:           30 * time.Second,
				BikeLyftIndexEnable:      true,
			},
		})
		defer finish()

		req := repository.UpdateDriverLocationRequest{
			DriverID: "Driver0001",
			Location: model.LocationWithUpdatedAt{
				Lat: 13.123456,
				Lng: 100.123456,
			},
			IsBikeDriver: true,
		}

		expectLyftHash := h3.ToString(h3.FromGeo(h3.GeoCoord{
			Latitude:  req.Location.Lat,
			Longitude: req.Location.Lng,
		}, underTest.atomCfg.Get().LyftIndexResolution))

		deps.pipeline.EXPECT().
			Set(gomock.Any(), "lyft_driver_location:"+req.DriverID, gomock.Any(), time.Duration(0)).
			Return(nil)

		deps.pipeline.EXPECT().
			Exec(gomock.Any()).
			Return(nil, nil)

		deps.redisClient.EXPECT().
			ZAdd(gomock.Any(), "lyft_geo_index_ids:"+expectLyftHash, gomock.Any()).
			Return(redis.NewIntResult(1, nil))

		deps.redisClient.EXPECT().
			ZAdd(gomock.Any(), "lyft_bike_geo_index_ids:"+expectLyftHash, gomock.Any()).
			Return(redis.NewIntResult(1, nil))

		deps.redisClient.EXPECT().
			ZRemRangeByScore(gomock.Any(), "lyft_geo_index_ids:"+expectLyftHash, "-inf", gomock.Any()).
			Return(redis.NewIntResult(1, nil))

		deps.redisClient.EXPECT().
			ZRemRangeByScore(gomock.Any(), "lyft_bike_geo_index_ids:"+expectLyftHash, "-inf", gomock.Any()).
			Return(redis.NewIntResult(1, nil))

		err := underTest.UpdateDriverLocation(ctx, req)

		require.NoError(t, err)
	})

	t.Run("GetDriverLocationHandler", func(t *testing.T) {
		t.Run("returns list of drivers", func(t *testing.T) {
			underTest, deps, finish := newRedisDriverLocationRepository(t)
			defer finish()

			drivers := []model.DriverLocation{
				{
					DriverID: "R300d3",
					Location: model.Location{
						Lat: 13.200000211378729,
						Lng: 100.00030249357224,
					},
				},
				{
					DriverID: "R300d2",
					Location: model.Location{
						Lat: 13.200000211378729,
						Lng: 100.00030249357224,
					},
				},
			}
			driverIDs := []string{drivers[0].DriverID, drivers[1].DriverID}
			driver0JSON, _ := json.Marshal(drivers[0].Location)
			driver1JSON, _ := json.Marshal(drivers[1].Location)

			deps.redisClient.EXPECT().
				ZRangeByScore(ctx, gomock.Any(), gomock.Any()).
				Return(redis.NewStringSliceResult(driverIDs, nil)).
				AnyTimes()

			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[0].DriverID).
				Return(redis.NewStringResult(string(driver0JSON), nil))
			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[1].DriverID).
				Return(redis.NewStringResult(string(driver1JSON), nil))

			deps.pipeline.EXPECT().
				Exec(gomock.Any()).
				Return(nil, nil)

			driverLocations, _ := underTest.GetDrivers(ctx, repository.DriverLocationQuery{
				Location: model.Location{
					Lat: 13.123456,
					Lng: 100.123456,
				},
				From: 0,
				To:   1000,
			})

			require.Equal(t, 2, len(driverLocations))
		})

		t.Run("returns list of bike drivers", func(t *testing.T) {
			underTest, deps, finish := newRedisDriverLocationRepositoryWithConfig(t, &AtomicDriverLocationConfig{
				config: DriverLocationConfig{
					MapDistanceEnabled:       true,
					MapDistanceMaxAttempt:    2,
					MapDistanceRetryDelay:    time.Second,
					LyftIndexResolution:      6,
					LyftIndexChildResolution: 9,
					UpdatedTimeout:           30 * time.Second,
					BikeLyftIndexEnable:      true,
				},
			})
			defer finish()

			drivers := []model.DriverLocation{
				{
					DriverID: "R300d3",
					Location: model.Location{
						Lat: 13.200000211378729,
						Lng: 100.00030249357224,
					},
				},
				{
					DriverID: "R300d2",
					Location: model.Location{
						Lat: 13.200000211378729,
						Lng: 100.00030249357224,
					},
				},
			}
			driverIDs := []string{drivers[0].DriverID, drivers[1].DriverID}
			driver0JSON, _ := json.Marshal(drivers[0].Location)
			driver1JSON, _ := json.Marshal(drivers[1].Location)

			deps.redisClient.EXPECT().
				ZRangeByScore(ctx, gomock.Any(), gomock.Any()).
				Return(redis.NewStringSliceResult(driverIDs, nil)).
				MinTimes(1)

			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[0].DriverID).
				Return(redis.NewStringResult(string(driver0JSON), nil))
			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[1].DriverID).
				Return(redis.NewStringResult(string(driver1JSON), nil))

			deps.pipeline.EXPECT().
				Exec(gomock.Any()).
				Return(nil, nil)

			driverLocations, _ := underTest.GetDrivers(ctx, repository.DriverLocationQuery{
				Location: model.Location{
					Lat: 13.123456,
					Lng: 100.123456,
				},
				From:          0,
				To:            1000,
				IsBikeService: true,
			})

			require.Equal(t, 2, len(driverLocations))
		})

		t.Run("don't return riders in removeIDs", func(t *testing.T) {
			underTest, deps, finish := newRedisDriverLocationRepository(t)
			defer finish()

			drivers := []model.DriverLocation{
				{
					DriverID: "R300d3",
					Location: model.Location{
						Lat: 13.200000211378729,
						Lng: 100.00030249357224,
					},
				},
				{
					DriverID: "R300d2",
					Location: model.Location{
						Lat: 13.200000211378729,
						Lng: 100.00030249357224,
					},
				},
			}
			driverIDs := []string{drivers[0].DriverID, drivers[1].DriverID}
			driver0JSON, _ := json.Marshal(drivers[0].Location)
			driver1JSON, _ := json.Marshal(drivers[1].Location)

			deps.redisClient.EXPECT().
				ZRangeByScore(ctx, gomock.Any(), gomock.Any()).
				Return(redis.NewStringSliceResult(driverIDs, nil)).
				AnyTimes()

			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[0].DriverID).
				Return(redis.NewStringResult(string(driver0JSON), nil))
			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[1].DriverID).
				Return(redis.NewStringResult(string(driver1JSON), nil))

			deps.pipeline.EXPECT().
				Exec(gomock.Any()).
				Return(nil, nil)

			removeIDs := types.NewStringSet("R300d3")
			driverLocations, _ := underTest.GetDrivers(ctx, repository.DriverLocationQuery{
				Location: model.Location{
					Lat: 13.123456,
					Lng: 100.123456,
				},
				From:      0,
				To:        1000,
				RemoveIDs: &removeIDs,
			})

			require.Equal(t, 1, len(driverLocations))
			require.Equal(t, "R300d2", driverLocations[0].DriverID)
		})

		t.Run("filter by map distance", func(tt *testing.T) {
			underTest, deps, finish := newRedisDriverLocationRepository(t)
			underTest.atomCfg.config.MapDistanceEnabled = true
			defer finish()

			restaurantLocation := model.Location{Lat: 8, Lng: 9}
			drivers := []model.DriverLocation{
				{
					DriverID: "R300d3",
					Location: model.Location{
						Lat: 13,
						Lng: 101,
					},
				},
				{
					DriverID: "R300d2",
					Location: model.Location{
						Lat: 14,
						Lng: 102,
					},
				},
			}
			driverIDs := []string{drivers[0].DriverID, drivers[1].DriverID}
			driver0JSON, _ := json.Marshal(drivers[0].Location)
			driver1JSON, _ := json.Marshal(drivers[1].Location)
			deps.mapService.AddFakeDistance(drivers[0], restaurantLocation, 3000)
			deps.mapService.AddFakeDistance(drivers[1], restaurantLocation, 800)

			deps.redisClient.EXPECT().
				ZRangeByScore(ctx, gomock.Any(), gomock.Any()).
				Return(redis.NewStringSliceResult(driverIDs, nil)).
				AnyTimes()

			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[0].DriverID).
				Return(redis.NewStringResult(string(driver0JSON), nil)).
				AnyTimes()
			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[1].DriverID).
				Return(redis.NewStringResult(string(driver1JSON), nil)).
				AnyTimes()

			deps.pipeline.EXPECT().
				Exec(gomock.Any()).
				Return(nil, nil).
				AnyTimes()

			tt.Run("first round", func(t *testing.T) {
				driverLocations, _ := underTest.GetDrivers(ctx, repository.DriverLocationQuery{
					Location: restaurantLocation,
					From:     0,
					To:       1000,
				})

				require.Equal(t, 1, len(driverLocations))
				require.Equal(t, drivers[1].DriverID, driverLocations[0].DriverID)
			})

			tt.Run("second round", func(t *testing.T) {
				driverLocations, _ := underTest.GetDrivers(ctx, repository.DriverLocationQuery{
					Location: restaurantLocation,
					From:     1000,
					To:       3000,
				})

				require.Equal(t, 1, len(driverLocations))
				require.Equal(t, drivers[0].DriverID, driverLocations[0].DriverID)
			})

			tt.Run("third round no drivers", func(t *testing.T) {
				driverLocations, _ := underTest.GetDrivers(ctx, repository.DriverLocationQuery{
					Location: restaurantLocation,
					From:     5000,
					To:       8000,
				})

				require.Equal(t, 0, len(driverLocations))
			})

			tt.Run("map service return error in the first round", func(t *testing.T) {
				require.Equal(t, 2, underTest.atomCfg.config.MapDistanceMaxAttempt)

				err := errors.New("map service got an error")
				deps.mapService.errors = append(deps.mapService.errors, err)

				driverLocations, err := underTest.GetDrivers(ctx, repository.DriverLocationQuery{
					Location: restaurantLocation,
					From:     0,
					To:       1000,
				})

				require.NoError(t, err)
				require.Equal(t, 1, len(driverLocations))
				require.Equal(t, drivers[1].DriverID, driverLocations[0].DriverID)
			})

			tt.Run("map error exceed the max attempt", func(t *testing.T) {
				require.Equal(t, 2, underTest.atomCfg.config.MapDistanceMaxAttempt)

				err := errors.New("map service got an error")
				deps.mapService.errors = append(deps.mapService.errors, err)
				deps.mapService.errors = append(deps.mapService.errors, err)

				driverLocations, err := underTest.GetDrivers(ctx, repository.DriverLocationQuery{
					Location: restaurantLocation,
					From:     0,
					To:       1000,
				})

				require.Error(t, err)
				require.Nil(t, driverLocations)
			})
		})
	})

	t.Run("GetDriversInMultiPolygon", func(t *testing.T) {
		t.Run("should error when query with empty coordinate", func(t *testing.T) {
			underTest, _, finish := newRedisDriverLocationRepository(t)
			defer finish()
			_, err := underTest.GetDriversInMultiPolygon(ctx, repository.DriverLocationInMultiPolygonQuery{
				Coordinates: [][][]geom.Coord{},
			})
			require.Error(t, err)
		})
		t.Run("should return empty when polygon is empty", func(t *testing.T) {
			underTest, deps, finish := newRedisDriverLocationRepository(t)
			defer finish()
			deps.pipeline.EXPECT().
				Exec(gomock.Any()).
				Return(nil, nil)

			result, err := underTest.GetDriversInMultiPolygon(ctx, repository.DriverLocationInMultiPolygonQuery{
				Coordinates: [][][]geom.Coord{{}},
			})
			require.NoError(t, err)
			require.Empty(t, result)
		})
		t.Run("returns list of drivers", func(t *testing.T) {
			underTest, deps, finish := newRedisDriverLocationRepository(t)
			defer finish()

			drivers := []model.DriverLocation{
				// polygon 1
				{
					DriverID: "LMD1", // inside polygon, inside hash
					Location: model.Location{
						Lat: 13.3904,
						Lng: 101.0627,
					},
				},
				{
					DriverID: "LMD2", // inside polygon, inside hash
					Location: model.Location{
						Lat: 13.3300,
						Lng: 100.9767,
					},
				},
				{
					DriverID: "LMD3", // outside polygon, inside hash
					Location: model.Location{
						Lat: 13.4695,
						Lng: 101.0080,
					},
				},
				{
					DriverID: "LMD4", // inside hole, inside hash
					Location: model.Location{
						Lat: 13.3854,
						Lng: 101.0628,
					},
				},

				// polygon 2
				{
					DriverID: "LMD5", // inside polygon, inside hash
					Location: model.Location{
						Lat: 13.2933,
						Lng: 101.0669,
					},
				},

				// mock to be return from redis, just want to make sure it will be filtered out
				{
					DriverID: "LMD6", // outside polygon, outside hash
					Location: model.Location{
						Lat: 13.3337,
						Lng: 101.0485,
					},
				},
			}
			driverIDs := []string{drivers[0].DriverID, drivers[1].DriverID, drivers[2].DriverID, drivers[3].DriverID, drivers[4].DriverID, drivers[5].DriverID}
			driver0JSON, _ := json.Marshal(drivers[0].Location)
			driver1JSON, _ := json.Marshal(drivers[1].Location)
			driver2JSON, _ := json.Marshal(drivers[2].Location)
			driver3JSON, _ := json.Marshal(drivers[3].Location)
			driver4JSON, _ := json.Marshal(drivers[4].Location)
			driver5JSON, _ := json.Marshal(drivers[5].Location)

			deps.redisClient.EXPECT().
				ZRangeByScore(ctx, gomock.Any(), gomock.Any()).
				Return(redis.NewStringSliceResult(driverIDs, nil)).
				AnyTimes()

			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[0].DriverID).
				Return(redis.NewStringResult(string(driver0JSON), nil))
			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[1].DriverID).
				Return(redis.NewStringResult(string(driver1JSON), nil))
			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[2].DriverID).
				Return(redis.NewStringResult(string(driver2JSON), nil))
			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[3].DriverID).
				Return(redis.NewStringResult(string(driver3JSON), nil))
			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[4].DriverID).
				Return(redis.NewStringResult(string(driver4JSON), nil))
			deps.pipeline.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+drivers[5].DriverID).
				Return(redis.NewStringResult(string(driver5JSON), nil))

			deps.pipeline.EXPECT().
				Exec(gomock.Any()).
				Return(nil, nil)

			driverLocations, _ := underTest.GetDriversInMultiPolygon(ctx, repository.DriverLocationInMultiPolygonQuery{
				Coordinates: [][][]geom.Coord{
					{ // polygon 1
						{ // fence
							{
								100.98873125784723,
								13.26168863115015,
							},
							{
								100.99475793225685,
								13.299227860711738,
							},
							{
								101.03152064615398,
								13.347316501560002,
							},
							{
								101.1418087878439,
								13.302160368417404,
							},
							{
								101.10384073906516,
								13.472184863854139,
							},
							{
								101.0194672973347,
								13.471598779212783,
							},
							{
								101.00440061131138,
								13.462221229682598,
							},
							{
								101.00138727410729,
								13.467496146518428,
							},
							{
								100.98632058808397,
								13.46397954821353,
							},
							{
								100.9760752415886,
								13.469840516674196,
							},
							{
								100.97125390206065,
								13.433500199643106,
							},
							{
								100.98511525320237,
								13.394223022169086,
							},
							{
								100.97306190438303,
								13.353180315606764,
							},
							{
								100.92304050678655,
								13.33558844635786,
							},
							{
								100.8983311417087,
								13.302160368417404,
							},
							{
								100.92846451375539,
								13.254062766233545,
							},
							{
								100.98873125784723,
								13.26168863115015,
							},
						},
						{ // hole
							{
								101.0586025280993,
								13.388893311262308,
							},
							{
								101.0586025280993,
								13.377383180423166,
							},
							{
								101.07331966598866,
								13.377383180423166,
							},
							{
								101.07331966598866,
								13.388893311262308,
							},
							{
								101.0586025280993,
								13.388893311262308,
							},
						},
					},
					{ // polygon 2
						{
							{
								100.9996410721646,
								13.265868230207772,
							},
							{
								101.11795311177889,
								13.265238967257204,
							},
							{
								101.1321764170882,
								13.293554185946718,
							},
							{
								101.0339063076816,
								13.329834780893407,
							},
							{
								101.00545969706315,
								13.297119571858758,
							},
							{
								100.9996410721646,
								13.265868230207772,
							},
						},
					},
				},
			})

			require.Equal(t, 3, len(driverLocations))
			require.Contains(t, driverLocations, drivers[0])
			require.Contains(t, driverLocations, drivers[1])
			require.Contains(t, driverLocations, drivers[4])
		})
	})

	t.Run("GetDriverLocation", func(t *testing.T) {
		t.Parallel()

		const driverID = "<driver_id>"

		t.Run("found driver location", func(tt *testing.T) {
			tt.Parallel()

			underTest, deps, finish := newRedisDriverLocationRepository(t)
			defer finish()

			locJSON, _ := json.Marshal(model.Location{
				Lat: 13.0000,
				Lng: 100.54,
			})
			deps.redisClient.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+driverID).
				Return(redis.NewStringResult(string(locJSON), nil))

			dl, _ := underTest.GetDriverLocation(ctx, driverID)

			require.Equal(tt, driverID, dl.DriverID)
			require.Equal(tt, 13.0000, dl.Location.Lat)
			require.Equal(tt, 100.54, dl.Location.Lng)
		})

		t.Run("driver location not found", func(tt *testing.T) {
			tt.Parallel()

			underTest, deps, finish := newRedisDriverLocationRepository(t)
			defer finish()

			deps.redisClient.EXPECT().
				Get(gomock.Any(), lyftLocationKeyPrefix+driverID).
				Return(redis.NewStringResult("", redis.Nil))

			loc, err := underTest.GetDriverLocation(ctx, driverID)

			require.Nil(tt, loc)
			require.Error(tt, err, "driver location not found")
		})
	})
}

type redisDriverLocationRepositoryDeps struct {
	redisClient *mock_redis.MockUniversalClient
	pipeline    *mock_redis.MockPipeliner
	mapService  *fakeMapService
	bgBatch     *BackgroundBatch
}

func newRedisDriverLocationRepository(t *testing.T) (*RedisDriverLocationRepository, *redisDriverLocationRepositoryDeps, func()) {
	return newRedisDriverLocationRepositoryWithConfig(t, &AtomicDriverLocationConfig{
		config: DriverLocationConfig{
			MapDistanceEnabled:       true,
			MapDistanceMaxAttempt:    2,
			MapDistanceRetryDelay:    time.Second,
			LyftIndexResolution:      6,
			LyftIndexChildResolution: 9,
			UpdatedTimeout:           30 * time.Second,
			BikeLyftIndexEnable:      false,
		},
	})
}

func newRedisDriverLocationRepositoryWithConfig(t *testing.T, config *AtomicDriverLocationConfig) (*RedisDriverLocationRepository, *redisDriverLocationRepositoryDeps, func()) {
	ctrl := gomock.NewController(t)
	redisClient := mock_redis.NewMockUniversalClient(ctrl)
	pipeline := mock_redis.NewMockPipeliner(ctrl)
	redisClient.EXPECT().Pipeline().Return(pipeline).AnyTimes()
	cfg := config

	mapService := &fakeMapService{errors: make([]error, 0)}
	batch := NewBackgroundBatch(10, 1, time.Millisecond)
	batch.RegisterCallback(saveLocationToRedis(redisClient, cfg))
	batch.Start()

	cleanup := func() {
		batch.Stop()
		ctrl.Finish()
	}

	return &RedisDriverLocationRepository{
			redisClient: redisClient,
			mapService:  mapService,
			atomCfg:     cfg,
			bgBatch:     batch,
		},
		&redisDriverLocationRepositoryDeps{
			redisClient: redisClient,
			pipeline:    pipeline,
			mapService:  mapService,
			bgBatch:     batch,
		},
		cleanup
}

var _ mapservice.MapService = &fakeMapService{} //ensure implement interface

type fakeMapService struct {
	distanceFromTo map[mapservice.Location]map[mapservice.Location]float64
	// errors will pop an error after return an error in every call
	// use to force map service to return an error when calling `FindDistances`
	errors []error
}

func (f *fakeMapService) FindFastestRoute(ctx context.Context, location mapservice.Location, location2 mapservice.Location, bool2 bool, opts ...mapservice.RouteOptFn) (*model.MapRoute, []model.MapWaypoint, error) {
	panic("implement me")
}

func (f *fakeMapService) FindDistances(ctx context.Context, sources []mapservice.Location, to mapservice.Location) (*mapservice.Table, error) {
	if len(f.errors) != 0 {
		index := len(f.errors) - 1
		err := f.errors[index]
		f.errors = f.errors[:index]
		return nil, err
	}

	distances := make([]float64, len(sources))
	for i, loc := range sources {
		if _, ok := f.distanceFromTo[loc]; ok {
			distances[i] = f.distanceFromTo[loc][to]
		}
	}
	return &mapservice.Table{
		Distances: distances,
	}, nil
}

func (f *fakeMapService) FindDestinationDistances(ctx context.Context, sources mapservice.Location, to []mapservice.Location) (*mapservice.Table, error) {
	if len(f.errors) != 0 {
		index := len(f.errors) - 1
		err := f.errors[index]
		f.errors = f.errors[:index]
		return nil, err
	}

	distances := make([]float64, len(to))
	for i, loc := range to {
		if _, ok := f.distanceFromTo[sources]; ok {
			distances[i] = f.distanceFromTo[sources][loc]
		}
	}
	return &mapservice.Table{
		Distances: distances,
	}, nil
}

func (f *fakeMapService) AddFakeDistance(driverLocation model.DriverLocation, restaurantLocation model.Location, distance float64) {
	if f.distanceFromTo == nil {
		f.distanceFromTo = make(map[mapservice.Location]map[mapservice.Location]float64)
	}

	from := mapservice.Location(driverLocation.Location)
	to := mapservice.Location(restaurantLocation)
	if _, ok := f.distanceFromTo[from]; !ok {
		f.distanceFromTo[from] = make(map[mapservice.Location]float64)
	}
	f.distanceFromTo[from][to] = distance
}
