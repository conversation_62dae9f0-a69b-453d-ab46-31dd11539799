package persistence

import (
	"context"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type MongoRequestUpdateProfileSectionQuery struct {
	IDs []string
}

func BuildRequestUpdateProfileSectionQuery() repository.RequestUpdateProfileSectionQuery {
	return &MongoRequestUpdateProfileSectionQuery{}
}

func (q *MongoRequestUpdateProfileSectionQuery) WithIDs(ids []string) repository.RequestUpdateProfileSectionQuery {
	q.IDs = ids
	return q
}

func (q *MongoRequestUpdateProfileSectionQuery) Query() bson.M {
	query := bson.M{}

	if len(q.IDs) > 0 {
		var bsonIDs []primitive.ObjectID
		for _, id := range q.IDs {
			oid, err := primitive.ObjectIDFromHex(id)
			if err != nil {
				continue
			}
			bsonIDs = append(bsonIDs, oid)
		}
		query["_id"] = bson.M{"$in": bsonIDs}
	}

	return query
}

var _ repository.RequestUpdateProfileSectionRepository = &RequestUpdateProfileSectionRepo{}

type RequestUpdateProfileSectionRepo struct {
	ds RequestUpdateProfileSectionDataStore
}

func (repo *RequestUpdateProfileSectionRepo) Create(ctx context.Context, section *model.DriverRequestUpdateProfileSection) error {
	return repo.ds.Insert(ctx, section)
}

func (repo *RequestUpdateProfileSectionRepo) GetAll(ctx context.Context, skip, limit int, opts ...repository.Option) ([]model.DriverRequestUpdateProfileSection, error) {
	roc := make([]model.DriverRequestUpdateProfileSection, 0)

	err := repo.ds.FindAndSort(ctx, bson.M{}, skip, limit, []string{"-updated_at"}, &roc, repository.ToDBOptions(opts)...)

	if err != nil {
		return []model.DriverRequestUpdateProfileSection{}, err
	}
	return roc, nil
}

func (repo *RequestUpdateProfileSectionRepo) FindWithQueryAndSort(ctx context.Context, query repository.RequestUpdateProfileSectionQuery, skip, limit int, opts ...repository.Option) ([]model.DriverRequestUpdateProfileSection, error) {
	var driverRequestUpdateProfileSections []model.DriverRequestUpdateProfileSection

	mq, ok := query.(*MongoRequestUpdateProfileSectionQuery)
	if !ok {
		return nil, errors.New("query must be MongoRequestUpdateProfileSectionQuery")
	}

	q := mq.Query()
	if err := repo.ds.FindAndSort(ctx, q, skip, limit, []string{"-created_at"}, &driverRequestUpdateProfileSections, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return driverRequestUpdateProfileSections, nil
}

type RequestUpdateProfileSectionDataStore mongodb.DataStoreInterface

func ProvideRequestUpdateProfileSectionRepository(datastore RequestUpdateProfileSectionDataStore, meter metric.Meter) *repository.ProxyRequestUpdateProfileSectionRepository {
	return repository.NewLatencyProxyRequestUpdateProfileSectionRepository(&RequestUpdateProfileSectionRepo{ds: datastore}, meter)
}

func ProvideRequestUpdateProfileSectionDataStore(conn *mongodb.Conn) RequestUpdateProfileSectionDataStore {
	return mongodb.NewDataStoreWithConn(conn, "request_update_profile_sections")
}
