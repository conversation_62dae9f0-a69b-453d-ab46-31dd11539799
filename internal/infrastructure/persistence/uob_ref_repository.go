package persistence

import (
	"context"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var ErrInvalidUobRefQuery = errors.New("invalid query")

type UobRefQuery struct {
	uobRefID string
	driverID *string
}

func NewUobRefQuery() *UobRefQuery {
	return &UobRefQuery{}
}

func (q *UobRefQuery) WithUobRefID(uobRefID string) *UobRefQuery {
	q.uobRefID = uobRefID
	return q
}

func (q *UobRefQuery) WithDriverID(driverID string) *UobRefQuery {
	q.driverID = &driverID
	return q
}

func (u UobRefQuery) Query() (bson.M, error) {
	q := bson.M{}
	if u.uobRefID != "" {
		q["uob_ref_id"] = u.uobRefID
	}
	if u.driverID != nil {
		q["driver_id"] = u.driverID
	}
	return repository.ValidateQuery(q)
}

type MongoUobRefRepository struct {
	datastore UobRefDataStore
}

func (repo MongoUobRefRepository) RandomFindOne(ctx context.Context) (*model.UobRef, error) {
	uobRef := &model.UobRef{}
	if err := repo.datastore.AggregateOne(ctx, []bson.M{
		{
			"$match": bson.M{
				"$and": []bson.M{
					{
						"is_used": bson.M{
							"$exists": true,
						},
					},
					{
						"is_used": false,
					},
				},
			},
		},
		{
			"$sample": bson.M{
				"size": 1,
			},
		},
	}, uobRef); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return uobRef, nil
}

func (repo MongoUobRefRepository) FindOne(ctx context.Context, query repository.Query) (*model.UobRef, error) {
	q, err := query.Query()
	if err != nil {
		return nil, err
	}

	uobRef := &model.UobRef{}
	err = repo.datastore.FindOne(ctx, q, uobRef)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return uobRef, nil
}

func (repo MongoUobRefRepository) Update(ctx context.Context, uobRef *model.UobRef) error {
	return repo.datastore.Replace(ctx, bson.M{"uob_ref_id": uobRef.UobRefID}, uobRef)
}

func (repo MongoUobRefRepository) UpdateDriverID(ctx context.Context, uobRef *model.UobRef, newDriverID string) error {
	now := timeutil.BangkokNow()
	var selector bson.M
	if uobRef.DriverID == "" {
		selector = bson.M{
			"uob_ref_id": uobRef.UobRefID,
			"driver_id": bson.M{
				"$exists": false,
			},
		}
	} else {
		selector = bson.M{
			"uob_ref_id": uobRef.UobRefID,
			"driver_id":  uobRef.DriverID,
		}
	}

	var setter bson.M
	if newDriverID == "" {
		setter = bson.M{
			"$unset": bson.M{"driver_id": ""},
			"$set":   bson.M{"updated_at": now, "is_used": false},
		}
	} else {
		setter = bson.M{
			"$set": bson.M{"driver_id": newDriverID, "updated_at": now, "is_used": true},
		}
	}

	if err := repo.datastore.Update(ctx, selector, setter); err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}

	return nil
}

func NewMongoUobRefRepository(ds UobRefDataStore) *MongoUobRefRepository {
	return &MongoUobRefRepository{
		datastore: ds,
	}
}

func ProvideMongoUobRefRepository(ds UobRefDataStore, meter metric.Meter) *repository.ProxyUobRefRepository {
	return repository.NewLatencyProxyUobRefRepository(NewMongoUobRefRepository(ds), meter)
}

type UobRefDataStore mongodb.DataStoreInterface

func ProvideUobRefDataStore(db *mongodb.Conn) UobRefDataStore {
	return mongodb.NewDataStoreWithConn(db, "uob_refs")
}
