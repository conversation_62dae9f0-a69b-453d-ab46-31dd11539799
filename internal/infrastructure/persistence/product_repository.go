package persistence

import (
	"context"
	"strings"
	"time"

	"github.com/kelseyhightower/envconfig"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	inventoryPb "git.wndv.co/go/proto/lineman/inventory/v1"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type MongoProductQuery struct {
	Name         string
	SKU          string
	PrioritySort string
	ProductGroup string
	MinimumPrice float64
	MaximumPrice float64
}

func (q *MongoProductQuery) WithName(name string) repository.ProductQuery {
	q.Name = name
	return q
}

func (q *MongoProductQuery) WithSKU(SKU string) repository.ProductQuery {
	q.SKU = SKU
	return q
}

func (q *MongoProductQuery) WithPrioritySort(sort string) repository.ProductQuery {
	q.PrioritySort = sort
	return q
}

func (q *MongoProductQuery) WithProductGroup(productGroup string) repository.ProductQuery {
	q.ProductGroup = productGroup
	return q
}

func (q *MongoProductQuery) WithPrice(min float64, max float64) repository.ProductQuery {
	q.MinimumPrice = min
	q.MaximumPrice = max
	return q
}

func (q *MongoProductQuery) Query() bson.M {
	query := bson.M{}

	if q.Name != "" {
		query["name"] = q.Name
	}

	if q.SKU != "" {
		query["sku"] = q.SKU
	}

	if q.ProductGroup != "" {
		objID, err := primitive.ObjectIDFromHex(q.ProductGroup)
		if err != nil {
			query["product_group"] = primitive.ObjectID{}
		} else {
			query["product_group"] = objID
		}
	}

	bsonPrice := bson.M{}
	if q.MinimumPrice > 0 {
		bsonPrice["$gte"] = q.MinimumPrice
	}
	if q.MaximumPrice > 0 {
		bsonPrice["$lte"] = q.MaximumPrice
	}
	if len(bsonPrice) > 0 {
		query["price"] = bsonPrice
	}

	return query
}
func BuildProductQuery() repository.ProductQuery {
	return &MongoProductQuery{}
}

var _ repository.ProductRepository = (*ProductRepo)(nil)

type ProductRepo struct {
	ds                               ProductDataStore
	EnableInventoryManagementService bool `envconfig:"ENABLE_IMS"  default:"false"`
	productService                   inventoryPb.ProductServiceClient
}

func (p *ProductRepo) CreateAll(ctx context.Context, products []model.Product, _ ...repository.Option) error {

	now := time.Now().UTC()
	data := make([]interface{}, len(products))
	for i, itm := range products {
		itm.CreatedAt = now
		itm.UpdatedAt = now
		data[i] = itm
	}
	if err := p.ds.InsertMany(ctx, data); err != nil {
		return err
	}

	return nil
}

func (p *ProductRepo) FindWithQueryAndSort(ctx context.Context, query repository.ProductQuery, skip, limit int, sort []string, opts ...repository.Option) ([]model.Product, error) {
	var products []model.Product

	mq, ok := query.(*MongoProductQuery)
	if !ok {
		return nil, errors.New("query must be MongoProductQuery")
	}

	q := mq.Query()
	if err := p.ds.FindAndSort(ctx, q, skip, limit, sort, &products, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return products, nil
}

func (p *ProductRepo) CountProductWithQuery(ctx context.Context, query repository.ProductQuery, opts ...repository.Option) (int, error) {
	var count int

	mq, ok := query.(*MongoProductQuery)
	if !ok {
		return 0, errors.New("query must be MongoProductQuery")
	}
	q := mq.Query()

	if err := p.ds.Count(ctx, q, &count, repository.ToDBOptions(opts)...); err != nil {
		return 0, err
	}

	return count, nil
}

func (p *ProductRepo) FindProductsBySKUs(ctx context.Context, skus []string, opts ...repository.Option) (map[string]*model.Product, error) {
	lenOfSKUs := len(skus)
	existingSKUs := make(map[string]*model.Product, lenOfSKUs)

	if p.EnableInventoryManagementService {
		listProductReq := inventoryPb.ListProductRequest{
			Skus: skus,
		}
		listProductRes, err := p.productService.ListProduct(ctx, &listProductReq)
		if err != nil {
			return existingSKUs, err
		}

		for _, item := range listProductRes.Data {
			if item == nil {
				continue
			}
			primitiveID, err := primitive.ObjectIDFromHex(item.Id)
			if err != nil {
				logrus.Warnf("findProductsBySKUs: unable to parse product ID [%s] to ObjectID", item.Id)
			}
			productModel := model.Product{
				ID:              primitiveID,
				Name:            item.Name,
				SKU:             item.Sku,
				Priority:        int(item.Priority),
				Price:           float64(types.NewMoneyWithDecimal(item.Price)),
				Description:     item.Description,
				CreatedBy:       item.CreatedBy,
				CreatedAt:       item.CreatedAt.AsTime(),
				UpdatedBy:       item.UpdatedBy,
				UpdatedAt:       item.UpdatedAt.AsTime(),
				PriorityGroupID: item.PriorityGroupId,
				BatchType:       model.ProductBatchTypeFromPbEnum(item.BatchGroup.String()),
			}
			existingSKUs[productModel.SKU] = &productModel
		}
		return existingSKUs, nil
	}

	batchSize := 100
	for i := 0; i < lenOfSKUs; i += batchSize {
		end := i + batchSize
		if end > lenOfSKUs {
			end = lenOfSKUs
		}

		batchSKUs := skus[i:end]
		result := make([]*model.Product, 0, len(batchSKUs))
		if err := p.ds.FindWithSelector(
			ctx,
			bson.M{
				"sku": bson.M{
					"$in": batchSKUs,
				},
			},
			bson.M{
				"sku": 1,
			},
			&result,
			repository.ToDBOptions(opts)...,
		); err != nil {
			return nil, err
		}

		for _, d := range result {
			existingSKUs[d.SKU] = d
		}
	}

	return existingSKUs, nil
}

func (p *ProductRepo) FindByIDsAndLookupInstallment(ctx context.Context, productIDs []primitive.ObjectID, skip, limit int, opts ...repository.Option) ([]model.Product, error) {
	var products []model.Product

	bsonMatchValue := bson.M{
		"_id": bson.M{"$in": productIDs},
	}
	matchStage := bson.D{
		{
			Key:   "$match",
			Value: bsonMatchValue,
		},
	}
	skipStage := bson.D{
		{
			Key:   "$skip",
			Value: skip,
		},
	}
	limitStage := bson.D{
		{
			Key:   "$limit",
			Value: limit,
		},
	}
	lookupInstallmentsStage := bson.D{
		{
			Key: "$lookup",
			Value: bson.M{
				"from":         "installments",
				"localField":   "sku",
				"foreignField": "sku",
				"as":           "installments",
			},
		},
	}
	mongoPipeline := mongo.Pipeline{matchStage, skipStage, lookupInstallmentsStage}
	if limit > 0 {
		// to prevent `the limit must be positive` error from mongo
		mongoPipeline = mongo.Pipeline{matchStage, skipStage, limitStage, lookupInstallmentsStage}
	}
	if err := p.ds.Aggregate(ctx, mongoPipeline, &products); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	return products, nil
}

func (p *ProductRepo) UpdateAll(ctx context.Context, products []model.Product) error {
	if len(products) == 0 {
		return nil
	}

	var models []mongo.WriteModel
	for _, product := range products {
		selector := bson.M{"_id": product.ID}
		replaceModel := mongo.NewReplaceOneModel().SetFilter(selector).SetReplacement(product)
		models = append(models, replaceModel)
	}

	result, err := p.ds.BulkWrite(ctx, models, &options.BulkWriteOptions{})
	if err != nil {
		return err
	}

	logrus.Infof("found %d, updated %d products", result.MatchedCount, result.ModifiedCount)
	return nil
}

func (p *ProductRepo) Update(ctx context.Context, product *model.Product) error {
	return p.ds.Update(
		ctx,
		bson.M{"_id": product.ID},
		bson.D{
			{
				Key: "$set",
				Value: bson.M{
					"name":          product.Name,
					"sku":           product.SKU,
					"priority":      product.Priority,
					"product_group": product.ProductGroup,
					"price":         product.Price,
					"description":   product.Description,
					"updated_by":    product.UpdatedBy,
					"updated_at":    timeutil.BangkokNow(),
				},
			},
		},
	)
}

func (p *ProductRepo) FindByIDAndLookupInstallment(ctx context.Context, id string, opts ...repository.Option) (*model.Product, error) {
	oid, err := primitive.ObjectIDFromHex(strings.TrimSpace(id))
	if err != nil {
		return nil, err
	}

	product := &model.Product{}
	if err := p.ds.AggregateOne(ctx, []bson.M{
		{
			"$match": bson.M{
				"_id": oid,
			},
		},
		{
			"$lookup": bson.M{
				"from":         "installments",
				"localField":   "sku",
				"foreignField": "sku",
				"as":           "installments",
			},
		},
		{
			"$set": bson.M{
				"installments": "$installments",
			},
		},
	}, product); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return product, nil
}

func ProvideProductRepository(
	datastore ProductDataStore,
	meter metric.Meter,
	productService inventoryPb.ProductServiceClient,
) *repository.ProxyProductRepository {
	repo := ProductRepo{
		ds:             datastore,
		productService: productService,
	}
	envconfig.MustProcess("", &repo)
	logrus.Infof("ProductRepo: EnableIMS: %v", repo.EnableInventoryManagementService)
	return repository.NewLatencyProxyProductRepository(&repo, meter)
}

type ProductDataStore mongodb.DataStoreInterface

func ProvideProductDataStore(conn *mongodb.Conn) ProductDataStore {
	return mongodb.NewDataStoreWithConn(conn, "products")
}
