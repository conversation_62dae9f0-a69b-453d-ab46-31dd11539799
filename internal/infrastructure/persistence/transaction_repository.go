package persistence

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/crypt"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type DataStoreTransactionRepository struct {
	datastore TransactionDataStore
}

type TransactionQuery struct {
	DriverID                    string
	TransRefId                  string
	WithdrawRefId               string
	Category                    model.TransactionCategory
	WalletType                  model.TransactionWalletType
	RequestedBy                 string
	Types                       []model.TransactionType
	BlacklistTransactionType    []model.TransactionType
	SubTypes                    []model.TransactionSubType
	Statuses                    []model.TransactionStatus
	From                        time.Time
	To                          time.Time
	DriverRegion                model.RegionCode
	InfoType                    model.TransactionType
	OrderID                     string
	Actions                     []model.TransactionAction
	Amount                      float64
	IncentiveID                 string
	IncentivePaymentDate        string
	TransactionIds              []string
	InfoTransactionDateFrom     time.Time
	InfoTransactionDateTo       time.Time
	ExcludePendingTransactionId bool
	NextPageToken               primitive.ObjectID
}

func (q TransactionQuery) Query() (bson.M, error) {
	query := bson.M{}

	if q.ExcludePendingTransactionId {
		query["info.pending_transaction_id"] = bson.M{"$exists": false}
	}

	if q.DriverID != "" {
		query["info.driver_id"] = q.DriverID
	}

	if q.OrderID != "" {
		query["info.order_id"] = q.OrderID
	}

	if len(q.Actions) > 0 {
		query["action"] = bson.M{"$in": q.Actions}
	}

	if q.DriverRegion != "" {
		query["info.driver_region"] = q.DriverRegion
	}

	if q.TransRefId != "" {
		query["info.trans_ref_id"] = crypt.EncryptedString(q.TransRefId).DeterministicEq()
	}

	if q.WithdrawRefId != "" {
		query["info.withdraw_ref_id"] = crypt.EncryptedString(q.WithdrawRefId).DeterministicEq()
	}

	if q.Category != "" {
		query["info.category"] = q.Category
	}

	if q.WalletType != "" {
		query["info.wallet_type"] = q.WalletType
	}

	if q.RequestedBy != "" {
		query["info.requested_by"] = q.RequestedBy
	}

	if q.InfoType != "" {
		query["info.type"] = q.InfoType
	}

	if len(q.Types) > 0 {
		query["info.type"] = bson.M{"$in": q.Types}
	}

	if len(q.BlacklistTransactionType) > 0 {
		query["info.type"] = bson.M{"$nin": q.BlacklistTransactionType}
	}

	if len(q.SubTypes) > 0 {
		query["info.sub_type"] = bson.M{"$in": q.SubTypes}
	}

	if len(q.Statuses) > 0 {
		query["status"] = bson.M{"$in": q.Statuses}
	}

	if !q.From.IsZero() || !q.To.IsZero() {
		createAtQuery := bson.M{}
		if !q.From.IsZero() {
			createAtQuery["$gte"] = q.From
		}
		if !q.To.IsZero() {
			createAtQuery["$lte"] = q.To
		}

		query["created_at"] = createAtQuery
	}

	if !q.InfoTransactionDateFrom.IsZero() || !q.InfoTransactionDateTo.IsZero() {
		query["info.transaction_date"] = bson.M{}
		if !q.InfoTransactionDateFrom.IsZero() {
			query["info.transaction_date"].(bson.M)["$gte"] = q.InfoTransactionDateFrom
		}
		if !q.InfoTransactionDateTo.IsZero() {
			query["info.transaction_date"].(bson.M)["$lte"] = q.InfoTransactionDateTo
		}
	}

	if q.Amount > 0 {
		query["info.amount"] = q.Amount
	}

	if q.IncentiveID != "" {
		query["info.incentive_id"] = q.IncentiveID
	}

	if q.IncentivePaymentDate != "" {
		query["info.incentive_payment_date"] = q.IncentivePaymentDate
	}

	if len(q.TransactionIds) > 0 {
		query["transaction_id"] = bson.M{"$in": q.TransactionIds}
	}

	if !q.NextPageToken.IsZero() {
		query["_id"] = bson.M{"$lte": q.NextPageToken}
	}

	return repository.ValidateQuery(query)
}

type MultiTransactionQuery []TransactionQuery

func NewMultiTransactionQuery() MultiTransactionQuery {
	return make(MultiTransactionQuery, 0, 5)
}

func (mq MultiTransactionQuery) Query() (bson.M, error) {
	txnQueries := make([]bson.M, len(mq))
	for i, query := range mq {
		q, err := query.Query()
		if err != nil {
			return nil, err
		}
		txnQueries[i] = q
	}

	return bson.M{"$or": txnQueries}, nil
}

func (repo *DataStoreTransactionRepository) Create(ctx context.Context, transaction *model.Transaction) error {
	if err := repo.datastore.Insert(ctx, transaction); err != nil {
		return err
	}

	return nil
}

func (repo *DataStoreTransactionRepository) CreateAll(ctx context.Context, transaction []model.Transaction) error {
	castedTrans := make([]interface{}, len(transaction))
	for i, t := range transaction {
		castedTrans[i] = t
	}
	if err := repo.datastore.InsertMany(ctx, castedTrans); err != nil {
		return err
	}

	return nil
}

func (repo *DataStoreTransactionRepository) Find(
	ctx context.Context,
	query repository.Query,
	skip int,
	limit int,
	opts ...repository.Option,
) ([]model.Transaction, error) {
	return repo.FindWithSort(ctx, query, skip, limit, []string{"-created_at"}, opts...)
}

func (repo *DataStoreTransactionRepository) FindWithSort(
	ctx context.Context,
	query repository.Query,
	skip int,
	limit int,
	sort []string,
	opts ...repository.Option,
) (
	[]model.Transaction,
	error,
) {
	q, err := query.Query()
	if err != nil {
		return nil, err
	}

	transactions := make([]model.Transaction, 0, limit)
	err = repo.datastore.FindAndSort(ctx, q, skip, limit, sort, &transactions, repository.ToDBOptions(opts)...)
	if err != nil {
		return nil, err
	}
	return transactions, nil
}

func (repo *DataStoreTransactionRepository) FindByID(ctx context.Context, id string) (*model.Transaction, error) {
	transaction := &model.Transaction{}
	err := repo.datastore.FindOne(ctx, bson.M{"transaction_id": id}, transaction)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return transaction, nil
}

func (repo *DataStoreTransactionRepository) FindByIDs(ctx context.Context, ids []string) ([]model.Transaction, error) {
	transactions := make([]model.Transaction, 0, len(ids))
	err := repo.datastore.Find(ctx, bson.M{"transaction_id": bson.M{"$in": ids}}, 0, 0, &transactions)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return transactions, nil
}

func (repo *DataStoreTransactionRepository) FindByTransactionRefID(ctx context.Context, refID string) (*model.Transaction, error) {
	transaction := &model.Transaction{}
	err := repo.datastore.FindOne(ctx, bson.M{"info.trans_ref_id": crypt.EncryptedString(refID).DeterministicEqWithContext(ctx)}, transaction)
	if err != nil {
		return nil, err
	}

	return transaction, nil
}

func (repo *DataStoreTransactionRepository) FindByTransactionVanIDAndDate(ctx context.Context, vanID string, date time.Time) (*model.Transaction, error) {
	start := timeutil.DateTruncate(date).In(timeutil.BangkokLocation())
	end := timeutil.DateCeiling(date).In(timeutil.BangkokLocation())

	transaction := &model.Transaction{}
	query := bson.M{
		"info.van_ref_id": vanID,
		"info.transaction_date_time": bson.M{
			"$gte": start,
			"$lte": end,
		},
	}
	err := repo.datastore.FindOne(ctx, query, transaction)
	if err != nil {
		return nil, err
	}

	return transaction, nil
}

func (repo *DataStoreTransactionRepository) FindLatestWithdrawTransactionByID(ctx context.Context, driverID string) (*model.Transaction, error) {
	transactions := []*model.Transaction{}
	query := bson.M{
		"info.driver_id": driverID,
		"info.category":  model.WalletTransactionCategory,
		"info.type":      model.WithdrawTransactionType,
		"status": bson.M{
			"$in": []model.TransactionStatus{model.SuccessTransactionStatus, model.RejectedTransactionStatus},
		},
	}
	err := repo.datastore.FindAndSort(ctx, query, 0, 1, []string{"-created_at"}, &transactions)
	if err != nil {
		return nil, err
	}
	if len(transactions) == 0 {
		return nil, repository.ErrNotFound
	}
	return transactions[0], nil
}

func (repo *DataStoreTransactionRepository) FindItemFeeByOrderID(ctx context.Context, orderID string) (*model.Transaction, error) {
	transactions := make([]*model.Transaction, 0)
	query := bson.M{
		"info.order_id": orderID,
		"info.category": model.WalletTransactionCategory,
		"info.type":     model.ItemFeeTransactionSubType,
	}
	err := repo.datastore.FindAndSort(ctx, query, 0, 1, []string{"-created_at"}, &transactions)
	if err != nil {
		return nil, err
	}
	if len(transactions) == 0 {
		return nil, repository.ErrNotFound
	}
	return transactions[0], nil
}

func (repo *DataStoreTransactionRepository) FindOnTopsByOrderID(ctx context.Context, orderID string) ([]model.Transaction, error) {
	transactions := make([]model.Transaction, 0)
	query := bson.M{
		"info.order_id": orderID,
		"info.category": model.WalletTransactionCategory,
		"info.type":     model.OnTopTransactionType,
	}
	err := repo.datastore.FindAndSort(ctx, query, 0, 0, []string{"-created_at"}, &transactions)
	if err != nil {
		return nil, err
	}
	return transactions, nil
}

func (repo *DataStoreTransactionRepository) FindWithdrawByID(
	ctx context.Context,
	id string,
	from time.Time,
	to time.Time,
) ([]model.Transaction, error) {
	var transactions []model.Transaction
	err := repo.datastore.Find(ctx, bson.M{
		"info.driver_id": id,
		"info.type":      model.WithdrawTransactionType,
		"created_at":     bson.M{"$gt": from, "$lt": to},
	}, 0, 0, &transactions)

	return transactions, err
}

func (repo *DataStoreTransactionRepository) FindWalletWithdraw(ctx context.Context, options ...repository.FindWalletWithdrawOptionFunc) ([]model.Transaction, error) {
	query := bson.M{
		"info.category": model.WalletTransactionCategory,
		"info.type":     model.WithdrawTransactionType,
		"status":        model.ProcessingTransactionStatus,
	}

	option := repository.FindWalletWithdrawOption{AnyWithdrawRefID: false}
	for _, opt := range options {
		opt(&option)
	}

	if !option.AnyWithdrawRefID {
		query["info.withdraw_ref_id"] = bson.M{"$exists": false, "$ne": ""}
	}

	totalCount := option.Limit
	if totalCount == 0 {
		err := repo.datastore.Count(ctx, query, &totalCount)
		if err != nil {
			return nil, err
		}
	}

	withdrawTxns := make([]model.Transaction, 0, totalCount)

	err := repo.datastore.FindAndSort(ctx, query, 0, totalCount, []string{"created_at", "_id"}, &withdrawTxns,
		repository.ToDBOptions(option.MongoOption)...)
	if err != nil {
		return nil, err
	}

	return withdrawTxns, nil
}

func (repo *DataStoreTransactionRepository) Count(ctx context.Context, query repository.Query) (int, error) {
	q, err := query.Query()
	if err != nil {
		return -1, err
	}

	var count int
	err = repo.datastore.Count(ctx, q, &count, mongodb.WithReadPreference(readpref.SecondaryPreferred()))
	if err != nil {
		return -1, err
	}

	return count, nil
}

func (repo *DataStoreTransactionRepository) Update(ctx context.Context, transaction *model.Transaction) error {
	transaction.SetUpdatedAt(time.Now())
	return repo.datastore.Replace(ctx, bson.M{"transaction_id": transaction.TransactionID}, transaction)
}

func (repo *DataStoreTransactionRepository) FindByWithdrawRefID(ctx context.Context, refIDs []crypt.EncryptedString) ([]model.Transaction, error) {
	ids := crypt.EncryptedStringArray(refIDs)
	query := bson.M{"info.withdraw_ref_id": bson.M{"$in": ids.AllPossibleDeterministicValue(ctx)}}

	txns := make([]model.Transaction, 0, len(refIDs))
	err := repo.datastore.Find(ctx, query, 0, 0, &txns)
	if err != nil {
		return nil, err
	}

	return txns, nil
}

func (repo *DataStoreTransactionRepository) DeleteByID(ctx context.Context, id string) error {
	return repo.datastore.Remove(ctx, bson.M{"transaction_id": id})
}

func (repo *DataStoreTransactionRepository) ApproveTransactionIDs(ctx context.Context, ids []string, requestedBy string) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	selector := bson.M{"transaction_id": bson.M{"$in": ids}}
	newAudit := bson.M{
		"action": model.AdminApproved,
		"time":   time.Now(),
		"by":     requestedBy,
	}
	operations := bson.M{
		"$set": bson.M{
			"status":     model.ProcessingTransactionStatus,
			"updated_at": time.Now(),
		},
		"$push": bson.M{"audit": newAudit},
	}
	info, err := repo.datastore.UpdateAll(ctx, selector, operations)
	if info != nil {
		failCount := len(ids) - int(info.ModifiedCount)
		return failCount, err
	}
	return 0, err
}

func (repo *DataStoreTransactionRepository) FindByOrderID(ctx context.Context, orderID string) ([]model.Transaction, error) {
	transactions := make([]model.Transaction, 0, 100)
	err := repo.datastore.Find(ctx, bson.M{"info.order_id": orderID}, 0, 0, &transactions)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return transactions, nil
}

func (repo *DataStoreTransactionRepository) FindByTripIDWithNoOrderID(ctx context.Context, tripID string) ([]model.Transaction, error) {
	transactions := make([]model.Transaction, 0, 100)
	err := repo.datastore.Find(ctx, bson.M{
		"info.trip_id": tripID,
		"$or": []bson.M{
			{"info.order_id": bson.M{"$exists": false}},
			{"info.order_id": ""},
		},
	}, 0, 0, &transactions)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return transactions, nil
}

func (repo *DataStoreTransactionRepository) FindLatestWage(ctx context.Context, tripID string) ([]model.Transaction, error) {
	transactions := make([]model.Transaction, 0, 1)

	q := bson.M{
		"info.category": "WALLET",
		"info.type":     "DRIVER_WAGE",
		"info.trip_id":  tripID,
	}

	err := repo.datastore.FindAndSort(ctx, q, 0, 1, []string{"-created_at"}, &transactions)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	if len(transactions) == 0 {
		return nil, repository.ErrNotFound
	}

	return transactions, nil
}

func (repo *DataStoreTransactionRepository) ApprovePendingFraudTransaction(ctx context.Context, requestedBy string, start, end time.Time, opts ...repository.Option) error {
	newAudit := bson.M{
		"action": model.AdminApproved,
		"time":   time.Now(),
		"by":     requestedBy,
	}
	_, err := repo.datastore.UpdateAll(ctx, bson.M{
		"status":           model.PendingTransactionStatus,
		"info.wallet_type": model.FraudTransactionWalletType,
		"info.category":    model.WalletTransactionCategory,
		"created_at":       bson.M{"$gte": start, "$lt": end},
	}, bson.M{
		"$set": bson.M{
			"status":     model.ProcessingTransactionStatus,
			"updated_at": time.Now(),
		},
		"$push": bson.M{"audit": newAudit},
	})
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}

	return nil
}

func (repo *DataStoreTransactionRepository) CountWithdrawal(ctx context.Context, driverId string, start, end time.Time, opts ...repository.Option) (int, error) {
	var c int
	q := bson.M{"$and": []bson.M{
		{
			"status": bson.M{"$in": []model.TransactionStatus{
				model.PendingTransactionStatus,
				model.SuccessTransactionStatus,
				model.ProcessingTransactionStatus,
				model.RejectedTransactionStatus,
			}},
		},
		{"action": model.WithdrawTransactionAction},
		{"info.driver_id": driverId},
		{"info.category": model.WalletTransactionCategory},
		{"info.type": model.WithdrawTransactionType},
		{"created_at": bson.M{"$gt": start, "$lt": end}},
	}}
	err := repo.datastore.Count(ctx, q, &c, repository.ToDBOptions(opts)...)

	return c, err
}

func (repo *DataStoreTransactionRepository) UnsetWithdrawRefIdByWithdrawRefId(ctx context.Context, withdrawRefId string) error {
	selector := bson.M{"info.withdraw_ref_id": crypt.EncryptedString(withdrawRefId).DeterministicEqWithContext(ctx)}

	operations := bson.M{
		"$unset": bson.M{
			"info.withdraw_ref_id": "",
		},
		"$set": bson.M{
			"updated_at": time.Now(),
		},
	}

	err := repo.datastore.Update(ctx, selector, operations)
	if err != nil {
		return err
	}
	return nil
}

func (repo *DataStoreTransactionRepository) FindListActiveDriverIdsInRange(ctx context.Context, fromInclusive time.Time, toExclusive time.Time, opts ...repository.Option) ([]string, error) {
	matchStage := bson.M{
		"$match": bson.M{
			"info.transaction_date": bson.M{
				"$gte": fromInclusive,
				"$lt":  toExclusive,
			},
		},
	}

	groupStage := bson.M{
		"$group": bson.M{
			"_id": "$info.driver_id",
		},
	}

	pipelines := []bson.M{matchStage, groupStage}

	type response struct {
		DriverID string `bson:"_id"`
	}

	var responses []response
	if err := repo.datastore.Aggregate(ctx, pipelines, &responses); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	driverIds := make([]string, len(responses))
	for idx, v := range responses {
		driverIds[idx] = v.DriverID
	}

	return driverIds, nil
}

func (repo *DataStoreTransactionRepository) FindMissingCITITransaction(ctx context.Context, trxIDs []string, startTime, endTime time.Time) ([]string, error) {
	var resp []map[string][]string
	matchStage := bson.M{
		"$match": bson.M{
			"status": bson.M{
				"$in": []model.TransactionStatus{model.SuccessTransactionStatus, model.UnmatchedTransactionStatus},
			},
			"action":      model.CreditTopUpTransactionAction,
			"info.source": model.TransactionSourceCITI,
			"info.bank_ref_id": bson.M{
				"$in": trxIDs,
			},
			"info.transaction_date_time": bson.M{
				"$gte": startTime,
				"$lte": endTime,
			},
		},
	}

	groupStage := bson.M{
		"$group": bson.M{
			"_id": nil,
			"values": bson.M{
				"$addToSet": "$info.bank_ref_id",
			},
		},
	}

	projectStage := bson.M{
		"$project": bson.M{
			"_id": 0,
			"missing": bson.M{
				"$setDifference": bson.A{trxIDs, "$values"},
			},
		},
	}

	pipelines := []bson.M{matchStage, groupStage, projectStage}

	if err := repo.datastore.Aggregate(ctx, pipelines, &resp); err != nil {
		return nil, err
	}

	if len(resp) == 0 {
		return []string{}, nil
	}

	if v, ok := resp[0]["missing"]; ok {
		return v, nil
	}

	return []string{}, nil
}

func (repo *DataStoreTransactionRepository) CreateAllTransactionTracking(ctx context.Context, transaction []model.Transaction) error {
	castedTrans := make([]interface{}, len(transaction))
	for i, t := range transaction {
		castedTrans[i] = t
	}
	if err := repo.datastore.transactionTrackingDataStore.InsertMany(ctx, castedTrans); err != nil {
		logx.Error().Err(err).Msg("Failed to create all transaction tracking data")

		return err
	}

	return nil
}

func (repo *DataStoreTransactionRepository) IterateCitiTopupTransactionsCreatedBetween(ctx context.Context, fromInclusive time.Time, toInclusive time.Time) (repository.Cursor, error) {
	query := bson.M{
		"action": model.CreditTopUpTransactionAction,
		"status": model.SuccessTransactionStatus,
		"created_at": bson.M{
			"$gte": fromInclusive,
			"$lte": toInclusive,
		},
		"info.source": model.TransactionSourceCITI,
	}
	return repo.datastore.collection().Find(ctx, query)
}

func (repo *DataStoreTransactionRepository) UpdateAll(ctx context.Context, txns []model.Transaction) error {
	if len(txns) == 0 {
		return nil
	}

	var models []mongo.WriteModel
	for _, txn := range txns {
		selector := bson.M{"transaction_id": txn.TransactionID}
		replaceModel := mongo.NewReplaceOneModel().SetFilter(selector).SetReplacement(txn)
		models = append(models, replaceModel)
	}

	_, err := repo.datastore.BulkWrite(ctx, models, &options.BulkWriteOptions{})
	if err != nil {
		return err
	}

	return nil
}

func (repo *DataStoreTransactionRepository) IsIncentivePaid(ctx context.Context, incentiveID, incentivePaymentDate, driverID string) (bool, error) {
	var txn model.Transaction
	query := bson.M{
		"info.incentive_id":           incentiveID,
		"info.incentive_payment_date": incentivePaymentDate,
		"info.driver_id":              driverID,
	}
	if err := repo.datastore.FindOne(ctx, query, &txn); err != nil {
		if errors.Is(err, mongodb.ErrDataNotFound) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func ProvideDataStoreTransactionRepository(ds TransactionDataStore, meter metric.Meter) *repository.ProxyTransactionRepository {
	return repository.NewLatencyProxyTransactionRepository(&DataStoreTransactionRepository{datastore: ds}, meter)
}

type TransactionDataStore struct {
	mongodb.DataStoreInterface
	transactionTrackingDataStore mongodb.DataStoreInterface
	conn                         *mongodb.Conn
	name                         string
}

func (datastore TransactionDataStore) collection() *mongo.Collection {
	return datastore.conn.Database().Collection(datastore.name)
}

func ProvideTransactionDataStore(conn *mongodb.Conn) TransactionDataStore {
	name := "transactions"
	transTrackingCollectionName := "transactions_tracking"
	return TransactionDataStore{
		DataStoreInterface:           mongodb.NewDataStoreWithConn(conn, name),
		transactionTrackingDataStore: mongodb.NewDataStoreWithConn(conn, transTrackingCollectionName),
		conn:                         conn,
		name:                         name,
	}
}
