package persistence

import (
	"context"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"

	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type CookingTimeDelayRedisClient datastore.RedisClient

const (
	cookingTimeDelayKey = "cooking_time_delay"
)

type CookingTimeDelayRepository struct {
	redisClient CookingTimeDelayRedisClient
}

func (ct *CookingTimeDelayRepository) AddOrder(ctx context.Context, orderID string, t time.Time) error {
	return ct.redisClient.ZAdd(ctx, cookingTimeDelayKey, redis.Z{
		Score:  float64(t.Unix()),
		Member: orderID,
	}).Err()
}

func (ct *CookingTimeDelayRepository) RemoveOrder(ctx context.Context, orderID string) error {
	return ct.redisClient.ZRem(ctx, cookingTimeDelayKey, orderID).Err()
}

func (ct *CookingTimeDelayRepository) GetDelayedOrders(ctx context.Context) ([]string, error) {
	now := time.Now().Unix()
	ret, err := ct.redisClient.ZRangeByScore(ctx, cookingTimeDelayKey, &redis.ZRangeBy{
		Min: strconv.Itoa(int(now) - (30 * 60)), // t-30min lower bound in case of huge backlog (older orders are probably obsolete, safe to delete)
		Max: strconv.Itoa(int(now)),
	}).Result()
	if err != nil {
		return []string{}, err
	}
	if ct.redisClient.ZRemRangeByScore(ctx, cookingTimeDelayKey, "-inf", strconv.Itoa(int(now))).Err() != nil {
		safe.SentryError(err)
	}
	return ret, nil
}

func ProvideCookingTimeDelayRepository(redisClient datastore.RedisClient, meter metric.Meter) repository.CookingTimeDelayRepository {
	return repository.NewLatencyProxyCookingTimeDelayRepository(&CookingTimeDelayRepository{
		redisClient: redisClient,
	}, meter)
}
