package persistence

import (
	"testing"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

func Test_toCondition(t *testing.T) {
	trueValue := true
	falseValue := false

	tcs := []struct {
		name              string
		query             repository.ProvinceQuery
		expectedCondition bson.M
	}{
		{
			name:              "all",
			query:             repository.ProvinceQuery{},
			expectedCondition: bson.M{},
		},
		{
			name:              "is_whitelist_auto_approve=true",
			query:             repository.ProvinceQuery{IsWhitelistAutoApprove: &trueValue},
			expectedCondition: bson.M{"is_whitelist_auto_approve": true},
		},
		{
			name:              "is_whitelist_auto_approve=false",
			query:             repository.ProvinceQuery{IsWhitelistAutoApprove: &falseValue},
			expectedCondition: bson.M{"is_whitelist_auto_approve": false},
		},
	}
	for _, tc := range tcs {
		t.Run(tc.name, func(t *testing.T) {
			require.Equal(t, tc.expectedCondition, toCondition(tc.query))
		})
	}
}
