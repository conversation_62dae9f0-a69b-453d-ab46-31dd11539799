package persistence

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/kelseyhightower/envconfig"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
	"github.com/twpayne/go-geom"
	"github.com/twpayne/go-geom/xy"

	"git.wndv.co/go/logx/v2"
	lmredis "git.wndv.co/go/redis"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

var (
	ErrNoneModified = errors.New("modified none")
)

type LocationRedisClient redis.UniversalClient

// RedisDriverLocationRepository Redis-backed driver location repository
type RedisDriverLocationRepository struct {
	redisClient LocationRedisClient
	mapService  mapservice.MapService
	atomCfg     *AtomicDriverLocationConfig
	bgBatch     *BackgroundBatch
}

func ProvideLocationRedisClient(cfg config.LocationRedisConfig) (LocationRedisClient, func()) {
	lmRedisConfig := cfg.ToLmRedisConfig()
	con, err := lmredis.NewConnection(&lmRedisConfig)
	if err != nil {
		logx.Error().Err(err).Msg("redis new connection location redis client error")
		panic(err)
	}
	if err != nil {
		logx.Error().Err(err).Msg("Cannot connect to location redis client")
		panic(err)
	}
	client := con.Client()
	if _, err := client.Ping(context.Background()).Result(); err != nil {
		logrus.Errorf("Cannot connect to location redis client: %v", err)
		panic(err)
	}

	return client, func() { client.Close() }
}

// @@wire-set-name@@ name:"Main"
// @@no-locator-generation@@
func ProvideDriverLocationRepository(redisClient LocationRedisClient, configurableCfg *AtomicDriverLocationConfig, mapService mapservice.MapService, meter metric.Meter) (*repository.ProxyDriverLocationRepository, func()) {
	return newDriverLocationRepository(redisClient, configurableCfg, mapService, meter, 100, time.Second)
}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideDriverLocationRepositoryForTest(redisClient LocationRedisClient, configurableCfg *AtomicDriverLocationConfig, mapService mapservice.MapService, meter metric.Meter) (*repository.ProxyDriverLocationRepository, func()) {
	return newDriverLocationRepository(redisClient, configurableCfg, mapService, meter, 1, time.Millisecond)
}

func newDriverLocationRepository(redisClient LocationRedisClient, configurableCfg *AtomicDriverLocationConfig, mapService mapservice.MapService, meter metric.Meter, batchSize int, bgDuration time.Duration) (*repository.ProxyDriverLocationRepository, func()) {
	bgBatch := NewBackgroundBatch(10000, batchSize, bgDuration)
	bgBatch.RegisterCallback(saveLocationToRedis(redisClient, configurableCfg))
	bgBatch.Start()

	cleanupFn := func() {
		bgBatch.Stop()
	}

	repo := &RedisDriverLocationRepository{
		redisClient: redisClient,
		bgBatch:     bgBatch,
		mapService:  mapService,
		atomCfg:     configurableCfg,
	}
	return repository.NewLatencyProxyDriverLocationRepository(repo, meter), cleanupFn
}

const (
	lyftLocationKeyPrefix     = "lyft_driver_location:"
	lyftGeoIndexKeyPrefix     = "lyft_geo_index_ids:"
	lyftBikeGeoIndexKeyPrefix = "lyft_bike_geo_index_ids:"
)

// UpdateDriverLocation update driver location
func (r *RedisDriverLocationRepository) UpdateDriverLocation(_ context.Context, req repository.UpdateDriverLocationRequest) error {
	ok := r.bgBatch.Add(req)
	if !ok {
		return fmt.Errorf("Failed to update driver location in background")
	}

	return nil
}

func saveLocationToRedis(redisClient datastore.RedisClient, configurableCfg *AtomicDriverLocationConfig) func(o []interface{}) {
	return func(o []interface{}) {
		ctx := context.Background()
		cfg := configurableCfg.Get()
		collector, m := metric.GetCollector(), metric.NewAPILatency("update_location_batch")
		defer collector.Save(m)

		nowSecond := time.Now().Unix()
		lyftMap := map[string][]redis.Z{}
		bikeLyftMap := map[string][]redis.Z{}
		pipe := redisClient.Pipeline()
		for _, i := range o {
			if req, ok := i.(repository.UpdateDriverLocationRequest); ok {
				if !req.ShouldUpdateLocationOnly {
					lyftHash := EncodeLatLngStr(req.Location.Lat, req.Location.Lng, cfg.LyftIndexResolution)
					lyftMap[lyftHash] = append(lyftMap[lyftHash], redis.Z{
						Score:  float64(nowSecond),
						Member: req.DriverID,
					})

					if req.IsBikeDriver && cfg.BikeLyftIndexEnable {
						bikeLyftMap[lyftHash] = append(bikeLyftMap[lyftHash], redis.Z{
							Score:  float64(nowSecond),
							Member: req.DriverID,
						})
					}
				}
				locBytes, err := json.Marshal(req.Location)
				if err != nil {
					safe.SentryError(err, safe.WithDriverID(req.DriverID))
					continue
				}
				pipe.Set(ctx, lyftLocationKeyPrefix+req.DriverID, string(locBytes), 0)
			}
		}
		if _, err := pipe.Exec(ctx); err != nil {
			safe.SentryError(err)
		}
		m.Lap("transform_model")

		timeout, cancel := context.WithTimeout(ctx, cfg.UpdatedTimeout)
		defer cancel()

		for hash, values := range lyftMap {
			select {
			case <-timeout.Done():
				safe.SentryErrorMessage("cannot update driver location within timeout - 30s")
				return
			default:
				redisClient.ZAdd(ctx, lyftGeoIndexKeyPrefix+hash, values...)
				redisClient.ZRemRangeByScore(ctx, lyftGeoIndexKeyPrefix+hash, "-inf", strconv.Itoa(int(nowSecond)-int(cfg.LyftIndexTTL.Seconds())))
				redisClient.ZRemRangeByScore(ctx, lyftBikeGeoIndexKeyPrefix+hash, "-inf", strconv.Itoa(int(nowSecond)-int(cfg.LyftIndexTTL.Seconds())))
			}
		}
		m.Lap("geoadd_hash")

		if len(bikeLyftMap) > 0 && cfg.BikeLyftIndexEnable {
			for hash, values := range bikeLyftMap {
				select {
				case <-timeout.Done():
					safe.SentryErrorMessage("cannot update driver location within timeout - 30s")
					return
				default:
					redisClient.ZAdd(ctx, lyftBikeGeoIndexKeyPrefix+hash, values...)
				}
			}
		}
		m.Lap("geoadd_bike_hash")
	}
}

// GetDrivers get drivers in specific radius.
func (r *RedisDriverLocationRepository) GetDrivers(ctx context.Context, query repository.DriverLocationQuery) ([]model.DriverLocation, error) {
	collector, m := metric.GetCollector(), metric.NewAPILatency("driver_location_repo_get_drivers")
	defer collector.Save(m)

	driverLocations := []model.DriverLocation{}
	cfg := r.atomCfg.Get()
	hashes := listSortedHashesInRadius(query.Location.Lat, query.Location.Lng, cfg.LyftIndexResolution, query.To/1000)
	m.Lap("list_sorted_hashes_in_radius")

	ids := types.NewStringSet()
	for _, ring := range hashes {
		for _, h := range ring {
			keyPrefix := lyftGeoIndexKeyPrefix
			if query.IsBikeService && cfg.BikeLyftIndexEnable {
				keyPrefix = lyftBikeGeoIndexKeyPrefix
			}
			tmp, err := r.redisClient.ZRangeByScore(ctx, keyPrefix+h, &redis.ZRangeBy{
				Min: strconv.Itoa(int(time.Now().Unix()) - int(cfg.LyftIndexTTL.Seconds())),
				Max: "+inf",
			}).Result()
			if err != nil {
				return nil, err
			}
			ids.Add(tmp...)
		}
		if ids.Count() > query.Limit {
			break
		}
	}
	m.Lap("z_range_by_score")

	result := r.GetDriversByIDs(ctx, ids.GetElements())
	if query.RemoveIDs != nil {
		for _, d := range result {
			if query.RemoveIDs.Has(d.DriverID) {
				continue
			}
			driverLocations = append(driverLocations, d)
		}
	} else {
		driverLocations = result
	}

	m.Lap("get_drivers_by_ids")

	logFields := logrus.Fields{
		"query":      query,
		"num_driver": len(driverLocations),
		"useOSRM":    cfg.MapDistanceEnabled,
	}

	if len(driverLocations) == 0 {
		logrus.WithFields(logFields).Info("no drivers found")
		return driverLocations, nil
	}

	if cfg.MapDistanceEnabled {
		attempt := 0
		for {
			attempt += 1
			err := r.updateDistanceByMap(ctx, driverLocations, query.Location)

			// if there is no error on OSRM calling, continue as normal
			if err == nil {
				break
			}

			logFields["map_distance_error"] = err

			// if the attempt is not reach the maximum attempt, retry update distances
			if attempt < cfg.MapDistanceMaxAttempt {
				time.Sleep(cfg.MapDistanceRetryDelay)
				continue
			}

			logrus.WithFields(logFields).Error("get drivers failed: update distances error")
			return nil, err
		}
		m.Lap("update_distance_by_map")
	}

	filteredLocations := filterLocationsByDistance(driverLocations, query.From, query.To)

	logFields["driver_distances"] = driverLocations
	logFields["filtered_driver_distances"] = filteredLocations
	logrus.WithFields(logFields).Info("get drivers success")

	return filteredLocations, nil

}

// GetDriversInMultiPolygon get drivers in specific polygon.
func (r *RedisDriverLocationRepository) GetDriversInMultiPolygon(ctx context.Context, query repository.DriverLocationInMultiPolygonQuery) ([]model.DriverLocation, error) {
	if len(query.Coordinates) == 0 {
		return nil, errors.New("can't get drivers with empty polygon")
	}
	cfg := r.atomCfg.Get()

	multiPolygon, err := geom.NewMultiPolygon(geom.XY).SetCoords(query.Coordinates)
	if err != nil {
		logrus.Errorf("GetDrivers can not parse multi polygon, error: %v", err)
		return nil, err
	}

	hashes := types.NewStringSet()
	for i := 0; i < multiPolygon.NumPolygons(); i++ {
		polygon := multiPolygon.Polygon(i)
		hashes.Add(listHashesByPolygon(polygon, cfg.LyftIndexResolution, cfg.LyftIndexChildResolution)...)
	}

	if hashes.Count() == 0 {
		logrus.WithField("query", query).Warnf("GetDriversInMultiPolygon: found empty hashes")
	}

	ids := types.NewStringSet()
	for _, h := range hashes.GetElements() {
		keyPrefix := lyftGeoIndexKeyPrefix
		if query.IsBikeService && cfg.BikeLyftIndexEnable {
			keyPrefix = lyftBikeGeoIndexKeyPrefix
		}
		tmp, err := r.redisClient.ZRangeByScore(ctx, keyPrefix+h, &redis.ZRangeBy{
			Min: strconv.Itoa(int(time.Now().Unix()) - int(cfg.LyftIndexTTL.Seconds())),
			Max: "+inf",
		}).Result()
		if err != nil {
			return nil, err
		}
		ids.Add(tmp...)
	}

	driverLocations := r.GetDriversByIDs(ctx, ids.GetElements())
	logFields := logrus.Fields{
		"query":      query,
		"num_driver": len(driverLocations),
		"useOSRM":    false,
	}

	filteredInPolygon := make([]model.DriverLocation, 0)
	for _, d := range driverLocations {
		point, err := geom.NewPoint(geom.XY).SetCoords(geom.Coord{d.Location.Lng, d.Location.Lat})
		if err != nil {
			safe.SentryErrorMessage("can't parse driver location to geo point", safe.WithDriverID(d.DriverID))
			continue
		}
		if multiPolygonContainsPoint(multiPolygon, point) {
			filteredInPolygon = append(filteredInPolygon, d)
			continue
		}
	}

	logFields["driver_distances"] = driverLocations
	logFields["filtered_driver_distances"] = filteredInPolygon
	logrus.WithFields(logFields).Info("get drivers success")

	return filteredInPolygon, nil

}

func multiPolygonContainsPoint(multiPolygon *geom.MultiPolygon, p *geom.Point) bool {
	for i := 0; i < multiPolygon.NumPolygons(); i++ {
		polygon := multiPolygon.Polygon(i)
		if polygonContainPoint(polygon, p) {
			return true
		}
	}
	return false
}

func polygonContainPoint(polygon *geom.Polygon, p *geom.Point) bool {
	exterior := polygon.LinearRing(0)
	if !xy.IsPointInRing(geom.XY, p.Coords(), exterior.FlatCoords()) {
		return false
	}

	num := polygon.NumLinearRings()
	for i := 1; i < num; i++ {
		interior := polygon.LinearRing(i)
		if xy.IsPointInRing(geom.XY, p.Coords(), interior.FlatCoords()) {
			return false
		}
	}

	return true
}

func (r *RedisDriverLocationRepository) GetDriversByIDs(ctx context.Context, driverIDs []string) []model.DriverLocation {
	driverLocations := []model.DriverLocation{}
	results := make(map[string]*redis.StringCmd)
	pipe := r.redisClient.Pipeline()
	for _, id := range driverIDs {
		results[id] = pipe.Get(ctx, lyftLocationKeyPrefix+id)
	}
	if _, err := pipe.Exec(ctx); err != nil && err != redis.Nil {
		sentry.CaptureException(err)
	}
	for id, result := range results {
		locJSON, err := result.Result()
		if err != nil {
			if err == redis.Nil {
				safe.SentryErrorMessage("found driverID in geoindex but actual location is missing.", safe.WithDriverID(id))
				continue
			}
			logrus.Errorf("trying to find location on redis failed. key: %s", lyftLocationKeyPrefix+id)
			continue
		}
		loc := model.Location{}
		if err := json.Unmarshal([]byte(locJSON), &loc); err != nil {
			safe.SentryError(err, safe.WithDriverID(id))
			continue
		}
		driverLocations = append(driverLocations, model.DriverLocation{
			DriverID: id,
			Location: loc,
		})
	}
	return driverLocations
}

// GetDriverLocation get drivers in specific radius.
func (r *RedisDriverLocationRepository) GetDriverLocation(ctx context.Context, driverID string) (resp *model.DriverLocation, err error) {
	locJSON, err := r.redisClient.Get(ctx, lyftLocationKeyPrefix+driverID).Result()
	if err != nil {
		logrus.Errorf("RedisDriverLocationRepository.GetDriverLocation: %v", err)
		return nil, mongodb.ErrDataNotFound
	}
	loc := model.Location{}
	if err := json.Unmarshal([]byte(locJSON), &loc); err != nil {
		safe.SentryError(err, safe.WithDriverID(driverID))
		return nil, err
	}

	return &model.DriverLocation{
		DriverID: driverID,
		Location: loc,
	}, nil
}

func (r *RedisDriverLocationRepository) GetDriverLocationWithUpdatedAt(ctx context.Context, driverID string) (resp *model.LocationWithUpdatedAt, err error) {
	locJSON, err := r.redisClient.Get(ctx, lyftLocationKeyPrefix+driverID).Result()
	if err != nil {
		logrus.Errorf("RedisDriverLocationRepository.GetDriverLocation: %v", err)
		return nil, mongodb.ErrDataNotFound
	}
	loc := model.LocationWithUpdatedAt{}
	if err := json.Unmarshal([]byte(locJSON), &loc); err != nil {
		safe.SentryError(err, safe.WithDriverID(driverID))
		return nil, err
	}

	return &loc, nil
}

func (r *RedisDriverLocationRepository) updateDistanceByMap(ctx context.Context, driverLocs []model.DriverLocation, restaurantLocation model.Location) error {
	sources := make([]mapservice.Location, len(driverLocs))
	for i, locs := range driverLocs {
		sources[i] = mapservice.Location(locs.Location)
	}
	tableResult, err := r.mapService.FindDistances(ctx, sources, mapservice.Location(restaurantLocation))
	if err != nil {
		return err
	}
	if tableResult.Distances == nil || len(tableResult.Distances) == 0 {
		return nil
	}

	for i, distance := range tableResult.Distances {
		driverLocs[i].DistanceMeter = distance
	}

	return nil
}

// filterLocationsByDistance filter driver that out of `from` and `to`
func filterLocationsByDistance(locs []model.DriverLocation, from, to float64) []model.DriverLocation {
	inRangeLocations := make([]model.DriverLocation, 0, len(locs))
	for _, v := range locs {
		if v.DistanceMeter >= from && v.DistanceMeter <= to {
			inRangeLocations = append(inRangeLocations, v)
		}
	}
	return inRangeLocations
}

type DriverLocationConfig struct {
	MapDistanceEnabled       bool          `envconfig:"DRIVER_LOCATION_REPOSITORY_MAP_DISTANCE_ENABLED"`
	MapDistanceMaxAttempt    int           `envconfig:"DRIVER_LOCATION_REPOSITORY_MAP_DISTANCE_MAX_RETRY" default:"2"`
	MapDistanceRetryDelay    time.Duration `envconfig:"DRIVER_LOCATION_REPOSITORY_MAP_DISTANCE_RETRY_DELAY" default:"1s"`
	LyftIndexResolution      int           `envconfig:"LYFT_INDEX_RESOLUTION" default:"6"`
	LyftIndexChildResolution int           `envconfig:"LYFT_INDEX_CHILD_RESOLUTION" default:"9"`
	LyftIndexTTL             time.Duration `envconfig:"LYFT_INDEX_TTL" default:"60s"`
	UpdatedTimeout           time.Duration `envconfig:"DRIVER_LOCATION_UPDATED_TIMEOUT" default:"30s"`

	BikeLyftIndexEnable bool `envconfig:"BIKE_LYFT_INDEX_ENABLE" default:"true"`
}

type AtomicDriverLocationConfig struct {
	lock   sync.RWMutex
	config DriverLocationConfig
}

func (adlc *AtomicDriverLocationConfig) Parse() {
	adlc.lock.Lock()
	defer adlc.lock.Unlock()
	envconfig.MustProcess("", &adlc.config)
}

func (adlc *AtomicDriverLocationConfig) Get() DriverLocationConfig {
	adlc.lock.RLock()
	defer adlc.lock.RUnlock()
	return adlc.config
}

func (adlc *AtomicDriverLocationConfig) EnabledMapDistance() {
	adlc.lock.RLock()
	defer adlc.lock.RUnlock()
	adlc.config.MapDistanceEnabled = true
}

// @@wire-set-name@@ name:"MainConfigSet"
func ProvideDriverLocationConfig(configUpdater *config.DBConfigUpdater) *AtomicDriverLocationConfig {
	var cfg AtomicDriverLocationConfig

	configUpdater.Register(&cfg)
	return &cfg
}

func ProvideDriverLocationTestConfig(configUpdater *config.DBConfigUpdater) *AtomicDriverLocationConfig {
	var cfg AtomicDriverLocationConfig

	configUpdater.Register(&cfg)

	return &cfg
}
