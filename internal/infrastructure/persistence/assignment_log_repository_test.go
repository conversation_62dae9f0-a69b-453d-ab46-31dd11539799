package persistence

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/mock_mongodb"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/repositorytestutil"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric/testmetric"
)

func TestDriverAssignmentLog_AssignToDrivers(t *testing.T) {
	const orderID = "LMF-12312415"
	drivers := []repository.DriverDistance{
		{DriverID: "<driver_id_1>", Distance: 1234},
		{DriverID: "<driver_id_2>", Distance: 2345},
	}

	t.Run("insert assignment log when no record found", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		datastore := mock_mongodb.NewMockDataStoreInterface(ctrl)
		meter := testmetric.NewStubMeter()

		datastore.EXPECT().
			FindOne(gomock.Any(), uniqueAssignmentLogKey(orderID, 0), gomock.Any(), repositorytestutil.NewDatabaseModeMatcher(readpref.Primary())).
			Return(mongodb.ErrDataNotFound)
		datastore.EXPECT().
			Insert(gomock.Any(), gomock.AssignableToTypeOf(model.AssignmentLogRecord{})).
			DoAndReturn(func(_ context.Context, aslog model.AssignmentLogRecord, opts ...repository.Option) error {
				records := aslog.Drivers
				require.Len(tt, records, 2)
				require.Equal(tt, drivers[0].DriverID, records[0].DriverID)
				require.Equal(tt, drivers[0].Distance, records[0].DistanceInMeter)
				require.False(tt, records[0].PushAt.IsZero())
				require.Equal(tt, drivers[1].DriverID, records[1].DriverID)
				require.Equal(tt, drivers[1].Distance, records[1].DistanceInMeter)
				require.False(tt, records[1].PushAt.IsZero())
				require.Equal(tt, "R1", records[0].Round)
				require.Equal(tt, "R1", records[1].Round)
				require.Equal(tt, true, records[0].IsPrioritized)
				require.Equal(tt, true, records[1].IsPrioritized)
				require.Equal(tt, true, records[0].AutoAssigned)
				require.Equal(tt, true, records[1].AutoAssigned)
				return nil
			})

		log := ProvideMongoAssignmentLogRepository(datastore, meter)
		_, err := log.AssignToDrivers(context.Background(), 1, orderID, 0, drivers, model.AssignmentLogOpt{IsPrioritized: true, AutoAssigned: true})
		require.NoError(tt, err)
	})

	t.Run("update existing record", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		datastore := mock_mongodb.NewMockDataStoreInterface(ctrl)
		meter := testmetric.NewStubMeter()

		datastore.EXPECT().
			FindOne(gomock.Any(), uniqueAssignmentLogKey(orderID, 0), gomock.Any(), repositorytestutil.NewDatabaseModeMatcher(readpref.Primary())).
			SetArg(2, model.AssignmentLogRecord{
				OrderID: orderID,
				Drivers: []model.Record{
					{
						DriverID:        drivers[0].DriverID,
						DistanceInMeter: drivers[0].Distance,
						PushAt:          time.Now().UTC(),
						Round:           "R0",
					},
				},
				CreatedAt: time.Now().UTC(),
				UpdatedAt: time.Now().UTC(),
			}).
			Return(nil)
		datastore.EXPECT().
			Replace(gomock.Any(), uniqueAssignmentLogKey(orderID, 0), gomock.AssignableToTypeOf(model.AssignmentLogRecord{})).
			DoAndReturn(func(_ context.Context, q bson.M, aslog model.AssignmentLogRecord, opts ...repository.Option) error {
				records := aslog.Drivers
				require.Len(tt, records, 2)
				require.Equal(tt, drivers[1].DriverID, records[1].DriverID)
				require.Equal(tt, drivers[1].Distance, records[1].DistanceInMeter)
				require.False(tt, records[1].PushAt.IsZero())
				require.Equal(tt, "R1", records[1].Round)
				require.NotEqual(tt, aslog.CreatedAt, aslog.UpdatedAt)
				require.Equal(tt, false, records[1].IsPrioritized)
				require.Equal(tt, false, records[1].AutoAssigned)
				return nil
			})

		log := ProvideMongoAssignmentLogRepository(datastore, meter)
		_, err := log.AssignToDrivers(context.Background(), 1, orderID, 0, []repository.DriverDistance{{DriverID: drivers[1].DriverID, Distance: drivers[1].Distance}})
		require.NoError(tt, err)
	})
}

func TestDriverAssignmentLog_UnassignToDrivers(t *testing.T) {
	const orderID = "LMF-12312415"
	drivers := []repository.DriverDistance{
		{DriverID: "<driver_id_1>", Distance: 1234},
		{DriverID: "<driver_id_2>", Distance: 2345},
	}

	t.Run("remove a driver from record (1 driver in record)", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		datastore := mock_mongodb.NewMockDataStoreInterface(ctrl)
		meter := testmetric.NewStubMeter()

		datastore.EXPECT().
			FindOne(gomock.Any(), uniqueAssignmentLogKey(orderID, 0), gomock.Any(), repositorytestutil.NewDatabaseModeMatcher(readpref.Primary())).
			SetArg(2, model.AssignmentLogRecord{
				OrderID: orderID,
				Drivers: []model.Record{
					{
						DriverID:        drivers[0].DriverID,
						DistanceInMeter: drivers[0].Distance,
						PushAt:          time.Now().UTC(),
						Round:           "R0",
					},
				},
				CreatedAt: time.Now().UTC(),
				UpdatedAt: time.Now().UTC(),
			}).
			Return(nil)
		datastore.EXPECT().
			Replace(gomock.Any(), uniqueAssignmentLogKey(orderID, 0), gomock.AssignableToTypeOf(model.AssignmentLogRecord{})).
			DoAndReturn(func(_ context.Context, q bson.M, aslog model.AssignmentLogRecord, opts ...repository.Option) error {
				records := aslog.Drivers
				require.Len(tt, records, 0)
				return nil
			})

		log := ProvideMongoAssignmentLogRepository(datastore, meter)
		err := log.UnassignToDriver(context.Background(), orderID, 0, drivers[0].DriverID)
		require.NoError(tt, err)
	})

	t.Run("remove the first driver from record (2 drivers in record)", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		datastore := mock_mongodb.NewMockDataStoreInterface(ctrl)
		meter := testmetric.NewStubMeter()

		datastore.EXPECT().
			FindOne(gomock.Any(), uniqueAssignmentLogKey(orderID, 0), gomock.Any(), repositorytestutil.NewDatabaseModeMatcher(readpref.Primary())).
			SetArg(2, model.AssignmentLogRecord{
				OrderID: orderID,
				Drivers: []model.Record{
					{
						DriverID:        drivers[0].DriverID,
						DistanceInMeter: drivers[0].Distance,
						PushAt:          time.Now().UTC(),
						Round:           "R0",
					}, {
						DriverID:        drivers[1].DriverID,
						DistanceInMeter: drivers[1].Distance,
						PushAt:          time.Now().UTC(),
						Round:           "R1",
					},
				},
				CreatedAt: time.Now().UTC(),
				UpdatedAt: time.Now().UTC(),
			}).Return(nil)
		datastore.EXPECT().
			Replace(gomock.Any(), uniqueAssignmentLogKey(orderID, 0), gomock.AssignableToTypeOf(model.AssignmentLogRecord{})).
			DoAndReturn(func(_ context.Context, q bson.M, aslog model.AssignmentLogRecord, opts ...repository.Option) error {
				records := aslog.Drivers
				require.Len(tt, records, 1)
				require.Equal(tt, drivers[1].DriverID, records[0].DriverID)
				require.Equal(tt, drivers[1].Distance, records[0].DistanceInMeter)
				require.False(tt, records[0].PushAt.IsZero())
				require.Equal(tt, "R1", records[0].Round)
				require.NotEqual(tt, aslog.CreatedAt, aslog.UpdatedAt)
				require.Equal(tt, false, records[0].IsPrioritized)
				require.Equal(tt, false, records[0].AutoAssigned)
				return nil
			})

		log := ProvideMongoAssignmentLogRepository(datastore, meter)
		err := log.UnassignToDriver(context.Background(), orderID, 0, drivers[0].DriverID)
		require.NoError(tt, err)
	})

	t.Run("error when assignment log record not found", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		datastore := mock_mongodb.NewMockDataStoreInterface(ctrl)
		meter := testmetric.NewStubMeter()

		datastore.EXPECT().
			FindOne(gomock.Any(), uniqueAssignmentLogKey(orderID, 0), gomock.Any(), repositorytestutil.NewDatabaseModeMatcher(readpref.Primary())).
			Return(mongodb.ErrDataNotFound)

		log := ProvideMongoAssignmentLogRepository(datastore, meter)
		err := log.UnassignToDriver(context.Background(), orderID, 0, drivers[0].DriverID)
		require.Error(tt, err)
	})
}

func TestDriverAssignmentLog_AssignedDrivers(t *testing.T) {
	const orderID = "LMF-12312415"
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	datastore := mock_mongodb.NewMockDataStoreInterface(ctrl)
	meter := testmetric.NewStubMeter()

	datastore.EXPECT().
		FindOne(gomock.Any(),
			bson.M{"order_id": orderID, "deleted_at": bson.M{"$exists": false}},
			gomock.Any(),
		).
		SetArg(2, model.AssignmentLogRecord{
			OrderID: orderID,
			Drivers: []model.Record{
				{
					DriverID: "<driver_id_1>",
					PushAt:   time.Now().UTC(),
				},
				{
					DriverID: "<driver_id_2>",
					PushAt:   time.Now().UTC(),
				},
			},
			CreatedAt: time.Now().UTC(),
			UpdatedAt: time.Now().UTC(),
		}).
		Return(nil)

	log := ProvideMongoAssignmentLogRepository(datastore, meter)
	records, err := log.AssignedDrivers(context.Background(), orderID)
	require.NoError(t, err)
	require.Len(t, records, 2)
}
