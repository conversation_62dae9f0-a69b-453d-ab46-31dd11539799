package persistence

import (
	"context"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

var _ repository.CounterRepository = (*CounterRepository)(nil)

type CounterRepository struct {
	ds CounterDataStore
}

func (cr *CounterRepository) GetNextSequence(ctx context.Context, cn model.CounterName) (int64, error) {
	u := bson.M{
		"$inc": bson.M{"seq": int64(1)},
	}
	f := bson.M{
		"_id": cn.ToString(),
	}
	updated, err := cr.ds.FindOneAndUpdate(ctx, f, u, mongodb.WithReturnDocument(options.After), mongodb.WithUpsert(true))
	if err != nil {
		logrus.Errorf("CounterRepository GetNextSequence FindOneAndUpdate  err: %v", err)
		return 0, err
	}
	var c model.Counter
	err = updated.Decode(&c)
	if err != nil {
		logrus.Errorf("CounterRepository GetNextSequence Decode err: %v", err)
		return 0, err
	}
	return c.Seq, nil
}

func (cr *CounterRepository) Create(ctx context.Context, c *model.Counter) error {
	err := cr.ds.Insert(ctx, c)
	if err != nil {
		logrus.Errorf("CounterRepository Create err: %v", err)
		return err
	}
	return nil
}

type CounterDataStore mongodb.DataStoreInterface

func ProvideCounterDataStore(conn *mongodb.Conn) CounterDataStore {
	return mongodb.NewDataStoreWithConn(conn, "counter")
}

func ProvideCounterRepository(datastore CounterDataStore, meter metric.Meter) *repository.ProxyCounterRepository {
	return repository.NewLatencyProxyCounterRepository(&CounterRepository{ds: datastore}, meter)
}
