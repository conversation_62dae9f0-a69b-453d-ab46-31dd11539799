package persistence

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/absinthe/database/v2/mock_mongodb"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore/mock_redis"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	cache2 "git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric/testmetric"
)

func TestDriverRepository_SetCurrentStatus(t *testing.T) {
	t.<PERSON>()

	const (
		driverID = "<driver_id_1>"
	)

	ctx := context.Background()

	driverrepo, cache, drivds, finish := newDriverRepositoryTest(t)
	defer finish()

	d1 := drivds.EXPECT().
		Update(gomock.Any(), bson.M{"driver_id": driverID, "status": bson.M{"$ne": model.StatusDeactivated}}, gomock.Any()).
		Return(nil)
	cache.EXPECT().
		Set(ctx, cache2.DriverStatusKey(driverID), string(model.StatusOffline), 0*time.Second).
		Return(redis.NewStatusResult("1", nil)).
		After(d1)

	err := driverrepo.SetCurrentStatus(context.Background(), driverID, model.StatusOffline)
	require.NoError(t, err)
}

func TestDriverRepository_CurrentStatus(t *testing.T) {
	t.Parallel()

	t.Run("get driver status and current order from cache", func(tt *testing.T) {
		tt.Parallel()

		const (
			driverID                 = "<driver_id_1>"
			expectedOrderID          = "LMF-123123145"
			expectedQueueingOrderID1 = "LMF-q1"
			expectedQueueingOrderID2 = "LMF-q2"
		)
		queueingOrderIDsJSON, _ := json.Marshal([]string{expectedQueueingOrderID1, expectedQueueingOrderID2})

		driverrepo, cache, _, finish := newDriverRepositoryTest(t)
		defer finish()

		ctx := context.Background()

		cache.EXPECT().
			Get(ctx, cache2.DriverStatusKey(driverID)).
			Return(redis.NewStringResult(string(model.StatusOffline), nil))
		cache.EXPECT().
			Get(ctx, cache2.DriverCurrentOrderKey(driverID)).
			Return(redis.NewStringResult(expectedOrderID, nil))
		cache.EXPECT().
			Get(ctx, cache2.DriverQueueingOrdersKey(driverID)).
			Return(redis.NewStringResult(string(queueingOrderIDsJSON), nil))
		cache.EXPECT().
			Get(ctx, cache2.DriverAllowQueueingKey(driverID)).
			Return(redis.NewStringResult("false", nil))

		state, err := driverrepo.CurrentStatus(context.Background(), driverID)
		require.NoError(tt, err)
		require.Equal(tt, model.StatusOffline, model.DriverStatus(state.Status))
		require.Equal(tt, []string{expectedQueueingOrderID1, expectedQueueingOrderID2}, state.QueueingOrders)
		require.Equal(tt, expectedOrderID, state.CurrentOrder)
	})

	t.Run("get driver status and current order from db if some of them have not in cache", func(tt *testing.T) {
		tt.Parallel()

		const (
			driverID                 = "<driver_id_1>"
			expectedOrderID          = "LMF-123123145"
			expectedQueueingOrderID1 = "LMF-queueingorder1"
			expectedQueueingOrderID2 = "LMF-queueingorder2"
		)

		driverrepo, cache, ds, finish := newDriverRepositoryTest(t)
		defer finish()

		ctx := context.Background()

		cache.EXPECT().
			Get(ctx, cache2.DriverStatusKey(driverID)).
			Return(redis.NewStringResult(string(model.StatusOffline), nil))
		cache.EXPECT().
			Get(ctx, cache2.DriverCurrentOrderKey(driverID)).
			Return(redis.NewStringResult("", nil))
		cache.EXPECT().
			Get(ctx, cache2.DriverQueueingOrdersKey(driverID)).
			Return(redis.NewStringResult("[]", nil))
		cache.EXPECT().
			Get(ctx, cache2.DriverAllowQueueingKey(driverID)).
			Return(redis.NewStringResult("", redis.Nil))

		ds.
			EXPECT().
			FindOneWithSelector(ctx, bson.M{"driver_id": driverID}, gomock.Any(), gomock.Any()).
			SetArg(3, repository.DriverCurrentState{
				Status:         string(model.StatusOffline),
				CurrentOrder:   expectedOrderID,
				QueueingOrders: []string{expectedQueueingOrderID1, expectedQueueingOrderID2},
				AllowQueueing:  true,
			})

		state, err := driverrepo.CurrentStatus(context.Background(), driverID)
		require.NoError(tt, err)
		require.Equal(tt, model.StatusOffline, model.DriverStatus(state.Status))
		require.Equal(tt, expectedOrderID, state.CurrentOrder)
		require.Equal(tt, []string{expectedQueueingOrderID1, expectedQueueingOrderID2}, state.QueueingOrders)
		require.Equal(tt, true, state.AllowQueueing)
	})
}

func TestDriverRepository_GetProfile(t *testing.T) {
	t.Parallel()

	t.Run("get driver driverrepo from db if cache has empty result", func(tt *testing.T) {
		tt.Parallel()

		const (
			driverID = "<driver_id_1>"
		)

		driverrepo, _, ds, finish := newDriverRepositoryTest(t)
		defer finish()

		var driverProfile model.Driver
		driverProfile.Firstname = crypt.NewLazyEncryptedString("Untie")
		driverProfile.Lastname = crypt.NewLazyEncryptedString("Taaal3")
		driverProfile.Phone = crypt.NewLazyEncryptedString("0987654321")

		ds.
			EXPECT().
			FindOne(gomock.Any(), bson.M{"driver_id": driverID}, gomock.Any()).
			SetArg(2, &driverProfile)

		profile, err := driverrepo.GetProfile(context.Background(), driverID)
		require.NoError(tt, err)
		require.Equal(tt, "Untie", profile.Firstname.String())
		require.Equal(tt, "Taaal3", profile.Lastname.String())
		require.Equal(tt, "0987654321", profile.Phone.String())
	})
}

func TestDriverRepository_SetProfile(t *testing.T) {
	t.Parallel()

	t.Run("set driver driverrepo db and cache", func(tt *testing.T) {
		tt.Parallel()

		const (
			driverID = "<driver_id_1>"
			orderID  = "<order_id_1>"
			orderID2 = "<order_id_2>"
			orderID3 = "<order_id_3>"
			tripID2  = "<trip_id_2>"
			tripID3  = "<trip_id_3>"
		)
		queueingOrdersJSON, _ := json.Marshal([]string{orderID2, orderID3})

		driverrepo, cache, ds, finish := newDriverRepositoryTest(tt)

		ctx := context.Background()

		cache.EXPECT().
			Set(ctx, cache2.DriverStatusKey(driverID), string(model.StatusOffline), 0*time.Second).
			Return(redis.NewStatusResult("1", nil))
		cache.EXPECT().
			Set(ctx, cache2.DriverCurrentOrderKey(driverID), orderID, 0*time.Second).
			Return(redis.NewStatusResult("1", nil))
		cache.EXPECT().
			Set(ctx, cache2.DriverQueueingOrdersKey(driverID), string(queueingOrdersJSON), 0*time.Second).
			Return(redis.NewStatusResult("1", nil))
		cache.EXPECT().
			Set(ctx, cache2.DriverAllowQueueingKey(driverID), false, 0*time.Second).
			Return(redis.NewStatusResult("1", nil))
		ds.EXPECT().Replace(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, q bson.M, driv *model.Driver, opts ...repository.Option) error {
			require.Equal(tt, driverID, driv.DriverID)
			return nil
		})

		err := driverrepo.SetProfile(context.Background(), &model.Driver{
			BaseDriver:     model.BaseDriver{},
			DriverID:       driverID,
			Status:         model.StatusOffline,
			CurrentOrder:   orderID,
			QueueingOrders: []string{orderID2, orderID3},
			QueueingTrips:  []string{tripID2, tripID3},
		}, nil)
		finish()
		require.NoError(tt, err)
	})
}

func TestProfileManagerImpl_GetProfilesByID(t *testing.T) {
	t.Parallel()

	driverIDs := make([]string, 102)
	drivers := make([]*model.Driver, len(driverIDs))
	for i := 0; i < len(driverIDs); i++ {
		seq := strconv.Itoa(i)
		driverId := "<driver_id" + seq + ">"
		driverIDs[i] = driverId
		d := model.Driver{}
		d.DriverID = driverId
		d.Firstname = crypt.NewLazyEncryptedString("fn_" + seq)
		d.Lastname = crypt.NewLazyEncryptedString("ln_" + seq)
		d.Phone = crypt.NewLazyEncryptedString("00" + seq)
		drivers[i] = &d
	}

	t.Run("get driver profile from db", func(tt *testing.T) {
		tt.Parallel()

		driverrepo, _, ds, finish := newDriverRepositoryTest(t)
		defer finish()

		ds.EXPECT().Find(gomock.Any(), bson.M{
			"driver_id": bson.M{"$in": driverIDs[:100]},
		},
			0,
			100, gomock.Any()).SetArg(4, drivers[1:90])
		ds.EXPECT().Find(gomock.Any(), bson.M{
			"driver_id": bson.M{"$in": driverIDs[100:102]},
		},
			0,
			2, gomock.Any()).SetArg(4, drivers[100:102])

		profilesByID, err := driverrepo.GetProfilesByID(context.Background(), driverIDs)
		require.NoError(tt, err)
		require.Nil(tt, profilesByID[driverIDs[0]])
		require.Nil(tt, profilesByID[driverIDs[91]])
		require.Nil(tt, profilesByID[driverIDs[99]])
		assertProfile(tt, driverIDs[1], drivers[1], profilesByID)
		assertProfile(tt, driverIDs[2], drivers[2], profilesByID)
		assertProfile(tt, driverIDs[100], drivers[100], profilesByID)
		assertProfile(tt, driverIDs[101], drivers[101], profilesByID)
	})
}

func assertProfile(t *testing.T, id string, driver *model.Driver, profilesByID map[string]*model.Driver) {
	p := profilesByID[id]

	require.Equal(t, *driver, *p)
}

func TestDriverRepository_GetTodayEarning(t *testing.T) {
	t.Run("should return earning", func(tt *testing.T) {
		driverrepo, cache, _, finish := newDriverRepositoryTest(tt)
		defer finish()

		ctx := context.Background()

		cache.EXPECT().
			HMGet(ctx, "driver_earning:driver-1", cache2.EARNING_FIELD_FEE, cache2.EARNING_FIELD_COMISSION, cache2.EARNING_FIELD_TAX, cache2.EARNING_FIELD_TIP).
			Return(redis.NewSliceResult([]interface{}{"40.0", "10.0", "2.0", "5.0"}, nil))

		earning, err := driverrepo.GetTodayEarning(ctx, "driver-1")

		require.Nil(tt, err)
		require.Equal(tt, "driver-1", earning.DriverID)
		require.Equal(tt, 40.0, earning.Fee.Float64())
		require.Equal(tt, 10.0, earning.Commission.Float64())
		require.Equal(tt, 2.0, earning.Tax.Float64())
		require.Equal(tt, 5.0, earning.Tip.Float64())
	})

	t.Run("should return 0 earning although key not found", func(tt *testing.T) {
		driverrepo, cache, _, finish := newDriverRepositoryTest(tt)
		defer finish()

		ctx := context.Background()

		cache.EXPECT().
			HMGet(ctx, "driver_earning:driver-1", cache2.EARNING_FIELD_FEE, cache2.EARNING_FIELD_COMISSION, cache2.EARNING_FIELD_TAX, cache2.EARNING_FIELD_TIP).
			Return(redis.NewSliceResult(nil, redis.Nil))

		earning, err := driverrepo.GetTodayEarning(ctx, "driver-1")

		require.Nil(tt, err)
		require.Equal(tt, "driver-1", earning.DriverID)
		require.Equal(tt, 0.0, earning.Earning().Float64())
	})
}

func TestDriverRepository_SetTodayEarning(t *testing.T) {
	t.Run("should set earning success", func(tt *testing.T) {
		driverrepo, cache, _, f1 := newDriverRepositoryTest(tt)
		defer f1()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		pipe := mock_redis.NewMockPipeliner(ctrl)

		ctx := context.Background()

		cache.EXPECT().
			Pipeline().
			Return(pipe)
		pipe.EXPECT().
			HMSet(ctx, "driver_earning:driver-1", map[string]interface{}{
				cache2.EARNING_FIELD_FEE:       10.0,
				cache2.EARNING_FIELD_COMISSION: 2.0,
				cache2.EARNING_FIELD_TAX:       1.0,
				cache2.EARNING_FIELD_TIP:       5.0,
			}).
			Return(redis.NewBoolResult(true, nil))
		pipe.EXPECT().
			Expire(ctx, "driver_earning:driver-1", gomock.Any()).
			Return(redis.NewBoolResult(true, nil))

		pipe.EXPECT().
			Exec(ctx).
			Return([]redis.Cmder{}, nil)

		err := driverrepo.SetTodayEarning(ctx, *model.NewDriverEarning("driver-1", 10.0, 2.0, 1.0, 5.0))

		require.Nil(tt, err)
	})

	t.Run("should error when cache return error", func(tt *testing.T) {
		driverrepo, cache, _, f1 := newDriverRepositoryTest(tt)
		defer f1()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		pipe := mock_redis.NewMockPipeliner(ctrl)

		ctx := context.Background()

		cache.EXPECT().
			Pipeline().
			Return(pipe)
		pipe.EXPECT().
			HMSet(ctx, "driver_earning:driver-1", map[string]interface{}{
				cache2.EARNING_FIELD_FEE:       10.0,
				cache2.EARNING_FIELD_COMISSION: 2.0,
				cache2.EARNING_FIELD_TAX:       1.0,
				cache2.EARNING_FIELD_TIP:       5.0,
			}).
			Return(redis.NewBoolResult(true, nil))
		pipe.EXPECT().
			Expire(ctx, "driver_earning:driver-1", gomock.Any()).
			Return(redis.NewBoolResult(true, nil))

		pipe.EXPECT().
			Exec(ctx).
			Return([]redis.Cmder{}, errors.New("error"))

		err := driverrepo.SetTodayEarning(ctx, *model.NewDriverEarning("driver-1", 10.0, 2.0, 1.0, 5.0))

		require.NotNil(tt, err)
	})
}

func TestMongoDriverRepository_getAtLog(t *testing.T) {
	testCases := []struct {
		status model.DriverStatus
		result model.AttendanceStatus
	}{
		{
			status: model.StatusDeactivated,
			result: model.AttendanceStatusOffline,
		},
		{
			status: model.StatusOffline,
			result: model.AttendanceStatusOffline,
		},
		{
			status: model.StatusBanned,
			result: model.AttendanceStatusOffline,
		},
		{
			status: model.StatusOnline,
			result: model.AttendanceStatusOnline,
		},
		{
			status: model.StatusAssigned,
			result: model.AttendanceStatusOnline,
		},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("driver status = %s, attendance status = %s", tc.status, tc.result), func(t *testing.T) {
			res := getAtLog(testmetric.NewStubMeter().GetCounter("test", "test"), tc.status)
			require.Equal(t, tc.result, res.Status)
		})
	}
}

func newDriverRepositoryTest(t gomock.TestReporter) (repository.DriverRepository, *mock_redis.MockUniversalClient, *mock_mongodb.MockDataStoreInterface, func()) {
	ctrl := gomock.NewController(t)
	cache := mock_redis.NewMockUniversalClient(ctrl)
	paymentConfig := config.AttendanceRateConfig{
		AttendanceRateEnabled: true,
	}
	meter := testmetric.NewStubMeter()
	drivds := mock_mongodb.NewMockDataStoreInterface(ctrl)
	return ProvideDriverRepository(drivds, cache, paymentConfig, meter), cache, drivds, func() {
		ctrl.Finish()
	}
}

func BenchmarkValidateDriverID(b *testing.B) {
	var drivers []string
	var validDrivers []string

	for i := 0; i < 100000; i++ {
		drivers = append(drivers, fmt.Sprintf("LMD-%d", i))
		validDrivers = append(validDrivers, fmt.Sprintf("LMD-%d", i))
	}

	var zero struct{}
	drivers = append(drivers, "LMD-I1", "LMD-I2", "LMD-I3")

	for i := 0; i < b.N; i++ {
		vs := make(map[string]struct{})
		for _, driverID := range validDrivers {
			vs[driverID] = zero
		}

		invalidDrivers := make([]string, len(drivers)-len(validDrivers))
		i := 0
		for _, driverID := range drivers {
			if _, exists := vs[driverID]; !exists {
				invalidDrivers[i] = driverID
				i++
			}
		}
	}
}

func TestDriverRepository_MongoDriverQuery_IsEmpty(t *testing.T) {
	type testData struct {
		Name         string
		QueryBuilder repository.DriverQuery
		ExpectResult bool
	}

	testSet := []testData{
		{
			Name:         "empty",
			QueryBuilder: BuildDriverQuery(),
			ExpectResult: true,
		},
		{
			Name: "with driver id",
			QueryBuilder: BuildDriverQuery().
				WithDriverID("DRIVER_ID"),
			ExpectResult: false,
		},
		{
			Name: "with driver ids",
			QueryBuilder: BuildDriverQuery().
				WithDriverIDs([]string{"DRIVER_ID1", "DRIVER_ID2"}),
			ExpectResult: false,
		},
		{
			Name: "with driver role normal",
			QueryBuilder: BuildDriverQuery().
				WithDriverRole(string(model.DriverRoleNormal)),
			ExpectResult: false,
		},
	}

	for _, testdata := range testSet {
		t.Run(testdata.Name, func(tt *testing.T) {
			assert.Equal(tt, testdata.ExpectResult, testdata.QueryBuilder.IsEmpty())
		})
	}
}
