//go:build integration_test
// +build integration_test

package persistence_test

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestTripRepository_Create(t *testing.T) {

	// Given
	ctn := ittest.NewContainer(t)

	// When
	err := ctn.TripRepository.Create(context.Background(), model.Trip{
		TripID: "trip-A",
	})

	// Then
	require.NoError(t, err)
	var trip model.Trip
	err = ctn.TripDataStore.FindOne(context.Background(), bson.M{"trip_id": "trip-A"}, &trip)
	require.NoError(t, err)
	require.NotEqual(t, primitive.NilObjectID, trip.ID) // should have generated id for `_id` in trip
}

func TestTripUnSyncNotFound(t *testing.T) {
	t.Run("should not found TripIDs if have no un-sync-trip matched", func(t *testing.T) {
		ctn := ittest.NewContainer(t)

		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/internal/trip/get-unsync?")

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(t, http.StatusNotFound)
	})
}

func TestTripRepository_UpdateDistance0(t *testing.T) {
	ctn := ittest.NewContainer(t)
	trip := model.Trip{
		Routes: model.TripRoutes{
			{Distance: 0, EstimatedDeliveryTime: 0},
			{Distance: 0, EstimatedDeliveryTime: 0},
		},
		TripID: "distance0",
	}
	require.NoError(t, ctn.TripDataStore.Insert(context.Background(), trip))
	trip.Routes[0].Distance = 500
	trip.Routes[0].EstimatedDeliveryTime = 200
	require.NoError(t, ctn.TripRepository.UpdateDistance0(context.Background(), &trip))
	actual, err := ctn.TripRepository.GetTripByTripID(context.Background(), "distance0")
	require.NoError(t, err)
	require.EqualValues(t, 500, actual.Routes[0].Distance)
	require.EqualValues(t, 200, actual.Routes[0].EstimatedDeliveryTime)
	require.EqualValues(t, 0, actual.Routes[1].Distance)
	require.EqualValues(t, 0, actual.Routes[1].EstimatedDeliveryTime)
}
