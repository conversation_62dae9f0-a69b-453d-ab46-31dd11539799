//go:build integration_test
// +build integration_test

package persistence_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/twpayne/go-geom"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/ontopfare"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
)

func mustGeometryToModel(t *testing.T, coordinates model.OntopCoordinates) model.OntopGeometry {
	res, err := ontopfare.GeometryToModel(coordinates)
	require.NoError(t, err)
	return res
}

func toOnTopFareIds(v []model.OnTopFare) []string {
	var ids []string
	for _, o := range v {
		ids = append(ids, o.ID)
	}
	return ids
}

func TestMongoOnTopFareRepository_GetOnTopFare(t *testing.T) {
	ctn := ittest.NewContainer(t)
	thonburi := [][][]geom.Coord{{{
		{100.28723460002647, 13.525987915841924},
		{100.58307413860666, 13.524978671197829},
		{100.48757505948572, 13.764049409498497},
		{100.20522995599981, 13.721195923760618},
		{100.28723460002647, 13.525987915841924},
	}}}
	thonburiPoint := model.Location{
		Lng: 100.38117662894427,
		Lat: 13.627899545933872,
	}
	nonthaburiPoint := model.Location{
		Lng: 100.34283798003548,
		Lat: 13.818022671195791,
	}

	regionOntopGeometry := model.OntopGeometry{
		Type: "MultiPolygon",
		Coordinates: model.OntopCoordinates{
			{
				{{1, 2}, {2, 3}, {3, 1}, {1, 2}},
				{{2, 4}, {4, 8}, {8, 2}, {2, 4}},
			},
		},
	}

	require.NoError(t, ctn.ZoneDataStore.Insert(context.Background(), model.Zone{
		Geometry: model.NewZoneGeometry(thonburi),
		ZoneCode: "zone_code_1",
	}))
	docs := []model.OnTopFare{
		{
			ID:         "on-top-1",
			Region:     "BKK",
			ZoneCode:   "zone_code_1",
			Status:     model.StatusActive,
			Scheme:     model.FlatRateScheme,
			Conditions: []model.OntopCondition{{ServiceTypes: []model.Service{model.ServiceFood}}},
		},
		{
			ID:         "on-top-2",
			Region:     "BKK",
			Status:     model.StatusActive,
			Scheme:     model.FlatRateScheme,
			Conditions: []model.OntopCondition{{ServiceTypes: []model.Service{model.ServiceFood}}},
			Geometry: mustGeometryToModel(t, [][][][]float64{{{
				{100.28723460002647, 13.525987915841924},
				{100.58307413860666, 13.524978671197829},
				{100.48757505948572, 13.764049409498497},
				{100.20522995599981, 13.721195923760618},
				{100.28723460002647, 13.525987915841924},
			}}}),
		},
		{
			ID:            "on-top-3",
			Region:        "BKK",
			Status:        model.StatusActive,
			Scheme:        model.FlatRateScheme,
			Conditions:    []model.OntopCondition{{ServiceTypes: []model.Service{model.ServiceFood}}},
			RestaurantIDs: []string{"restaurant-A"},
			Restaurants: []model.OnTopRestaurant{{RestaurantId: "restaurant-A", Loc: model.OntopPoint{
				Type:        "Point",
				Coordinates: []float64{100.28723460002647, 13.525987915841924},
			}}},
		},
		{
			ID:       "on-top-4",
			Region:   "BKK",
			ZoneCode: "zone_code_1",
			Status:   model.StatusInactive,
			Scheme:   model.FlatRateScheme,
		},
		{ // Case IsWholeRegion is true without Coordinates
			ID:            "on-top-5",
			Region:        "BKK",
			Status:        model.StatusActive,
			Scheme:        model.FlatRateScheme,
			Conditions:    []model.OntopCondition{{ServiceTypes: []model.Service{model.ServiceFood}}},
			IsWholeRegion: true,
		},
		{ // Case doesn't has Coordinates and IsWholeRegion is false
			ID:            "on-top-6",
			Region:        "BKK",
			Status:        model.StatusActive,
			Scheme:        model.FlatRateScheme,
			Conditions:    []model.OntopCondition{{ServiceTypes: []model.Service{model.ServiceFood}}},
			IsWholeRegion: false,
		},
		{ // Case with both Coordinates and IsWholeRegion
			ID:     "on-top-7",
			Region: "BKK",
			Status: model.StatusActive,
			Geometry: mustGeometryToModel(t, [][][][]float64{{{
				{100.28723460002647, 13.525987915841924},
				{100.58307413860666, 13.524978671197829},
				{100.48757505948572, 13.764049409498497},
				{100.20522995599981, 13.721195923760618},
				{100.28723460002647, 13.525987915841924},
			}}}),
			Scheme:        model.FlatRateScheme,
			Conditions:    []model.OntopCondition{{ServiceTypes: []model.Service{model.ServiceFood}}},
			IsWholeRegion: true,
		},
	}
	docsTmp := make([]interface{}, len(docs))
	for idx, o := range docs {
		docsTmp[idx] = o
	}
	require.NoError(t, ctn.OnTopFareDataStore.InsertMany(context.Background(), docsTmp))

	t.Run("should return on-top fares by GetOnTopFareHeatMap correctly", func(t *testing.T) {
		results, err := ctn.OnTopFareRepository.GetOnTopFareHeatMap(context.Background(), thonburiPoint.Lat, thonburiPoint.Lng, "BKK", regionOntopGeometry, 1000)
		require.NoError(t, err)
		require.Len(t, results, 4)
		require.ElementsMatch(t, []string{"on-top-1", "on-top-2", "on-top-5", "on-top-7"}, toOnTopFareIds(results))
		require.NotEmpty(t, results[0].Geometry)
		require.NotEmpty(t, results[1].Geometry)
		require.Equal(t, regionOntopGeometry, results[2].Geometry)
		require.Equal(t, regionOntopGeometry, results[3].Geometry)

		results, err = ctn.OnTopFareRepository.GetOnTopFareHeatMap(context.Background(), 13.6501, 100.5401, "BKK", regionOntopGeometry, 1000)
		require.NoError(t, err)
		require.Len(t, results, 4)
		require.ElementsMatch(t, []string{"on-top-1", "on-top-2", "on-top-5", "on-top-7"}, toOnTopFareIds(results))
		require.NotEmpty(t, results[0].Geometry)
		require.NotEmpty(t, results[1].Geometry)
		require.Equal(t, regionOntopGeometry, results[2].Geometry)
		require.Equal(t, regionOntopGeometry, results[3].Geometry)

		results, err = ctn.OnTopFareRepository.GetOnTopFareHeatMap(context.Background(), nonthaburiPoint.Lat, nonthaburiPoint.Lng, "BKK", regionOntopGeometry, 1000)
		require.NoError(t, err)
		require.Len(t, results, 2)
		require.ElementsMatch(t, []string{"on-top-5", "on-top-7"}, toOnTopFareIds(results))
		require.Equal(t, regionOntopGeometry, results[0].Geometry)
		require.Equal(t, regionOntopGeometry, results[1].Geometry)
	})

	t.Run("should return on-top fares by GetOnTopFareByLocation correctly", func(t *testing.T) {
		results, err := ctn.OnTopFareRepository.GetOnTopFareByLocation(context.Background(), thonburiPoint, "food", "BKK")
		require.NoError(t, err)
		require.Len(t, results, 4)
		require.ElementsMatch(t, []string{"on-top-1", "on-top-2", "on-top-5", "on-top-7"}, toOnTopFareIds(results))

		results, err = ctn.OnTopFareRepository.GetOnTopFareByLocation(context.Background(), nonthaburiPoint, "food", "BKK")
		require.NoError(t, err)
		require.Len(t, results, 2)
		require.ElementsMatch(t, []string{"on-top-5", "on-top-7"}, toOnTopFareIds(results))
	})

	t.Run("should return on-top fares by FindOrderOnTop correctly", func(t *testing.T) {
		results, err := ctn.OnTopFareRepository.FindOrderOnTop(context.Background(), repository.OnTopOrderQuery{
			RestaurantID: "restaurant-A",
			Location:     thonburiPoint,
			ServiceType:  "food",
			Region:       "BKK",
		})
		require.NoError(t, err)
		require.Len(t, results, 5)
		require.ElementsMatch(t, []string{"on-top-1", "on-top-2", "on-top-3", "on-top-5", "on-top-7"}, toOnTopFareIds(results))
	})
}
