package persistence

import (
	"context"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/go/logx/v2"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/localcache"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

var _ repository.SummaryOfChangeRepository = &SummaryOfChangeRepository{}

type SummaryOfChangeRepository struct {
	dataStore   SummaryOfChangeDataStore
	redisClient datastore.RedisClient
	localCache  localcache.Caches
}

func (repo *SummaryOfChangeRepository) Create(ctx context.Context, summary *model.SummaryOfChange) error {
	currentTime := time.Now().UTC()
	summary.CreatedAt = currentTime
	summary.UpdatedAt = currentTime
	if err := repo.dataStore.Insert(ctx, summary); err != nil {
		logx.Error().Msgf("SummaryOfChangeRepository Create err: %v", err)
		return err
	}
	repo.deleteSummaryOfChangeCache(ctx)
	return nil
}

func (repo *SummaryOfChangeRepository) Get(ctx context.Context, id string) (*model.SummaryOfChange, error) {
	objectId, err := primitive.ObjectIDFromHex(strings.TrimSpace(id))
	if err != nil {
		logx.Error().Msgf("SummaryOfChangeRepository Get %s error: %v", id, err)
		return nil, err
	}
	var summary model.SummaryOfChange
	err = repo.dataStore.FindOne(ctx, bson.M{"_id": objectId}, &summary)
	if err != nil {
		logx.Error().Msgf("SummaryOfChangeRepository Get: Unable to find summary of change in database: %v", err)
		return nil, err
	}
	return &summary, nil

}

func (repo *SummaryOfChangeRepository) Update(ctx context.Context, summary *model.SummaryOfChange) error {
	summary.UpdatedAt = time.Now().UTC()
	err := repo.dataStore.Replace(ctx, bson.M{"_id": summary.ID}, summary)
	if err != nil {
		logx.Error().Msgf("SummaryOfChangeRepository Update err: %v", err)
		return err
	}
	repo.deleteSummaryOfChangeCache(ctx)
	return nil
}

func (repo *SummaryOfChangeRepository) Archived(ctx context.Context, id, updatedBy string) error {
	objectId, err := primitive.ObjectIDFromHex(strings.TrimSpace(id))
	if err != nil {
		logx.Error().Msgf("SummaryOfChangeRepository Archived ID is %s invalid: %v", id, err)
		return err
	}

	err = repo.dataStore.Update(
		ctx,
		bson.M{
			"_id": objectId,
		},
		bson.M{
			"$set": bson.M{
				"status":     model.SummaryOfChangeStatusArchived.ToString(),
				"updated_by": updatedBy,
				"updated_at": time.Now().UTC(),
			},
		},
	)
	if err != nil {
		logx.Error().Msgf("SummaryOfChangeRepository Archived err: %v", err)
		return err
	}
	repo.deleteSummaryOfChangeCache(ctx)
	return nil
}

func (repo *SummaryOfChangeRepository) deleteSummaryOfChangeCache(ctx context.Context) {
	err := cache.DelLatestSummaryOfChange(ctx, repo.redisClient)
	if err != nil {
		logx.Error().Msgf("SummaryOfChangeRepository DelLatestSummaryOfChange err: %v", err)
	}
	err = cache.DelActiveSummaryOfChanges(ctx, repo.redisClient)
	if err != nil {
		logx.Error().Msgf("SummaryOfChangeRepository DelSummaryOfChanges err: %v", err)
	}
}

func (repo *SummaryOfChangeRepository) FindWithQueryAndSortSummaryOfChange(ctx context.Context, query interface{}, skip, limit int, opts ...repository.Option) ([]model.SummaryOfChange, error) {
	var summaries []model.SummaryOfChange
	err := repo.dataStore.FindAndSort(ctx, query, skip, limit, []string{"-summary_of_change_date"}, &summaries, repository.ToDBOptions(opts)...)
	if err != nil {
		logx.Error().Msgf("SummaryOfChangeRepository FindWithQueryAndSortSummaryOfChangeWithCount err: %v", err)
		return []model.SummaryOfChange{}, err
	}

	return summaries, nil
}
func (repo *SummaryOfChangeRepository) FindActiveSummaryOfChanges(ctx context.Context, opts ...repository.Option) ([]model.SummaryOfChange, error) {
	cacheKey := cache.DriverActiveSummaryOfChangesKey()
	result, err := repo.localCache.Get(ctx, cacheKey, func(ctx context.Context) (interface{}, error) {
		summaryOfChangesInCache, err := cache.GetActiveSummaryOfChanges(ctx, repo.redisClient)
		if err != nil {
			logx.Warn().Msgf("SummaryOfChangeRepository FindActiveSummaryOfChanges: Unable to get summary of changes from cache: %v", err)
		} else {
			return summaryOfChangesInCache, nil
		}

		var summaryOfChanges []model.SummaryOfChange
		query := repository.NewSummaryOfChangeQuery().SetStatus(model.SummaryOfChangeStatusActive).Query()
		fideErr := repo.dataStore.FindAndSort(ctx, query, 0, 0, []string{"-summary_of_change_date"}, &summaryOfChanges, repository.ToDBOptions(opts)...)
		if fideErr != nil {
			logx.Error().Msgf("SummaryOfChangeRepository FindActiveSummaryOfChanges: Unable to find summary of changes in database: %v", err)
			return nil, err
		}

		setCacheErr := cache.SetActiveSummaryOfChanges(ctx, repo.redisClient, summaryOfChanges)
		if setCacheErr != nil {
			logx.Warn().Msgf("SummaryOfChangeRepository FindActiveSummaryOfChanges: Unable to set summary of changes in cache: %v", err)
		}

		return summaryOfChanges, nil
	})

	if err != nil {
		logx.Error().Msgf("SummaryOfChangeRepository FindActiveSummaryOfChanges: Unable to get summary of changes: %v", err)
		return nil, err
	}

	summaryOfChanges := result.([]model.SummaryOfChange)
	return summaryOfChanges, nil
}
func (repo *SummaryOfChangeRepository) GetLatestSummaryOfChange(ctx context.Context, opts ...repository.Option) (*model.SummaryOfChange, error) {
	cacheKey := cache.DriverLatestSummaryOfChangesKey()
	result, err := repo.localCache.Get(ctx, cacheKey, func(ctx context.Context) (interface{}, error) {
		summaryOfChangeInCache, err := cache.GetLatestSummaryOfChange(ctx, repo.redisClient)
		if err != nil {
			logx.Warn().Msgf("SummaryOfChangeRepository GetLatestDateSummaryOfChange: Unable to get summary of change from cache: %v", err)
		} else {
			return *summaryOfChangeInCache, nil
		}

		var summaryOfChanges []model.SummaryOfChange

		query := repository.NewSummaryOfChangeQuery().SetStatus(model.SummaryOfChangeStatusActive).Query()

		err = repo.dataStore.FindAndSort(ctx, query, 0, 1, []string{"-summary_of_change_date"}, &summaryOfChanges, repository.ToDBOptions(opts)...)
		if err != nil {
			logx.Warn().Msgf("SummaryOfChangeRepository GetLatestDateSummaryOfChange: Unable to set summary of change in cache: %v", err)
			return model.SummaryOfChange{}, err
		}

		if len(summaryOfChanges) > 0 {

			err = cache.SetLatestSummaryOfChange(ctx, repo.redisClient, summaryOfChanges[0])
			if err != nil {
				logx.Warn().Msgf("SummaryOfChangeRepository GetLatestDateSummaryOfChange: Unable to set summary of change in cache: %v", err)
			}

			return summaryOfChanges[0], nil
		}

		return model.SummaryOfChange{}, nil
	})

	if result != nil {
		summaryOfChange := result.(model.SummaryOfChange)
		return &summaryOfChange, nil
	}
	return nil, err
}

func ProvideSummaryOfChangeRepository(dataStore SummaryOfChangeDataStore, redisClient datastore.RedisClient, localCache localcache.Caches, meter metric.Meter) *repository.ProxySummaryOfChangeRepository {
	return repository.NewLatencyProxySummaryOfChangeRepository(&SummaryOfChangeRepository{
		dataStore:   dataStore,
		redisClient: redisClient,
		localCache:  localCache,
	}, meter)
}

type SummaryOfChangeDataStore mongodb.DataStoreInterface

func ProvideSummaryOfChangeDataStore(conn *mongodb.Conn) SummaryOfChangeDataStore {
	return mongodb.NewDataStoreWithConn(conn, "summary_of_changes")
}
