package persistence

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/go/logx/v2"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

var _ repository.AssignmentLogRepository = &MongoAssignmentLogRepository{}

// MongoAssignmentLogRepository logging record drivers who received order notify.
type MongoAssignmentLogRepository struct {
	DataStore DriverAssignmentLogsDataStore
}

func uniqueAssignmentLogKey(orderID string, deliveringRound int) bson.M {
	// for backward compat
	if deliveringRound == 0 {
		return bson.M{
			"order_id": orderID,
			"$or": bson.A{
				bson.M{"delivering_round": 0},
				bson.M{"delivering_round": bson.M{"$exists": false}},
			},
		}
	}
	return bson.M{
		"order_id":         orderID,
		"delivering_round": deliveringRound,
	}
}

func (log *MongoAssignmentLogRepository) AssignToDrivers(ctx context.Context, r int, orderID string, deliveringRound int, driverDists []repository.DriverDistance, option ...model.AssignmentLogOpt) (*model.AssignmentLogRecord, error) {
	opt := model.DefaultAssignmentLogOpt
	if len(option) > 0 {
		opt = option[0]
	}

	round := fmt.Sprintf("R%d", r)

	col, m := metric.GetCollector(), metric.NewAPILatency("assignment_logs")
	defer func() {
		if err := col.Save(m); err != nil {
			logrus.Errorf("cannot collect metric assignment_logs : %v", err)
		}
	}()
	aslog, err := log.find(ctx, orderID, deliveringRound, repository.WithReadPrimary)
	// insert a new one when error occurs.
	if err != nil {
		aslog = model.NewAssignmentLogRecord(orderID, deliveringRound, opt.Region)
		aslog.SearchRiderStrategy = opt.SearchRiderStrategy
		for _, dd := range driverDists {
			aslog.AddDriver(dd.DriverID, round, dd.Distance, opt)
		}
		if err := log.DataStore.Insert(ctx, aslog); err != nil {
			return nil, err
		}
		return &aslog, nil
	} else {
		aslog.SearchRiderStrategy = opt.SearchRiderStrategy
		for _, dd := range driverDists {
			aslog.AddDriver(dd.DriverID, round, dd.Distance, opt)
		}
		if err := log.update(ctx, &aslog); err != nil {
			return nil, err
		}
		return &aslog, nil
	}
}

func (log *MongoAssignmentLogRepository) UnassignToDriver(ctx context.Context, orderID string, deliveringRound int, driverID string, option ...model.AssignmentLogOpt) error {
	aslog, err := log.find(ctx, orderID, deliveringRound, repository.WithReadPrimary)
	if err != nil {
		return err
	} else {
		for index, driver := range aslog.Drivers {
			if driver.DriverID == driverID {
				aslog.RemoveDriver(driver.DriverID, index)
				break
			}
		}
		return log.update(ctx, &aslog)
	}
}

func (log *MongoAssignmentLogRepository) UpdateDriverNotAcceptAutoAssignedOrder(ctx context.Context, orderID string, driverID string) error {
	aslog, err := log.FindOneActive(ctx, orderID, repository.WithReadPrimary)
	if err != nil {
		return fmt.Errorf("UpdateDriverNotAcceptAutoAssignedOrder: Can not get assignment log: %v", err)
	}
	for i := 0; i < len(aslog.Drivers); i++ {
		if aslog.Drivers[i].DriverID == driverID {
			aslog.Drivers[i].IsNotAcceptAutoAssignedOrder = true
		}
	}
	return log.update(ctx, &aslog)
}

func (log *MongoAssignmentLogRepository) UpdateDriverTryAcceptAutoAssignedOrder(ctx context.Context, orderID string, driverID string) error {
	aslog, err := log.FindOneActive(ctx, orderID, repository.WithReadPrimary)
	if err != nil {
		return fmt.Errorf("UpdateDriverNotAcceptAutoAssignedOrder: Can not get assignment log: %v", err)
	}
	for i := 0; i < len(aslog.Drivers); i++ {
		if aslog.Drivers[i].DriverID == driverID {
			aslog.Drivers[i].IsTryAcceptAutoAssignedOrder = true
		}
	}
	return log.update(ctx, &aslog)
}

func (log *MongoAssignmentLogRepository) AutoAssignedDrivers(ctx context.Context, orderID string) ([]model.Record, model.SearchRiderStrategy, error) {
	var aslog model.AssignmentLogRecord
	err := log.DataStore.AggregateOne(ctx, []bson.M{
		{
			"$match": bson.M{
				"order_id":   orderID,
				"deleted_at": bson.M{"$exists": false},
			},
		},
		{
			"$project": bson.M{
				"drivers": bson.M{
					"$filter": bson.M{
						"input": "$drivers",
						"as":    "driver",
						"cond": bson.M{
							"$and": bson.A{
								bson.M{"$eq": bson.A{"$$driver.auto_assigned", true}},
							},
						},
					},
				},
				"search_rider_strategy": 1,
			},
		},
	}, &aslog)
	if err != nil {
		return nil, "", err
	}

	return aslog.Drivers, aslog.SearchRiderStrategy, nil
}

func (log *MongoAssignmentLogRepository) IsAutoAssigned(ctx context.Context, orderID string, driverID string) (bool, error) {
	drivers, _, err := log.AutoAssignedDrivers(ctx, orderID)
	if err == mongodb.ErrDataNotFound {
		return false, nil
	}
	if err != nil {
		return false, err
	}
	for _, d := range drivers {
		if d.DriverID == driverID {
			return true, nil
		}
	}

	return false, nil
}

func (log *MongoAssignmentLogRepository) AssignedDrivers(ctx context.Context, orderID string, opts ...repository.Option) ([]model.Record, error) {
	aslog, err := log.FindOneActive(ctx, orderID, opts...)
	if err != nil {
		return nil, err
	}
	return aslog.Drivers, nil
}

// find assignment log with specific deliveringRound. If assignment log for orderID not found, it's return empty assignment log
// with error.
func (log *MongoAssignmentLogRepository) find(ctx context.Context, orderID string, deliveringRound int, opts ...repository.Option) (model.AssignmentLogRecord, error) {
	var aslog model.AssignmentLogRecord
	query := uniqueAssignmentLogKey(orderID, deliveringRound)
	if err := log.DataStore.FindOne(ctx, query, &aslog, repository.ToDBOptions(opts)...); err != nil {
		return model.AssignmentLogRecord{}, err
	}
	return aslog, nil
}

// find active assignment log. If assignment log for orderID not found, it's return empty assignment log
// with error.
func (log *MongoAssignmentLogRepository) FindOneActive(ctx context.Context, orderID string, opts ...repository.Option) (model.AssignmentLogRecord, error) {
	var aslog model.AssignmentLogRecord
	query := bson.M{"order_id": orderID, "deleted_at": bson.M{"$exists": false}}
	if err := log.DataStore.FindOne(ctx, query, &aslog, repository.ToDBOptions(opts)...); err != nil {
		return model.AssignmentLogRecord{}, err
	}
	return aslog, nil
}

func (log *MongoAssignmentLogRepository) findOneActiveWithSelector(ctx context.Context, orderID string, selector bson.M, opts ...repository.Option) (model.AssignmentLogRecord, error) {
	var aslog model.AssignmentLogRecord
	query := bson.M{"order_id": orderID, "deleted_at": bson.M{"$exists": false}}
	if err := log.DataStore.FindOneWithSelector(ctx, query, selector, &aslog, repository.ToDBOptions(opts)...); err != nil {
		return model.AssignmentLogRecord{}, err
	}
	return aslog, nil
}

func (log *MongoAssignmentLogRepository) SoftDelete(ctx context.Context, orderID string, deliveringRound int) error {
	query := uniqueAssignmentLogKey(orderID, deliveringRound)
	update := bson.M{
		"$set": bson.M{"deleted_at": time.Now()},
	}
	if err := log.DataStore.Update(ctx, query, update); err != nil {
		return err
	}
	return nil
}

func (log *MongoAssignmentLogRepository) CountAssignmentLogByDriverID(ctx context.Context, driverID string, duration time.Duration, opts ...repository.Option) (int, error) {
	var count int
	if err := log.DataStore.Count(ctx, bson.M{
		"created_at": bson.M{
			"$gte": time.Now().Add(-duration),
		},
		"drivers": bson.M{
			"$elemMatch": bson.M{
				"driver_id": driverID,
			},
		},
	}, &count, repository.ToDBOptions(opts)...); err != nil {
		return 0, err
	}
	return count, nil
}

func (log *MongoAssignmentLogRepository) FilterOrdersAcceptableByRider(ctx context.Context, orderIDs []string, driverID string, opts ...repository.Option) ([]string, error) {
	query := bson.M{
		"order_id":   bson.M{"$in": orderIDs},
		"deleted_at": bson.M{"$exists": false},
		"drivers": bson.M{
			"$elemMatch": bson.M{
				"driver_id":                       driverID,
				"is_not_accept_auto_assign_order": false,
			},
		},
	}
	selector := bson.M{"order_id": 1}

	var aslog []model.AssignmentLogRecord
	if err := log.DataStore.FindWithSelector(ctx, query, selector, &aslog, repository.ToDBOptions(opts)...); err != nil {
		return []string{}, err
	}
	ids := make([]string, len(aslog))
	for i, a := range aslog {
		ids[i] = a.OrderID
	}
	return ids, nil
}

func (log *MongoAssignmentLogRepository) IllegalDriverIDs(ctx context.Context, orderID string, opts ...repository.Option) (types.StringSet, error) {
	result := types.NewStringSet()
	selector := bson.M{"illegal_drivers": 1}

	aslog, err := log.findOneActiveWithSelector(ctx, orderID, selector, opts...)
	if err != nil {
		return types.NewStringSet(), err
	}

	for _, driver := range aslog.IllegalDrivers {
		result.Add(driver.DriverID)
	}
	return result, nil
}

func (log *MongoAssignmentLogRepository) InsertIllegalDriver(ctx context.Context, orderID string, deliveringRound int, record model.IllegalDriverRecord) error {
	query := bson.M{"order_id": orderID, "delivering_round": deliveringRound}
	update := bson.M{
		"$push": bson.M{
			"illegal_drivers": record,
		},
	}
	return log.DataStore.Update(ctx, query, update)
}

func (log *MongoAssignmentLogRepository) update(ctx context.Context, as *model.AssignmentLogRecord) error {
	as.UpdatedAt = time.Now().UTC()
	selector := uniqueAssignmentLogKey(as.OrderID, as.DeliveringRound)
	return log.DataStore.Replace(ctx, selector, *as)
}

func NewMongoeAssignmentLogRepository(ds DriverAssignmentLogsDataStore) *MongoAssignmentLogRepository {
	return &MongoAssignmentLogRepository{
		DataStore: ds,
	}
}

func ProvideMongoAssignmentLogRepository(ds DriverAssignmentLogsDataStore, meter metric.Meter) *repository.ProxyAssignmentLogRepository {
	return repository.NewLatencyProxyAssignmentLogRepository(NewMongoeAssignmentLogRepository(ds), meter)
}

var _ repository.AssignmentLogRepository = (*MongoAssignmentLogRepository)(nil)

// DriverAssignmentLogsDataStore is type wrapper for driver_assignment_logs collection.
type DriverAssignmentLogsDataStore mongodb.DataStoreInterface

func ProvideDriverAssignmentLogsDataStore(conn *mongodb.Conn) DriverAssignmentLogsDataStore {
	return mongodb.NewDataStoreWithConn(conn, "driver_assignment_logs")
}

type DriverAssignmentLogsRevisionsDataStore mongodb.DataStoreInterface

func ProvideDriverAssignmentLogsRevisionsDataStore(conn *mongodb.Conn) DriverAssignmentLogsRevisionsDataStore {
	return mongodb.NewDataStoreWithConn(conn, "driver_assignment_logs_revisions")
}

func (log *MongoAssignmentLogRepository) CountOrdersByDriverIDSince(ctx context.Context, driverID string, since time.Time, opts ...repository.Option) (int, error) {
	query := bson.M{
		"drivers": bson.M{
			"$elemMatch": bson.M{
				"driver_id": driverID,
				"push_at": bson.M{
					"$gte": since,
				},
			},
		},
	}

	var count int
	if err := log.DataStore.Count(ctx, query, &count, repository.ToDBOptions(opts)...); err != nil {
		logx.Error().
			Context(ctx).
			Err(err).
			Str("method", "CountOrdersByDriverIDSince").
			Msg("fail to count corders by driver ID since ")

		return 0, err
	}

	return count, nil
}

func (log *MongoAssignmentLogRepository) GetManyAssignedDrivers(ctx context.Context, orderIDs []string, opts ...repository.Option) ([]model.AssignmentLogRecord, error) {
	var aslogs []model.AssignmentLogRecord
	query := bson.M{"order_id": bson.M{"$in": orderIDs}, "deleted_at": bson.M{"$exists": false}}

	if err := log.DataStore.Find(ctx, query, 0, 0, &aslogs, repository.ToDBOptions(opts)...); err != nil {
		if err == mongodb.ErrDataNotFound {
			return aslogs, repository.ErrNotFound
		}

		return aslogs, err
	}

	return aslogs, nil
}
