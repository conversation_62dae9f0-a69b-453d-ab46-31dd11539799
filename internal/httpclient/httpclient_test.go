package httpclient

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/afex/hystrix-go/hystrix"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestClient_CircuitBreakerClose(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(`{}`))
	}))
	defer ts.Close()

	type testcase struct {
		name   string
		client func() *Client
		setEnv func(t *testing.T)
	}

	testcases := []testcase{
		{
			name: "dalian client",
			client: func() *Client {
				c := ProvideDalianClient()
				return c.Client
			},
			setEnv: func(t *testing.T) {
				t.Setenv("LM_DALIAN_HTTP_CIRCUIT_BREAKER_NAME", fmt.Sprintf("dalian-cb-%s", uuid.NewString()))
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			tc.setEnv(t)

			underTest := tc.client()

			resp, err := underTest.Get(context.Background(), ts.URL, nil)
			if err == nil {
				resp.Body.Close()
			}

			require.NoError(t, err)
		})
	}
}

func TestClient_CircuitBreakerTripped_ServerErrorResponse(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(`{}`))
	}))
	defer ts.Close()

	type testcase struct {
		name   string
		client func() *Client
		setEnv func(t *testing.T)
	}

	testcases := []testcase{
		{
			name: "dalian client",
			client: func() *Client {
				c := ProvideDalianClient()
				return c.Client
			},
			setEnv: func(t *testing.T) {
				t.Setenv("LM_DALIAN_HTTP_CIRCUIT_BREAKER_NAME", fmt.Sprintf("dalian-cb-%s", uuid.NewString()))
				t.Setenv("LM_DALIAN_HTTP_CIRCUIT_BREAKER_REQUEST_VOLUME_THRESHOLD", "1")
				t.Setenv("LM_DALIAN_HTTP_CIRCUIT_BREAKER_ERROR_PERCENT_THRESHOLD", "100")
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			tc.setEnv(t)

			underTest := tc.client()

			resp, err := underTest.Get(context.Background(), ts.URL, nil)
			if err == nil {
				resp.Body.Close()
			}

			assert.Eventually(t, func() bool {
				resp, err := underTest.Get(context.Background(), ts.URL, nil)
				if err == nil {
					resp.Body.Close()
				}

				return errors.Is(err, hystrix.ErrCircuitOpen)
			}, time.Second, 50*time.Millisecond)
		})
	}
}

func TestClient_CircuitBreakerTripped_Timeout(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(50 * time.Millisecond)
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(`{}`))
	}))
	defer ts.Close()

	type testcase struct {
		name   string
		client func() *Client
		setEnv func(t *testing.T)
	}

	testcases := []testcase{
		{
			name: "dalian client",
			client: func() *Client {
				c := ProvideDalianClient()
				return c.Client
			},
			setEnv: func(t *testing.T) {
				t.Setenv("LM_DALIAN_HTTP_CIRCUIT_BREAKER_NAME", fmt.Sprintf("dalian-cb-%s", uuid.NewString()))
				t.Setenv("LM_DALIAN_HTTP_CIRCUIT_BREAKER_TIMEOUT", "10ms")
				t.Setenv("LM_DALIAN_HTTP_CIRCUIT_BREAKER_REQUEST_VOLUME_THRESHOLD", "1")
				t.Setenv("LM_DALIAN_HTTP_CIRCUIT_BREAKER_ERROR_PERCENT_THRESHOLD", "100")
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			tc.setEnv(t)

			underTest := tc.client()

			resp, err := underTest.Get(context.Background(), ts.URL, nil)
			if err == nil {
				resp.Body.Close()
			}

			assert.ErrorIs(t, err, hystrix.ErrTimeout)
		})
	}
}
