package httpclient

import (
	"net/http"
	"time"

	cnthttp "git.wndv.co/go/cnt/http"
)

func FinalizeCircuitBreakerConfig(name string, cbCfg cnthttp.CircuitBreakerConfig) cnthttp.CircuitBreakerConfig {
	if cbCfg.Name == "" {
		cbCfg.Name = name
	}
	if cbCfg.IsErrorStatusCode == nil {
		cbCfg.IsErrorStatusCode = func(statusCode int) bool {
			return statusCode == http.StatusRequestTimeout ||
				statusCode >= http.StatusServiceUnavailable ||
				statusCode == http.StatusInternalServerError
		}
	}
	if cbCfg.Timeout < 45*time.Second {
		cbCfg.Timeout = 45 * time.Second
	}
	return cbCfg
}
