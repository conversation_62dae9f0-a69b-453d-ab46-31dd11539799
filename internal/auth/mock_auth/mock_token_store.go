// Code generated by MockGen. DO NOT EDIT.
// Source: ./token_store.go

// Package mock_auth is a generated GoMock package.
package mock_auth

import (
	context "context"
	reflect "reflect"

	auth "git.wndv.co/lineman/fleet-distribution/internal/auth"
	gomock "github.com/golang/mock/gomock"
)

// MockTokenStorage is a mock of TokenStorage interface.
type MockTokenStorage struct {
	ctrl     *gomock.Controller
	recorder *MockTokenStorageMockRecorder
}

// MockTokenStorageMockRecorder is the mock recorder for MockTokenStorage.
type MockTokenStorageMockRecorder struct {
	mock *MockTokenStorage
}

// NewMockTokenStorage creates a new mock instance.
func NewMockTokenStorage(ctrl *gomock.Controller) *MockTokenStorage {
	mock := &MockTokenStorage{ctrl: ctrl}
	mock.recorder = &MockTokenStorageMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTokenStorage) EXPECT() *MockTokenStorageMockRecorder {
	return m.recorder
}

// Store mocks base method.
func (m *MockTokenStorage) Store(ctx context.Context, props auth.TokenProps) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Store", ctx, props)
	ret0, _ := ret[0].(error)
	return ret0
}

// Store indicates an expected call of Store.
func (mr *MockTokenStorageMockRecorder) Store(ctx, props interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Store", reflect.TypeOf((*MockTokenStorage)(nil).Store), ctx, props)
}
