package httpcsvwriter

import (
	"compress/gzip"
	"io"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func Test_gzipChunkedWriter_streamWrite(t *testing.T) {
	t.Run("csvutil encoder", func(t *testing.T) {
		// Given
		c := &HTTPGzipChunkedWriter{
			encoderType:      CSVUtilEncoderType,
			compressionLevel: gzip.BestCompression,
		}
		rr := httptest.NewRecorder()

		datas := []struct {
			A string `csv:"column-a"`
		}{
			{A: "Test1"},
			{A: "Test2"},
		}

		// When
		err := c.StreamWrite(rr, "test.csv", func() <-chan interface{} {
			ch := make(chan interface{})

			go func() {
				for _, u := range datas {
					ch <- u
				}
				close(ch)
			}()

			return ch
		})

		// Then
		require.NoError(t, err)
		gzipReader, err := gzip.NewReader(rr.Body)
		if err != nil {
			t.Fatalf("gzip reader err: %v", err)
		}
		defer gzipReader.Close()

		s, err := io.ReadAll(gzipReader)
		if err != nil {
			t.Fatalf("read from gzip err: %v", err)
		}

		assert.Equal(t, "application/x-gzip", rr.Header().Get("Content-Type"))
		assert.Equal(t, "attachment;filename=test.csv", rr.Header().Get("Content-Disposition"))
		assert.Equal(t, "column-a\nTest1\nTest2\n", string(s))

	})

	t.Run("go csv encoder", func(t *testing.T) {
		// Given
		c := &HTTPGzipChunkedWriter{
			encoderType:      GoCSVEncoderType,
			compressionLevel: gzip.BestCompression,
		}
		rr := httptest.NewRecorder()

		datas := [][]string{
			{"column-a"},
			{"Test1"},
			{"Test2"},
		}

		// When
		err := c.StreamWrite(rr, "test.csv", func() <-chan interface{} {
			ch := make(chan interface{})

			go func() {
				for _, u := range datas {
					ch <- u
				}
				close(ch)
			}()

			return ch
		})

		// Then
		require.NoError(t, err)
		gzipReader, err := gzip.NewReader(rr.Body)
		if err != nil {
			t.Fatalf("gzip reader err: %v", err)
		}
		defer gzipReader.Close()

		s, err := io.ReadAll(gzipReader)
		if err != nil {
			t.Fatalf("read from gzip err: %v", err)
		}

		assert.Equal(t, "application/x-gzip", rr.Header().Get("Content-Type"))
		assert.Equal(t, "attachment;filename=test.csv", rr.Header().Get("Content-Disposition"))
		assert.Equal(t, "column-a\nTest1\nTest2\n", string(s))

	})

}
