// Code generated by MockGen. DO NOT EDIT.
// Source: ./assignment_rejection_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockAssignmentRejectionRepository is a mock of AssignmentRejectionRepository interface.
type MockAssignmentRejectionRepository struct {
	ctrl     *gomock.Controller
	recorder *MockAssignmentRejectionRepositoryMockRecorder
}

// MockAssignmentRejectionRepositoryMockRecorder is the mock recorder for MockAssignmentRejectionRepository.
type MockAssignmentRejectionRepositoryMockRecorder struct {
	mock *MockAssignmentRejectionRepository
}

// NewMockAssignmentRejectionRepository creates a new mock instance.
func NewMockAssignmentRejectionRepository(ctrl *gomock.Controller) *MockAssignmentRejectionRepository {
	mock := &MockAssignmentRejectionRepository{ctrl: ctrl}
	mock.recorder = &MockAssignmentRejectionRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssignmentRejectionRepository) EXPECT() *MockAssignmentRejectionRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockAssignmentRejectionRepository) Create(ctx context.Context, approval *model.AssignmentRejection) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, approval)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockAssignmentRejectionRepositoryMockRecorder) Create(ctx, approval interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockAssignmentRejectionRepository)(nil).Create), ctx, approval)
}
