// Code generated by MockGen. DO NOT EDIT.
// Source: ./zone_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
	geom "github.com/twpayne/go-geom"
	primitive "go.mongodb.org/mongo-driver/bson/primitive"
)

// MockZoneRepository is a mock of ZoneRepository interface.
type MockZoneRepository struct {
	ctrl     *gomock.Controller
	recorder *MockZoneRepositoryMockRecorder
}

// MockZoneRepositoryMockRecorder is the mock recorder for MockZoneRepository.
type MockZoneRepositoryMockRecorder struct {
	mock *MockZoneRepository
}

// NewMockZoneRepository creates a new mock instance.
func NewMockZoneRepository(ctrl *gomock.Controller) *MockZoneRepository {
	mock := &MockZoneRepository{ctrl: ctrl}
	mock.recorder = &MockZoneRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockZoneRepository) EXPECT() *MockZoneRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockZoneRepository) Create(ctx context.Context, zone model.Zone) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, zone)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockZoneRepositoryMockRecorder) Create(ctx, zone interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockZoneRepository)(nil).Create), ctx, zone)
}

// Find mocks base method.
func (m *MockZoneRepository) Find(ctx context.Context, req model.ZoneQueryReq, skip, limit int, sort []string, opts ...repository.Option) ([]model.Zone, int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req, skip, limit, sort}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Find", varargs...)
	ret0, _ := ret[0].([]model.Zone)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Find indicates an expected call of Find.
func (mr *MockZoneRepositoryMockRecorder) Find(ctx, req, skip, limit, sort interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req, skip, limit, sort}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockZoneRepository)(nil).Find), varargs...)
}

// FindActiveZoneCodesByLocation mocks base method.
func (m *MockZoneRepository) FindActiveZoneCodesByLocation(ctx context.Context, lat, lng float64, opts ...repository.Option) ([]string, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, lat, lng}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindActiveZoneCodesByLocation", varargs...)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindActiveZoneCodesByLocation indicates an expected call of FindActiveZoneCodesByLocation.
func (mr *MockZoneRepositoryMockRecorder) FindActiveZoneCodesByLocation(ctx, lat, lng interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, lat, lng}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindActiveZoneCodesByLocation", reflect.TypeOf((*MockZoneRepository)(nil).FindActiveZoneCodesByLocation), varargs...)
}

// FindBriefZones mocks base method.
func (m *MockZoneRepository) FindBriefZones(ctx context.Context, req model.BriefZoneQueryReq) ([]model.BriefZone, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindBriefZones", ctx, req)
	ret0, _ := ret[0].([]model.BriefZone)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindBriefZones indicates an expected call of FindBriefZones.
func (mr *MockZoneRepositoryMockRecorder) FindBriefZones(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindBriefZones", reflect.TypeOf((*MockZoneRepository)(nil).FindBriefZones), ctx, req)
}

// FindById mocks base method.
func (m *MockZoneRepository) FindById(ctx context.Context, id string, opts ...repository.Option) (model.Zone, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindById", varargs...)
	ret0, _ := ret[0].(model.Zone)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindById indicates an expected call of FindById.
func (mr *MockZoneRepositoryMockRecorder) FindById(ctx, id interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindById", reflect.TypeOf((*MockZoneRepository)(nil).FindById), varargs...)
}

// FindByZoneCode mocks base method.
func (m *MockZoneRepository) FindByZoneCode(ctx context.Context, zoneCode string, opts ...repository.Option) (model.Zone, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, zoneCode}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByZoneCode", varargs...)
	ret0, _ := ret[0].(model.Zone)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByZoneCode indicates an expected call of FindByZoneCode.
func (mr *MockZoneRepositoryMockRecorder) FindByZoneCode(ctx, zoneCode interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, zoneCode}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByZoneCode", reflect.TypeOf((*MockZoneRepository)(nil).FindByZoneCode), varargs...)
}

// FindZoneCodesByLocation mocks base method.
func (m *MockZoneRepository) FindZoneCodesByLocation(ctx context.Context, lat, lng float64, opts ...repository.Option) ([]string, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, lat, lng}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindZoneCodesByLocation", varargs...)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindZoneCodesByLocation indicates an expected call of FindZoneCodesByLocation.
func (mr *MockZoneRepositoryMockRecorder) FindZoneCodesByLocation(ctx, lat, lng interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, lat, lng}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindZoneCodesByLocation", reflect.TypeOf((*MockZoneRepository)(nil).FindZoneCodesByLocation), varargs...)
}

// FindZoneIDsByLocation mocks base method.
func (m *MockZoneRepository) FindZoneIDsByLocation(ctx context.Context, lat, lng float64, opts ...repository.Option) ([]primitive.ObjectID, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, lat, lng}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindZoneIDsByLocation", varargs...)
	ret0, _ := ret[0].([]primitive.ObjectID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindZoneIDsByLocation indicates an expected call of FindZoneIDsByLocation.
func (mr *MockZoneRepositoryMockRecorder) FindZoneIDsByLocation(ctx, lat, lng interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, lat, lng}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindZoneIDsByLocation", reflect.TypeOf((*MockZoneRepository)(nil).FindZoneIDsByLocation), varargs...)
}

// FindZonesIntersectPolygon mocks base method.
func (m *MockZoneRepository) FindZonesIntersectPolygon(ctx context.Context, polygon *geom.Polygon, opts ...repository.Option) ([]model.ZoneWithoutGeometry, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, polygon}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindZonesIntersectPolygon", varargs...)
	ret0, _ := ret[0].([]model.ZoneWithoutGeometry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindZonesIntersectPolygon indicates an expected call of FindZonesIntersectPolygon.
func (mr *MockZoneRepositoryMockRecorder) FindZonesIntersectPolygon(ctx, polygon interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, polygon}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindZonesIntersectPolygon", reflect.TypeOf((*MockZoneRepository)(nil).FindZonesIntersectPolygon), varargs...)
}

// UpdateById mocks base method.
func (m *MockZoneRepository) UpdateById(ctx context.Context, id string, req model.UpdateZoneReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateById", ctx, id, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateById indicates an expected call of UpdateById.
func (mr *MockZoneRepositoryMockRecorder) UpdateById(ctx, id, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateById", reflect.TypeOf((*MockZoneRepository)(nil).UpdateById), ctx, id, req)
}
