// Code generated by MockGen. DO NOT EDIT.
// Source: ./internal_cancel_reason_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	types "git.wndv.co/lineman/fleet-distribution/internal/types"
	gomock "github.com/golang/mock/gomock"
)

// MockInternalCancelReasonRepository is a mock of InternalCancelReasonRepository interface.
type MockInternalCancelReasonRepository struct {
	ctrl     *gomock.Controller
	recorder *MockInternalCancelReasonRepositoryMockRecorder
}

// MockInternalCancelReasonRepositoryMockRecorder is the mock recorder for MockInternalCancelReasonRepository.
type MockInternalCancelReasonRepositoryMockRecorder struct {
	mock *MockInternalCancelReasonRepository
}

// NewMockInternalCancelReasonRepository creates a new mock instance.
func NewMockInternalCancelReasonRepository(ctrl *gomock.Controller) *MockInternalCancelReasonRepository {
	mock := &MockInternalCancelReasonRepository{ctrl: ctrl}
	mock.recorder = &MockInternalCancelReasonRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInternalCancelReasonRepository) EXPECT() *MockInternalCancelReasonRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockInternalCancelReasonRepository) Create(ctx context.Context, internalCancelReason *model.InternalCancelReason) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, internalCancelReason)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockInternalCancelReasonRepositoryMockRecorder) Create(ctx, internalCancelReason interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockInternalCancelReasonRepository)(nil).Create), ctx, internalCancelReason)
}

// Delete mocks base method.
func (m *MockInternalCancelReasonRepository) Delete(ctx context.Context, internalCancelReasonId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, internalCancelReasonId)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockInternalCancelReasonRepositoryMockRecorder) Delete(ctx, internalCancelReasonId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockInternalCancelReasonRepository)(nil).Delete), ctx, internalCancelReasonId)
}

// Find mocks base method.
func (m *MockInternalCancelReasonRepository) Find(ctx context.Context, skip, limit int, opts ...repository.Option) ([]model.InternalCancelReason, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Find", varargs...)
	ret0, _ := ret[0].([]model.InternalCancelReason)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockInternalCancelReasonRepositoryMockRecorder) Find(ctx, skip, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockInternalCancelReasonRepository)(nil).Find), varargs...)
}

// FindByIDs mocks base method.
func (m *MockInternalCancelReasonRepository) FindByIDs(ctx context.Context, icrIDs types.StringSet, opts ...repository.Option) (map[string]model.InternalCancelReason, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, icrIDs}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByIDs", varargs...)
	ret0, _ := ret[0].(map[string]model.InternalCancelReason)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByIDs indicates an expected call of FindByIDs.
func (mr *MockInternalCancelReasonRepositoryMockRecorder) FindByIDs(ctx, icrIDs interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, icrIDs}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIDs", reflect.TypeOf((*MockInternalCancelReasonRepository)(nil).FindByIDs), varargs...)
}

// FindById mocks base method.
func (m *MockInternalCancelReasonRepository) FindById(ctx context.Context, internalCancelReasonId string, opts ...repository.Option) (*model.InternalCancelReason, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, internalCancelReasonId}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindById", varargs...)
	ret0, _ := ret[0].(*model.InternalCancelReason)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindById indicates an expected call of FindById.
func (mr *MockInternalCancelReasonRepositoryMockRecorder) FindById(ctx, internalCancelReasonId interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, internalCancelReasonId}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindById", reflect.TypeOf((*MockInternalCancelReasonRepository)(nil).FindById), varargs...)
}

// FindByLabel mocks base method.
func (m *MockInternalCancelReasonRepository) FindByLabel(ctx context.Context, label string, opts ...repository.Option) (*model.InternalCancelReason, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, label}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByLabel", varargs...)
	ret0, _ := ret[0].(*model.InternalCancelReason)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByLabel indicates an expected call of FindByLabel.
func (mr *MockInternalCancelReasonRepositoryMockRecorder) FindByLabel(ctx, label interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, label}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByLabel", reflect.TypeOf((*MockInternalCancelReasonRepository)(nil).FindByLabel), varargs...)
}

// FindSorted mocks base method.
func (m *MockInternalCancelReasonRepository) FindSorted(ctx context.Context, skip, limit int, opts ...repository.Option) ([]model.InternalCancelReason, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindSorted", varargs...)
	ret0, _ := ret[0].([]model.InternalCancelReason)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindSorted indicates an expected call of FindSorted.
func (mr *MockInternalCancelReasonRepositoryMockRecorder) FindSorted(ctx, skip, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindSorted", reflect.TypeOf((*MockInternalCancelReasonRepository)(nil).FindSorted), varargs...)
}

// Update mocks base method.
func (m *MockInternalCancelReasonRepository) Update(ctx context.Context, driver *model.InternalCancelReason) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, driver)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockInternalCancelReasonRepositoryMockRecorder) Update(ctx, driver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockInternalCancelReasonRepository)(nil).Update), ctx, driver)
}
