// Code generated by MockGen. DO NOT EDIT.
// Source: ./otp_session_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockOTPSessionRepo is a mock of OTPSessionRepo interface.
type MockOTPSessionRepo struct {
	ctrl     *gomock.Controller
	recorder *MockOTPSessionRepoMockRecorder
}

// MockOTPSessionRepoMockRecorder is the mock recorder for MockOTPSessionRepo.
type MockOTPSessionRepoMockRecorder struct {
	mock *MockOTPSessionRepo
}

// NewMockOTPSessionRepo creates a new mock instance.
func NewMockOTPSessionRepo(ctrl *gomock.Controller) *MockOTPSessionRepo {
	mock := &MockOTPSessionRepo{ctrl: ctrl}
	mock.recorder = &MockOTPSessionRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOTPSessionRepo) EXPECT() *MockOTPSessionRepoMockRecorder {
	return m.recorder
}

// Load mocks base method.
func (m *MockOTPSessionRepo) Load(ctx context.Context, lineUID string) (model.OTPSession, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Load", ctx, lineUID)
	ret0, _ := ret[0].(model.OTPSession)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Load indicates an expected call of Load.
func (mr *MockOTPSessionRepoMockRecorder) Load(ctx, lineUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Load", reflect.TypeOf((*MockOTPSessionRepo)(nil).Load), ctx, lineUID)
}

// LoadOrNew mocks base method.
func (m *MockOTPSessionRepo) LoadOrNew(ctx context.Context, lineUID string) (model.OTPSession, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadOrNew", ctx, lineUID)
	ret0, _ := ret[0].(model.OTPSession)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadOrNew indicates an expected call of LoadOrNew.
func (mr *MockOTPSessionRepoMockRecorder) LoadOrNew(ctx, lineUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadOrNew", reflect.TypeOf((*MockOTPSessionRepo)(nil).LoadOrNew), ctx, lineUID)
}

// Remove mocks base method.
func (m *MockOTPSessionRepo) Remove(ctx context.Context, lineUID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Remove", ctx, lineUID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Remove indicates an expected call of Remove.
func (mr *MockOTPSessionRepoMockRecorder) Remove(ctx, lineUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Remove", reflect.TypeOf((*MockOTPSessionRepo)(nil).Remove), ctx, lineUID)
}

// Save mocks base method.
func (m *MockOTPSessionRepo) Save(ctx context.Context, ses model.OTPSession) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", ctx, ses)
	ret0, _ := ret[0].(error)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockOTPSessionRepoMockRecorder) Save(ctx, ses interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockOTPSessionRepo)(nil).Save), ctx, ses)
}
