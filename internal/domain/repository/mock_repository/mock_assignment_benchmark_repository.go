// Code generated by MockGen. DO NOT EDIT.
// Source: ./assignment_benchmark_repository.go
//
// Generated by this command:
//
//	mockgen -source=./assignment_benchmark_repository.go -destination=./mock_repository/mock_assignment_benchmark_repository.go -package=mock_repository
//

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	mongo "go.mongodb.org/mongo-driver/mongo"
	gomock "go.uber.org/mock/gomock"
)

// MockAssignmentBenchmarkRepository is a mock of AssignmentBenchmarkRepository interface.
type MockAssignmentBenchmarkRepository struct {
	ctrl     *gomock.Controller
	recorder *MockAssignmentBenchmarkRepositoryMockRecorder
}

// MockAssignmentBenchmarkRepositoryMockRecorder is the mock recorder for MockAssignmentBenchmarkRepository.
type MockAssignmentBenchmarkRepositoryMockRecorder struct {
	mock *MockAssignmentBenchmarkRepository
}

// NewMockAssignmentBenchmarkRepository creates a new mock instance.
func NewMockAssignmentBenchmarkRepository(ctrl *gomock.Controller) *MockAssignmentBenchmarkRepository {
	mock := &MockAssignmentBenchmarkRepository{ctrl: ctrl}
	mock.recorder = &MockAssignmentBenchmarkRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssignmentBenchmarkRepository) EXPECT() *MockAssignmentBenchmarkRepositoryMockRecorder {
	return m.recorder
}

// BulkWrite mocks base method.
func (m_2 *MockAssignmentBenchmarkRepository) BulkWrite(ctx context.Context, m []mongo.WriteModel, opts ...repository.Option) (*mongo.BulkWriteResult, error) {
	m_2.ctrl.T.Helper()
	varargs := []any{ctx, m}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m_2.ctrl.Call(m_2, "BulkWrite", varargs...)
	ret0, _ := ret[0].(*mongo.BulkWriteResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkWrite indicates an expected call of BulkWrite.
func (mr *MockAssignmentBenchmarkRepositoryMockRecorder) BulkWrite(ctx, m any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, m}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkWrite", reflect.TypeOf((*MockAssignmentBenchmarkRepository)(nil).BulkWrite), varargs...)
}

// Count mocks base method.
func (m *MockAssignmentBenchmarkRepository) Count(ctx context.Context, filter repository.Filter, count *int, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, filter, count}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Count", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Count indicates an expected call of Count.
func (mr *MockAssignmentBenchmarkRepositoryMockRecorder) Count(ctx, filter, count any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, filter, count}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockAssignmentBenchmarkRepository)(nil).Count), varargs...)
}

// DeleteMany mocks base method.
func (m *MockAssignmentBenchmarkRepository) DeleteMany(ctx context.Context, filter repository.Filter, opts ...repository.Option) (*mongo.DeleteResult, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, filter}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteMany", varargs...)
	ret0, _ := ret[0].(*mongo.DeleteResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteMany indicates an expected call of DeleteMany.
func (mr *MockAssignmentBenchmarkRepositoryMockRecorder) DeleteMany(ctx, filter any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, filter}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMany", reflect.TypeOf((*MockAssignmentBenchmarkRepository)(nil).DeleteMany), varargs...)
}

// Find mocks base method.
func (m *MockAssignmentBenchmarkRepository) Find(ctx context.Context, filter repository.Filter, skip, limit int, opts ...repository.Option) ([]model.AssignmentBenchmark, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, filter, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Find", varargs...)
	ret0, _ := ret[0].([]model.AssignmentBenchmark)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockAssignmentBenchmarkRepositoryMockRecorder) Find(ctx, filter, skip, limit any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, filter, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockAssignmentBenchmarkRepository)(nil).Find), varargs...)
}

// FindOne mocks base method.
func (m *MockAssignmentBenchmarkRepository) FindOne(ctx context.Context, params *repository.FindAssignmentBenchmarkParams, opts ...repository.Option) (*model.AssignmentBenchmark, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, params}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindOne", varargs...)
	ret0, _ := ret[0].(*model.AssignmentBenchmark)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockAssignmentBenchmarkRepositoryMockRecorder) FindOne(ctx, params any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, params}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockAssignmentBenchmarkRepository)(nil).FindOne), varargs...)
}
