package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

//go:generate mockgen -source=./trip_repository.go -destination=./mock_repository/mock_trip_repository.go -package=mock_repository

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type TripRepository interface {
	Create(ctx context.Context, t model.Trip) error
	FindWithQueryAndSort(ctx context.Context, query interface{}, skip, limit int, opts ...Option) ([]model.Trip, error)
	CountWithQuery(ctx context.Context, query interface{}, opts ...Option) (int, error)
	GetTrip() (model.Trip, error)
	IsExists(ctx context.Context, tripID string) bool
	GetTripByTripID(ctx context.Context, tripID string, opts ...Option) (model.Trip, error)
	GetMany(ctx context.Context, tripIDs []string) ([]model.Trip, error)
	GetManyOrderedByIdx(ctx context.Context, tripIDs []string) ([]model.Trip, error)
	GetOngoingTripByDriverID(ctx context.Context, id string) ([]model.Trip, error)
	Update(ctx context.Context, trip model.Trip) error
	SetFraudData(ctx context.Context, trip model.Trip) error
	SetRefundData(ctx context.Context, trip model.Trip) error
	UpdateTransferAmountOutstanding(ctx context.Context, id string, tf types.Money, od types.Money) error
	GetUnsyncTripIDs(ctx context.Context, start time.Time, end time.Time, opts ...Option) ([]string, error)
	SetDistanceCompensatoryInfo(ctx context.Context, tripID string, info *model.TripDistanceCompensatoryInfo, opts ...Option) error
	UpdateDistance0(ctx context.Context, trip *model.Trip) error
	GetTripByOrderIDAndDriverID(ctx context.Context, driverID string, orderID string, opts ...Option) (model.Trip, error)
	UpdateRestaurantReviewedByTripIDAndRouteIndex(ctx context.Context, tripID string, routeIndex int, restaurantReviewed bool) error
	SetPreviousTripID(ctx context.Context, tripID string, previousTripID string) error
	MarkSyncDeliveryAllPickUpStopsDone(ctx context.Context, tripID string) error
}
