// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyShiftRepository(delegate ShiftRepository, meter metric.Meter) *ProxyShiftRepository {
	return &ProxyShiftRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyShiftRepository-tracer"),
	}
}

type ProxyShiftRepository struct {
	Delegate         ShiftRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyShiftRepository) AnyStartEndDatetimeDuplicated(i0 context.Context, i1 []model.Shift, i2 []string, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "ShiftRepository.AnyStartEndDatetimeDuplicated")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.AnyStartEndDatetimeDuplicated(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.AnyStartEndDatetimeDuplicated")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyShiftRepository) Book(i0 context.Context, i1 *model.Shift, i2 string, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "ShiftRepository.Book")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Book(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.Book")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyShiftRepository) CreateAll(i0 context.Context, i1 []model.Shift, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "ShiftRepository.CreateAll")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.CreateAll(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.CreateAll")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyShiftRepository) Delete(i0 context.Context, i1 *model.Shift, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "ShiftRepository.Delete")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Delete(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.Delete")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyShiftRepository) Find(i0 context.Context, i1 ShiftQuery, i2 int, i3 int, i4 ...Option) ([]model.Shift, int, error) {

	_, span := p.Tracer.Start(i0, "ShiftRepository.Find")
	defer span.End()

	start := time.Now()

	r0, r1, r2 := p.Delegate.Find(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.Find")

	if r2 != nil {
		span.RecordError(r2)
		span.SetStatus(codes.Error, r2.Error())
	}

	return r0, r1, r2
}

func (p *ProxyShiftRepository) FindActiveByDriverIdAndRegion(i0 context.Context, i1 string, i2 string, i3 time.Time, i4 time.Time, i5 ...Option) ([]model.Shift, error) {

	_, span := p.Tracer.Start(i0, "ShiftRepository.FindActiveByDriverIdAndRegion")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindActiveByDriverIdAndRegion(i0, i1, i2, i3, i4, i5...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.FindActiveByDriverIdAndRegion")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyShiftRepository) FindActiveByIdAndRegionAndNotDeleted(i0 context.Context, i1 string, i2 string, i3 ...Option) (*model.Shift, error) {

	_, span := p.Tracer.Start(i0, "ShiftRepository.FindActiveByIdAndRegionAndNotDeleted")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindActiveByIdAndRegionAndNotDeleted(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.FindActiveByIdAndRegionAndNotDeleted")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyShiftRepository) FindByIdDeletedAtNotExist(i0 context.Context, i1 string, i2 ...Option) (*model.Shift, error) {

	_, span := p.Tracer.Start(i0, "ShiftRepository.FindByIdDeletedAtNotExist")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindByIdDeletedAtNotExist(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.FindByIdDeletedAtNotExist")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyShiftRepository) FindByID(i0 context.Context, i1 string, i2 ...Option) (model.Shift, error) {

	_, span := p.Tracer.Start(i0, "ShiftRepository.FindByID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindByID(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.FindByID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyShiftRepository) FindCurrentActiveByDriverIdAndRegion(i0 context.Context, i1 string, i2 string, i3 ...Option) (*model.Shift, error) {

	_, span := p.Tracer.Start(i0, "ShiftRepository.FindCurrentActiveByDriverIdAndRegion")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindCurrentActiveByDriverIdAndRegion(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.FindCurrentActiveByDriverIdAndRegion")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyShiftRepository) FindExpiredShift(i0 context.Context, i1 time.Time, i2 []string, i3 ...Option) ([]string, error) {

	_, span := p.Tracer.Start(i0, "ShiftRepository.FindExpiredShift")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindExpiredShift(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.FindExpiredShift")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyShiftRepository) FindIncoming(i0 context.Context, i1 []string, i2 string, i3 int, i4 int, i5 ...Option) ([]model.Shift, error) {

	_, span := p.Tracer.Start(i0, "ShiftRepository.FindIncoming")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindIncoming(i0, i1, i2, i3, i4, i5...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.FindIncoming")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyShiftRepository) FindSorted(i0 context.Context, i1 string, i2 string, i3 bool, i4 int, i5 int, i6 time.Time, i7 time.Time, i8 int, i9 int, i10 ...Option) ([]model.Shift, error) {

	_, span := p.Tracer.Start(i0, "ShiftRepository.FindSorted")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindSorted(i0, i1, i2, i3, i4, i5, i6, i7, i8, i9, i10...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.FindSorted")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyShiftRepository) FindStartedOrIncomingForNotification(i0 context.Context, i1 time.Time, i2 time.Time, i3 int, i4 int, i5 ...Option) ([]model.Shift, error) {

	_, span := p.Tracer.Start(i0, "ShiftRepository.FindStartedOrIncomingForNotification")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindStartedOrIncomingForNotification(i0, i1, i2, i3, i4, i5...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.FindStartedOrIncomingForNotification")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyShiftRepository) SaveStartedNotified(i0 context.Context, i1 []model.Shift, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "ShiftRepository.SaveStartedNotified")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SaveStartedNotified(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.SaveStartedNotified")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyShiftRepository) SaveIncomingNotified(i0 context.Context, i1 []model.Shift, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "ShiftRepository.SaveIncomingNotified")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SaveIncomingNotified(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.SaveIncomingNotified")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyShiftRepository) UnBook(i0 context.Context, i1 primitive.ObjectID, i2 string, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "ShiftRepository.UnBook")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UnBook(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.UnBook")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyShiftRepository) UnBookMultipleShift(i0 context.Context, i1 string, i2 []string) error {

	_, span := p.Tracer.Start(i0, "ShiftRepository.UnBookMultipleShift")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UnBookMultipleShift(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.UnBookMultipleShift")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyShiftRepository) UpdateByID(i0 context.Context, i1 string, i2 *model.Shift) error {

	_, span := p.Tracer.Start(i0, "ShiftRepository.UpdateByID")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateByID(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ShiftRepository.UpdateByID")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
