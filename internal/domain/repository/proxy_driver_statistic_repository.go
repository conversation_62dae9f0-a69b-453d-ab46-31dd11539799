// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyDriverStatisticRepository(delegate DriverStatisticRepository, meter metric.Meter) *ProxyDriverStatisticRepository {
	return &ProxyDriverStatisticRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyDriverStatisticRepository-tracer"),
	}
}

type ProxyDriverStatisticRepository struct {
	Delegate         DriverStatisticRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyDriverStatisticRepository) Create(i0 context.Context, i1 *model.DriverStatistic) error {

	_, span := p.Tracer.Start(i0, "DriverStatisticRepository.Create")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Create(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverStatisticRepository.Create")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverStatisticRepository) FindByDriverID(i0 context.Context, i1 string, i2 ...Option) (*model.DriverStatistic, error) {

	_, span := p.Tracer.Start(i0, "DriverStatisticRepository.FindByDriverID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindByDriverID(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverStatisticRepository.FindByDriverID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverStatisticRepository) FindByDriverIDs(i0 context.Context, i1 []string) ([]model.DriverStatistic, error) {

	_, span := p.Tracer.Start(i0, "DriverStatisticRepository.FindByDriverIDs")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindByDriverIDs(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverStatisticRepository.FindByDriverIDs")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverStatisticRepository) Update(i0 context.Context, i1 *model.DriverStatistic) error {

	_, span := p.Tracer.Start(i0, "DriverStatisticRepository.Update")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Update(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverStatisticRepository.Update")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverStatisticRepository) Delete(i0 context.Context, i1 string, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "DriverStatisticRepository.Delete")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Delete(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverStatisticRepository.Delete")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
