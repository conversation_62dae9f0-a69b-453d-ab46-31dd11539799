package repository

//go:generate mockgen -source=./assignment_log_repository.go -destination=./mock_repository/mock_assignment_log_repository.go -package=mock_repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

// AssignmentLogRepository provides functionality to log drivers record on each order.
// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type AssignmentLogRepository interface {
	FindOneActive(ctx context.Context, orderID string, opts ...Option) (model.AssignmentLogRecord, error)

	// AssignToDrivers log which received distributed.
	AssignToDrivers(ctx context.Context, r int, orderID string, deliveringRound int, driverDists []DriverDistance, option ...model.AssignmentLogOpt) (*model.AssignmentLogRecord, error)

	// UnassingToDriver log which added to assign log.
	UnassignToDriver(ctx context.Context, orderID string, deliveringRound int, driverID string, option ...model.AssignmentLogOpt) error

	// AssignedDrivers get drivers who received assign for this order.
	AssignedDrivers(ctx context.Context, orderID string, opts ...Option) ([]model.Record, error)

	// AutoAssignedDrivers get drivers who received auto assigned for this order and also the search rider strategy that has been used
	AutoAssignedDrivers(ctx context.Context, orderID string) ([]model.Record, model.SearchRiderStrategy, error)

	SoftDelete(ctx context.Context, orderID string, deliveringRound int) error

	IsAutoAssigned(ctx context.Context, orderID string, driverID string) (bool, error)

	FilterOrdersAcceptableByRider(ctx context.Context, orderIDs []string, driverID string, opts ...Option) ([]string, error)

	// CountAssignmentLogByDriverID counts assignment logs with specific driver and duration
	CountAssignmentLogByDriverID(ctx context.Context, driverID string, duration time.Duration, opts ...Option) (int, error)

	// UpdateDriverNotAcceptAutoAssignedOrder update assignment log for driver who does not accept order in assigning time
	UpdateDriverNotAcceptAutoAssignedOrder(ctx context.Context, orderID string, driverID string) error

	UpdateDriverTryAcceptAutoAssignedOrder(ctx context.Context, orderID string, driverID string) error

	IllegalDriverIDs(ctx context.Context, orderID string, opts ...Option) (types.StringSet, error)

	InsertIllegalDriver(ctx context.Context, orderID string, deliveringRound int, record model.IllegalDriverRecord) error

	CountOrdersByDriverIDSince(ctx context.Context, driverID string, since time.Time, opts ...Option) (int, error)

	// GetManyAssignedDrivers used to get many assigned driver IDs for many order IDs
	GetManyAssignedDrivers(ctx context.Context, orderIDs []string, opts ...Option) ([]model.AssignmentLogRecord, error)
}

type DriverDistance struct {
	DriverID string
	// Distance in meter
	Distance float64
}
