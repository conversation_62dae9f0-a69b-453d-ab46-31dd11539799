// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyPdpaRepository(delegate PdpaRepository, meter metric.Meter) *ProxyPdpaRepository {
	return &ProxyPdpaRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyPdpaRepository-tracer"),
	}
}

type ProxyPdpaRepository struct {
	Delegate         PdpaRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyPdpaRepository) Upsert(i0 context.Context, i1 *model.Pdpa) error {

	_, span := p.Tracer.Start(i0, "PdpaRepository.Upsert")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Upsert(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "PdpaRepository.Upsert")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyPdpaRepository) FindByDriverId(i0 context.Context, i1 string, i2 ...Option) (*model.Pdpa, error) {

	_, span := p.Tracer.Start(i0, "PdpaRepository.FindByDriverId")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindByDriverId(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "PdpaRepository.FindByDriverId")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
