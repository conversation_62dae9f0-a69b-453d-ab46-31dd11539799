// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyTermAndConditionRepository(delegate TermAndConditionRepository, meter metric.Meter) *ProxyTermAndConditionRepository {
	return &ProxyTermAndConditionRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyTermAndConditionRepository-tracer"),
	}
}

type ProxyTermAndConditionRepository struct {
	Delegate         TermAndConditionRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyTermAndConditionRepository) Create(i0 context.Context, i1 model.TermAndCondition) error {

	_, span := p.Tracer.Start(i0, "TermAndConditionRepository.Create")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Create(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "TermAndConditionRepository.Create")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyTermAndConditionRepository) FindLatestByDriverID(i0 context.Context, i1 string, i2 ...Option) (*model.TermAndCondition, error) {

	_, span := p.Tracer.Start(i0, "TermAndConditionRepository.FindLatestByDriverID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindLatestByDriverID(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "TermAndConditionRepository.FindLatestByDriverID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
