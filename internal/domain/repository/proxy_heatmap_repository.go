// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyHeatMapRepository(delegate HeatMapRepository, meter metric.Meter) *ProxyHeatMapRepository {
	return &ProxyHeatMapRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyHeatMapRepository-tracer"),
	}
}

type ProxyHeatMapRepository struct {
	Delegate         HeatMapRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyHeatMapRepository) Find(i0 HeatMapQuery, i1 ...Option) ([]model.HeatMap, error) {

	start := time.Now()

	r0, r1 := p.Delegate.Find(i0, i1...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "HeatMapRepository.Find")

	return r0, r1
}

func (p *ProxyHeatMapRepository) FindByH3IDIn(i0 []string, i1 ...Option) ([]model.HeatMap, error) {

	start := time.Now()

	r0, r1 := p.Delegate.FindByH3IDIn(i0, i1...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "HeatMapRepository.FindByH3IDIn")

	return r0, r1
}

func (p *ProxyHeatMapRepository) UpsertAll(i0 []model.HeatMap) error {

	start := time.Now()

	r0 := p.Delegate.UpsertAll(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "HeatMapRepository.UpsertAll")

	return r0
}

func (p *ProxyHeatMapRepository) DeleteByHourOfWeekday(i0 int, i1 int) error {

	start := time.Now()

	r0 := p.Delegate.DeleteByHourOfWeekday(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "HeatMapRepository.DeleteByHourOfWeekday")

	return r0
}

func (p *ProxyHeatMapRepository) DeleteByCreatedAtLte(i0 time.Time) error {

	start := time.Now()

	r0 := p.Delegate.DeleteByCreatedAtLte(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "HeatMapRepository.DeleteByCreatedAtLte")

	return r0
}
