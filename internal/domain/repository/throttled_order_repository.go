package repository

//go:generate mockgen -source=./throttled_order_repository.go -destination=./mock_repository/mock_throttled_order_repository.go -package=mock_repository

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type ThrottledOrderRepository interface {
	Find(ctx context.Context, query model.ThrottledOrderQuery, skip int, limit int, opts ...Option) ([]model.ThrottledOrder, error)
	FindOneByMpID(ctx context.Context, mpID string, opts ...Option) (*model.ThrottledOrder, error)
	SetProcessedAt(ctx context.Context, orders []model.ThrottledOrder, date time.Time) error
	InsertOrder(ctx context.Context, order model.Order, zoneID primitive.ObjectID, now time.Time) error
	InsertDeferredOrder(ctx context.Context, order model.Order, zoneID primitive.ObjectID, shouldPickupAt time.Time) error
	UpdateMpShouldPickupAt(ctx context.Context, mpID string, shouldPickupAt time.Time) error
	UpdateMpShouldPickupAtAndZoneID(ctx context.Context, mpID string, shouldPickupAt time.Time, zoneID primitive.ObjectID) error
	UpdateMpShouldPickupAtAndInvalidate(ctx context.Context, mpID string, shouldPickupAt time.Time) error
}
