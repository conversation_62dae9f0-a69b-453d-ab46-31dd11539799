package repository_test

import (
	"testing"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

func TestValidateQuery(t *testing.T) {
	t.Run("should error", func(t *testing.T) {
		type dummy map[string]any
		m1 := bson.M{}
		m2 := map[string]any{}
		var m3 dummy
		m4 := []any{}

		var err error
		_, err = repository.ValidateQuery(m1)
		require.Error(t, err)
		_, err = repository.ValidateQuery(m2)
		require.Error(t, err)

		_, err = repository.ValidateQueryAny(m1)
		require.Error(t, err)
		_, err = repository.ValidateQueryAny(m2)
		require.Error(t, err)
		_, err = repository.ValidateQueryAny(m3)
		require.Error(t, err)
		_, err = repository.ValidateQueryAny(m4)
		require.Error(t, err)
	})
}
