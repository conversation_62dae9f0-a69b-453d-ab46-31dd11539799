package repository

//go:generate mockgen -source=./service_area_repository.go -destination=./mock_repository/mock_service_area_repository.go -package=mock_repository

import (
	"context"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

var (
	ErrServiceAreaRegionDuplicate = errors.New("region is duplicate")
)

type ServiceAreaQuery struct {
	Region string
}

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type ServiceAreaRepository interface {
	Create(ctx context.Context, model *model.ServiceArea) error
	Get(ctx context.Context, id string) (*model.ServiceArea, error)
	GetByRegion(ctx context.Context, region string) (*model.ServiceArea, error)
	GetByRegionWithoutCache(ctx context.Context, region string) (*model.ServiceArea, error)
	GetAutoAssignRegion(ctx context.Context) ([]string, error)
	FindWithQueryAndSort(ctx context.Context, query ServiceAreaQuery, skip, limit int, sort []string, opts ...Option) ([]model.ServiceArea, error)
	CountWithQuery(ctx context.Context, query ServiceAreaQuery, opts ...Option) (int, error)
	FindByRegions(ctx context.Context, regions []string) ([]model.ServiceArea, error)
	Update(ctx context.Context, model *model.ServiceArea) error
	UpdateSet(ctx context.Context, region string, set bson.M) error
	SetShiftDriverIDs(ctx context.Context, serviceAreaID model.ServiceAreaID, region string, shiftDriverIDs []string) error
	Delete(ctx context.Context, model *model.ServiceArea) error
}
