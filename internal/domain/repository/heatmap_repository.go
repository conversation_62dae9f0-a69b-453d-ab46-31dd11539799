package repository

//go:generate mockgen -source=./heatmap_repository.go -destination=./mock_repository/mock_heatmap_repository.go -package=mock_repository

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type HeatMapRepository interface {
	Find(query HeatMapQuery, opts ...Option) ([]model.HeatMap, error)
	FindByH3IDIn(h3ids []string, opts ...Option) ([]model.HeatMap, error)
	UpsertAll(listHeatMap []model.HeatMap) error
	DeleteByHourOfWeekday(weekday int, hour int) error
	DeleteByCreatedAtLte(time time.Time) error
}

type HeatMapQuery struct {
	Lat                       float64   `form:"lat" binding:"required"`
	Lng                       float64   `form:"lng" binding:"required"`
	Time                      time.Time `form:"time" binding:"omitempty"`
	DistanceInMeters          int       `form:"distanceInMeters" binding:"omitempty,min=1"`
	OnTopFareDistanceInMeters int       `form:"onTopFareDistanceInMeters" binding:"omitempty,min=1"`
	Region                    string
}
