package repository

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

//go:generate mockgen -source=./summary_of_change_repository.go -destination=./mock_repository/mock_summary_of_change_repository.go -package=mock_repository

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type SummaryOfChangeRepository interface {
	Create(ctx context.Context, summary *model.SummaryOfChange) error
	Update(ctx context.Context, summary *model.SummaryOfChange) error
	Get(ctx context.Context, id string) (*model.SummaryOfChange, error)
	FindWithQueryAndSortSummaryOfChange(ctx context.Context, query interface{}, skip, limit int, opts ...Option) ([]model.SummaryOfChange, error)
	FindActiveSummaryOfChanges(ctx context.Context, opts ...Option) ([]model.SummaryOfChange, error)
	Archived(ctx context.Context, id, updatedBy string) error
	GetLatestSummaryOfChange(ctx context.Context, opts ...Option) (*model.SummaryOfChange, error)
}

type SummaryOfChangeQuery struct {
	Status model.SummaryOfChangeStatus
}

func NewSummaryOfChangeQuery() *SummaryOfChangeQuery {
	return &SummaryOfChangeQuery{}
}

func (q *SummaryOfChangeQuery) Query() bson.M {
	query := bson.M{}

	if q.Status.ToString() != "" {
		query["status"] = q.Status.ToString()
	}

	return query
}

func (q *SummaryOfChangeQuery) SetStatus(status model.SummaryOfChangeStatus) *SummaryOfChangeQuery {
	q.Status = status
	return q
}
