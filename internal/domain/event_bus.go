package domain

import (
	"context"

	"github.com/pkg/errors"
)

//go:generate mockgen -source=event_bus.go -destination=./mock_event_bus/mock_event_bus.go -package=mock_event_bus

var (
	ErrFailToPublishMessage = errors.New("fail to publish message")
)

type EventBus interface {
	Publish(ctx context.Context, topic string, data []byte, opts ...PublishOption) error
}

type PublishOption func(*PublishConfig)

type PublishConfig struct {
	IsAsync bool
}
