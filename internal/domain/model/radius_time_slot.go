package model

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type RadiusTimeSlots []RadiusTimeSlot

func (rtss RadiusTimeSlots) GetOverriddenKM(currentTime time.Time) (bool, float64) {
	for _, timeSlot := range rtss {
		// Overlapped time slot is not allowed, therefore we could just return the slot after finding one
		if timeSlot.IsIn(currentTime) && timeSlot.MaxRadiusInKm != 0 {
			return true, timeSlot.MaxRadiusInKm
		}
	}
	return false, 0
}

type RadiusTimeSlot struct {
	Time          StartEndTime `json:"time" binding:"required" bson:"time"`
	MaxRadiusInKm float64      `json:"maxRadiusInKm" binding:"required,gt=0" bson:"max_radius_in_km"`
}

func (rts RadiusTimeSlot) IsIn(t time.Time) bool {
	return IsInStartEndTimeCondition(t.In(timeutil.BangkokLocation()), []StartEndTime{rts.Time})
}

type RadiusTimeSlotsWithServiceTypes []RadiusTimeSlotWithServiceTypes

func (rtss RadiusTimeSlotsWithServiceTypes) GetOverriddenKM(currentTime time.Time, serviceType Service) (bool, float64) {
	for _, timeSlot := range rtss {
		// Overlapped time slot is not allowed, therefore we could just return the slot after finding one
		if serviceType.IsValid() && timeSlot.IsIn(currentTime) {
			for _, radiusByServiceType := range timeSlot.RadiiByServiceType {
				if radiusByServiceType.ServiceType == serviceType && radiusByServiceType.MaxRadiusInKm != 0 {
					return true, radiusByServiceType.MaxRadiusInKm
				}
			}
		}
	}
	return false, 0
}

type RadiusTimeSlotWithServiceTypes struct {
	Time               StartEndTime          `json:"time" binding:"required" bson:"time"`
	RadiiByServiceType []RadiusByServiceType `json:"radiiByServiceType" binding:"required,gt=0,unique=ServiceType" bson:"radii_by_service_type"`
}

func (rts RadiusTimeSlotWithServiceTypes) IsIn(t time.Time) bool {
	return IsInStartEndTimeCondition(t.In(timeutil.BangkokLocation()), []StartEndTime{rts.Time})
}

type RadiusByServiceType struct {
	ServiceType   Service `json:"serviceType" binding:"required" bson:"service_type"`
	MaxRadiusInKm float64 `json:"maxRadiusInKm" binding:"required,min=1" bson:"max_radius_in_km"`
}
