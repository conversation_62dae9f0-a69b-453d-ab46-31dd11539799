package model

type MapRoute struct {
	// Distance is the distance traveled by the route.
	Distance float64 `json:"distance"`

	// Duration is estimated travel time.
	Duration float64 `json:"duration"`

	// IsOverridden is the flag that tells whether the distance and duration have been overridden
	IsOverridden bool `json:"-"`

	// BeforeOverriddenDistance is the distance before overridden
	BeforeOverriddenDistance float64 `json:"-"`
}

type MapWaypoint struct {
	Distance float64 `json:"distance"`
}
