package model

import "fmt"

type CounterName string

const (
	DriverEtaxInvoiceCounter           CounterName = "DRIVER_ETAX_INVOICE"
	DriverEtaxInvoiceCreditNoteCounter CounterName = "DRIVER_ETAX_INVOICE_CREDIT_NOTE"
)

type Counter struct {
	ID  string `bson:"_id,omitempty"`
	Seq int64  `bson:"seq,omitempty"`
}

func (c CounterName) ToString() string {
	return string(c)
}

func (c CounterName) WithFormat(format string) CounterName {
	return CounterName(fmt.Sprintf("%s_%s", c, format))
}
