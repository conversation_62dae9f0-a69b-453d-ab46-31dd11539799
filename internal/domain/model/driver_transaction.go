package model

import (
	"errors"
	"fmt"
	"sort"
	"time"

	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var (
	ErrorWalletNotEnough         = errors.New("wallet not enough")
	ErrorCreditNotEnough         = errors.New("credit not enough")
	ErrorCreditOutstanding       = errors.New("credit is outstanding")
	ErrorCategoryWalletRequire   = errors.New("transaction category must be wallet")
	ErrorTypeWithdrawRequire     = errors.New("transaction type must be withdraw")
	ErrorStatusProcessingRequire = errors.New("transaction status must be processing")
	ErrorBanWithdraw             = errors.New("withdrawing has been banned")
)

type lessFunc func(p1, p2 *TransactionInfo) bool

var expiredTimeLess = func(p1, p2 *TransactionInfo) bool {
	return p1.ExpiredDate.Before(p2.ExpiredDate)
}

type TransactionSort struct {
	transactions []TransactionInfo
	lesses       []lessFunc
}

func OrderBy(lesses ...lessFunc) *TransactionSort {
	return &TransactionSort{lesses: lesses}
}

func (ms *TransactionSort) Sort(transactions []TransactionInfo) {
	ms.transactions = transactions
	sort.Sort(ms)
}

func (ms *TransactionSort) Len() int {
	return len(ms.transactions)
}

func (ms *TransactionSort) Swap(i, j int) {
	ms.transactions[i], ms.transactions[j] = ms.transactions[j], ms.transactions[i]
}

func (ms *TransactionSort) Less(i, j int) bool {
	p, q := &ms.transactions[i], &ms.transactions[j]

	for _, lessFunc := range ms.lesses {
		switch {
		case lessFunc(p, q):
			return true
		case lessFunc(q, p):
			return false
		}
	}

	return true
}

type conditionFunc func(TransactionInfo) bool

var expiredTimeCond = func(info TransactionInfo) bool {
	return info.ExpiredDate.After(time.Now().In(info.ExpiredDate.Location()))
}

var amountCond = func(info TransactionInfo) bool {
	return info.Amount.GT(0)
}

type transactionFilter struct {
	conditions []conditionFunc
}

func FilterBy(conditions ...conditionFunc) *transactionFilter {
	return &transactionFilter{conditions: conditions}
}

func (tf *transactionFilter) Filter(transInfos []TransactionInfo) []TransactionInfo {
	result := make([]TransactionInfo, 0, len(transInfos))

	for _, info := range transInfos {
		isPassed := true
		for _, cond := range tf.conditions {
			if !cond(info) {
				isPassed = false
				break
			}
		}

		if isPassed {
			result = append(result, info)
		}
	}

	return result
}

func (tf *transactionFilter) Split(transInfos []TransactionInfo) (passes []TransactionInfo, fails []TransactionInfo) {
	passes = make([]TransactionInfo, 0, len(transInfos))
	fails = make([]TransactionInfo, 0, len(transInfos))

	for _, info := range transInfos {
		isPassed := true
		for _, cond := range tf.conditions {
			if !cond(info) {
				isPassed = false
				break
			}
		}

		if isPassed {
			passes = append(passes, info)
		} else {
			fails = append(fails, info)
		}
	}

	return passes, fails
}

var filterInfo = FilterBy(amountCond)

type transactionList []TransactionInfo

func (t *transactionList) Add(info TransactionInfo) {
	result := append(*t, info)
	*t = filterInfo.Filter(result)
}

func (t *transactionList) Deduct(amount types.Money) types.Money {
	t.loop(func(info *TransactionInfo) bool {
		info.Amount, amount = info.Amount.Sub(amount), amount.Sub(info.Amount)
		return amount.GT(0)
	})

	*t = filterInfo.Filter(*t)

	if amount.LTE(0) {
		return 0
	} else {
		return amount
	}
}

func (t transactionList) Total() types.Money {
	total := types.NewMoney(0.0)

	t.loop(func(info *TransactionInfo) bool {
		total = total.Add(info.Amount)
		return true
	})

	return total
}

func (t *transactionList) RemoveAllExpired() []TransactionInfo {
	filterExpired := FilterBy(expiredTimeCond)
	passes, removes := filterExpired.Split(*t)

	*t = passes

	return removes
}

func (t transactionList) SetExpiredDate(index int, expireDate time.Time) bool {
	if len(t) <= index || index < 0 {
		return false
	}

	fc := t[index]
	fc.ExpiredDate = expireDate
	t[index] = fc

	return true
}

func (t *transactionList) loop(do func(info *TransactionInfo) bool) {
	transList := *t
	for index, size := 0, len(transList); index < size; index++ {
		if expiredTimeCond(transList[index]) {
			if !do(&transList[index]) {
				return
			}
		}
	}
}

type BanWithdrawDetail struct {
	Reason  string    `bson:"reason"`
	BanAt   time.Time `bson:"ban_at"`
	OrderID string    `bson:"order_id"`
	BanBy   string    `bson:"ban_by"`
}

type NegativeCreditDetail struct {
	NegativeCreditStartTime *time.Time `bson:"negative_credit_start_time"`
	IsCreditNegative        bool       `bson:"is_credit_negative"`
}

type DriverTransaction struct {
	DriverID               string               `bson:"driver_id" json:"driverId"`
	FreeCreditTransactions transactionList      `bson:"free_credit_transactions"`
	PurchaseCreditBalance  types.Money          `bson:"credit_balance"`
	WalletBalance          types.Money          `bson:"wallet_balance"`
	InstallmentAmount      types.Money          `bson:"installment_amount"`
	BanWithdrawDetail      BanWithdrawDetail    `bson:"ban_withdraw_detail,omitempty"`
	Cash                   types.Money          `bson:"cash"`
	NegativeCreditDetail   NegativeCreditDetail `bson:"negative_credit_detail"`
	PositiveCreditBalance  types.Money          `bson:"positive_credit_balance"`
	CreatedAt              time.Time            `bson:"created_at"`
	UpdatedAt              time.Time            `bson:"updated_at"`
}

func NewDriverTransaction(driverId string) *DriverTransaction {
	return &DriverTransaction{
		DriverID:               driverId,
		FreeCreditTransactions: make([]TransactionInfo, 0, 20),
		PurchaseCreditBalance:  0.0,
		WalletBalance:          0.0,
		BanWithdrawDetail:      BanWithdrawDetail{},
		CreatedAt:              timeutil.BangkokNow(),
		NegativeCreditDetail:   NegativeCreditDetail{IsCreditNegative: false, NegativeCreditStartTime: nil},
		PositiveCreditBalance:  0.0,
	}
}

func (dt *DriverTransaction) AddTransaction(transactions []TransactionInfo) ([]TransactionInfo, error) {
	return dt.AddTransactionWithReference(transactions, "")
}

func (dt *DriverTransaction) AddTransactionWithReference(transactions []TransactionInfo, referenceID crypt.EncryptedString) ([]TransactionInfo, error) {
	result := make([]TransactionInfo, 0, 10)
	var (
		creditOutstandingTxn *TransactionInfo
		walletOutstandingTxn *TransactionInfo
	)

	for i, transaction := range transactions {
		if transaction.Amount.LTE(0) {
			logrus.Warnf("transaction lte 0 %f", transaction.Amount.Float64())
			continue
		}

		if referenceID != "" {
			transaction.TransRefID = referenceID
		}

		transaction.WalletBalance = dt.WalletBalance
		transaction.CreditBalance = dt.CreditBalance()
		transaction.InstallmentAmount = dt.InstallmentAmount

		if CreditTransactionCategory == transaction.Category {
			switch transaction.Type {
			case PurchaseTransactionType, VoidReturnCreditTransactionType, DeductVoidFraudTransactionType, AdjustNormalOrderTripWageTransactionType:
				result = append(result, dt.addCredit(transaction)...)
			case VoidTransactionType, WithholdingTransactionType, CommissionTransactionType, ItemFeeTransactionType,
				ChargeTransactionType, AddVoidFraudTransactionType, UserDeliveryFeeType, WithdrawalFeeTransactionType,
				PenaltyChargeTransactionType:
				switch transaction.SubType {
				case WithdrawErrorRecallSubType:
					result = append(result, dt.deductPurchaseCredit(transaction)...)
				default:
					result = append(result, dt.deductCredit(transaction)...)
				}
			case WithdrawTransactionType:
				if dt.PurchaseCreditBalance.Sub(transaction.Amount).LT(0) {
					return nil, ErrorCreditNotEnough
				}
				result = append(result, dt.deductPurchaseCredit(transaction)...)
			case InstallmentDeductTransactionType:
				result = append(result, dt.deductCredit(transaction)...)
			case AddCreditRefinanceTransactionType:
				result = append(result, dt.addCredit(transaction)...)
			default:
				switch transaction.Operator {
				case AdditionOperator:
					result = append(result, dt.addCredit(transaction)...)
				case SubtractionOperator:
					result = append(result, dt.deductCredit(transaction)...)
				}
			}

			if dt.CreditBalance().LT(0) {
				t := transaction
				creditOutstandingTxn = &t
			} else {
				creditOutstandingTxn = nil
			}
		} else if WalletTransactionCategory == transaction.Category {
			switch transaction.Type {
			case WithdrawTransactionType:
				if dt.IsBanWithdraw() {
					return nil, ErrorBanWithdraw
				}
				result = append(result, dt.deductWallet(transaction)...)
			case CouponTransactionType, SubsidizeTransactionType, IncentiveTransactionType, NewRiderIncentiveTransactionType,
				VoidTransactionType, CompensationTransactionType, ClaimTransactionType, DeliveryFeeTransactionType, CashAdvanceCouponTransactionType,
				RiderReferralIncentiveTransactionType, AddTransactionType, OtherIncentiveTransactionType, AddVoidFraudTransactionType, OnTopTransactionType,
				DriverWageType, ItemFeeTransactionType, TipTransactionType, AdditionalServiceFeeTransactionType, GoodwillTransactionType:
				result = append(result, dt.addWallet(transaction)...)
			case DeductVoidTransactionType, DeductVoidFraudTransactionType, AdjustNormalOrderTripWageTransactionType, ChargeTransactionType, PenaltyChargeTransactionType:
				result = append(result, dt.deductWallet(transaction)...)
			case InstallmentDeductTransactionType:
				result = append(result, dt.deductWallet(transaction)...)
			default:
				switch transaction.Operator {
				case AdditionOperator:
					result = append(result, dt.addWallet(transaction)...)
				case SubtractionOperator:
					result = append(result, dt.deductWallet(transaction)...)
				}
			}

			if dt.WalletBalance.LT(0) {
				t := transaction
				walletOutstandingTxn = &t
			} else {
				walletOutstandingTxn = nil
			}
		}

		transactions[i] = transaction
		if transaction.WalletAfter.Equal(0) {
			transaction.WalletAfter = dt.WalletBalance
		}
		if transaction.CreditAfter.Equal(0) {
			transaction.CreditAfter = dt.CreditBalance()
		}
	}

	if creditOutstandingTxn != nil {
		outstandingTxn := *NewOutstandingTransactionInfo(
			CreditTransactionCategory,
			creditOutstandingTxn.DriverID,
			creditOutstandingTxn.TransRefID,
			dt.CreditBalance().Abs(),
		)

		if creditOutstandingTxn.OrderID != "" || creditOutstandingTxn.TripID != "" {
			outstandingTxn.OrderID = creditOutstandingTxn.OrderID
			outstandingTxn.TripID = creditOutstandingTxn.TripID
		}

		outstandingTxn.WalletAfter = dt.WalletBalance
		outstandingTxn.CreditAfter = dt.CreditBalance()
		outstandingTxn.InstallmentAfter = dt.InstallmentAmount

		result = append(result, outstandingTxn)
	}

	if walletOutstandingTxn != nil {
		outstandingTxn := *NewOutstandingTransactionInfo(
			WalletTransactionCategory,
			walletOutstandingTxn.DriverID,
			walletOutstandingTxn.TransRefID,
			dt.WalletBalance.Abs(),
		)

		if walletOutstandingTxn.OrderID != "" || walletOutstandingTxn.TripID != "" {
			outstandingTxn.OrderID = walletOutstandingTxn.OrderID
			outstandingTxn.TripID = walletOutstandingTxn.TripID
		}

		outstandingTxn.WalletAfter = dt.WalletBalance
		outstandingTxn.CreditAfter = dt.CreditBalance()
		outstandingTxn.InstallmentAfter = dt.InstallmentAmount

		result = append(result, outstandingTxn)
	}

	for i := range result {
		if result[i].Operator != TransactionSchemeOperator("") ||
			result[i].Type == OutstandingTransactionType {
			continue
		}
		switch result[i].Category {
		case WalletTransactionCategory:
			result[i].Operator.CompareMoney(result[i].WalletBalance, result[i].WalletAfter)
		case CreditTransactionCategory:
			result[i].Operator.CompareMoney(result[i].CreditBalance, result[i].CreditAfter)
		}
	}
	return result, nil
}

func (dt *DriverTransaction) CompleteWithdraw(txn *Transaction) error {
	if err := dt.validateWithdrawTransaction(*txn); err != nil {
		return err
	}

	if txn.Status != ProcessingTransactionStatus {
		return ErrorStatusProcessingRequire
	}

	return txn.SetStatus(SuccessTransactionStatus)
}

func (dt *DriverTransaction) RefundWithdraw(txn *Transaction) ([]TransactionInfo, error) {
	if err := dt.validateWithdrawTransaction(*txn); err != nil {
		return nil, err
	}

	if txn.Status != ProcessingTransactionStatus {
		return nil, ErrorStatusProcessingRequire
	}

	if err := txn.SetStatus(FailTransactionStatus); err != nil {
		return nil, err
	}

	voidTxn := *NewWalletVoidTransaction(txn.TransactionID, txn.Info.DriverID, txn.Info.Amount)
	if txn.Info.WithdrawalFeeCredit.GT(0) {
		voidCreditTxn := *NewVoidReturnCreditTransactionInfo(txn.Info.DriverID, txn.Info.WithdrawalFeeCredit)
		voidCreditTxn.SubType = WithdrawalFailedTransactionSubType
		voidCreditTxn.RefID = txn.Info.WithdrawalFeeCreditRefID
		voidCreditTxn.TransRefID = txn.Info.TransRefID
		return dt.AddTransactionWithReference([]TransactionInfo{voidTxn, voidCreditTxn}, txn.Info.TransRefID)
	}
	return dt.AddTransactionWithReference([]TransactionInfo{voidTxn}, txn.Info.TransRefID)
}

func (dt *DriverTransaction) TransferWalletToCredit(amount types.Money) ([]TransactionInfo, error) {
	remainWallet := dt.WalletBalance.Sub(amount)
	if remainWallet.LT(0) {
		return []TransactionInfo{}, ErrorWalletNotEnough
	}

	withdraw := NewWithdrawWalletTransactionInfo(dt.DriverID, amount)
	withdraw.RequestedTime = time.Now().UTC()
	withdraw.RequestedBy = dt.DriverID
	purchase := NewPurchaseCreditTransactionInfo(dt.DriverID, amount)
	purchase.RequestedTime = time.Now().UTC()
	purchase.RequestedBy = dt.DriverID

	return dt.AddTransaction([]TransactionInfo{*withdraw, *purchase})
}

func (dt *DriverTransaction) WithdrawWallet(amount, fee types.Money, bankInfo DriverTransactionBankInfo) ([]TransactionInfo, error) {
	if dt.WalletBalance.LT(amount) {
		return []TransactionInfo{}, ErrorWalletNotEnough
	}

	// Allow credit to be negative from fee
	// if fee.GT(0) && dt.CreditBalance().LT(fee) {
	// 	return []TransactionInfo{}, ErrorCreditOutstanding
	// }

	withdraw := NewWithdrawWalletTransactionInfo(dt.DriverID, amount)
	withdraw.RequestedTime = time.Now().UTC()
	withdraw.RequestedBy = dt.DriverID
	withdraw.WalletType = FraudTransactionWalletType
	withdraw.WithdrawInfo.SetInfo(bankInfo)
	tranRefID := utils.GenerateUUID()
	if fee.GT(0) {
		feeTran := NewWithdrawFeeCreditTransactionInfo(dt.DriverID, fee)
		uuid := utils.GenerateUUID()
		feeTran.RequestedTime = time.Now().UTC()
		feeTran.RequestedBy = dt.DriverID
		feeTran.WithdrawInfo.SetInfo(bankInfo)
		feeTran.WithdrawalFeeCreditRefID = uuid
		feeTran.SubType = WithdrawalSuccessTransactionSubType
		withdraw.WithdrawalFeeCreditRefID = uuid
		withdraw.WithdrawalFeeCredit = fee

		return dt.AddTransactionWithReference([]TransactionInfo{*withdraw, *feeTran}, crypt.EncryptedString(tranRefID))
	}
	return dt.AddTransactionWithReference([]TransactionInfo{*withdraw}, crypt.EncryptedString(tranRefID))
}

func (dt *DriverTransaction) validateWithdrawTransaction(txn Transaction) error {
	if txn.Info.Category != WalletTransactionCategory {
		return ErrorCategoryWalletRequire
	}
	if txn.Info.Type != WithdrawTransactionType {
		return ErrorTypeWithdrawRequire
	}

	return nil
}

func (dt *DriverTransaction) NewClaimTransaction(amount types.Money) *TransactionInfo {
	return &TransactionInfo{
		Category: WalletTransactionCategory,
		Type:     ClaimTransactionType,
		DriverID: dt.DriverID,
		Amount:   amount,
	}
}

func (dt *DriverTransaction) NewCompensationTransaction(amount types.Money) *TransactionInfo {
	return &TransactionInfo{
		Category: WalletTransactionCategory,
		Type:     CompensationTransactionType,
		DriverID: dt.DriverID,
		Amount:   amount,
	}
}

func (dt *DriverTransaction) NewWithHoldingTaxTransaction(amount types.Money) *TransactionInfo {
	return &TransactionInfo{
		Category: CreditTransactionCategory,
		Type:     WithholdingTransactionType,
		DriverID: dt.DriverID,
		Amount:   amount,
	}
}

func (dt *DriverTransaction) NewIncentiveTransaction(amount types.Money) *TransactionInfo {
	return NewIncentiveTransactionInfo(dt.DriverID, amount, []string{})
}

func (dt *DriverTransaction) NewNewRiderIncentiveTransaction(amount types.Money) *TransactionInfo {
	return NewNewRiderIncentiveTransactionInfo(dt.DriverID, amount, []string{})
}

type FreeCreditTransactionOption struct {
	expirationDate time.Time
}

func WithExpirationDate(expirationDate time.Time) func(*FreeCreditTransactionOption) {
	return func(o *FreeCreditTransactionOption) {
		o.expirationDate = expirationDate
	}
}

func (dt *DriverTransaction) NewFreeCreditTransaction(txnRefID crypt.EncryptedString, amount types.Money, transactionOptions ...func(*FreeCreditTransactionOption)) *TransactionInfo {
	txn := NewFreeCreditTransactionInfo(dt.DriverID, amount, transactionOptions...)
	txn.TransRefID = txnRefID
	return txn
}

func (dt *DriverTransaction) NewCreditTransaction(txnRefID crypt.EncryptedString, amount types.Money) *TransactionInfo {
	txn := NewPurchaseCreditTransactionInfo(dt.DriverID, amount)
	txn.TransRefID = txnRefID
	return txn
}

func (dt *DriverTransaction) NewTransferCreditToWalletTransactionInfos(amount types.Money, requestedBy string) (creditInfo *TransactionInfo, walletInfo *TransactionInfo) {
	timeNow := time.Now().UTC()
	creditInfo = &TransactionInfo{
		Category:      CreditTransactionCategory,
		Type:          WithdrawTransactionType,
		DriverID:      dt.DriverID,
		Amount:        amount,
		RequestedBy:   requestedBy,
		RequestedTime: timeNow,
	}
	walletInfo = &TransactionInfo{
		Category:      WalletTransactionCategory,
		Type:          AddTransactionType,
		DriverID:      dt.DriverID,
		Amount:        amount,
		RequestedBy:   requestedBy,
		RequestedTime: timeNow,
	}
	return
}

func (dt *DriverTransaction) NewCreditChargeTransaction(txnRefID crypt.EncryptedString, amount types.Money, subtype TransactionSubType, orderID string) *TransactionInfo {
	txn := NewChargeTransactionInfo(dt.DriverID, orderID, subtype, amount)
	txn.TransRefID = txnRefID
	return txn
}

func (dt *DriverTransaction) NewWalletCouponTransaction(orderID string, tripID string, code string, amount types.Money) *TransactionInfo {
	return NewCouponTransactionInfo(dt.DriverID, orderID, tripID, code, amount)
}

func (dt *DriverTransaction) NewWalletSubsidizeTransaction(orderID string, category string, amount types.Money) *TransactionInfo {
	return NewSubsidizeTransactionInfo(dt.DriverID, orderID, "", category, amount)
}

func (dt *DriverTransaction) NewWalletIncentiveTransaction(amount types.Money, refsID []string) *TransactionInfo {
	return NewIncentiveTransactionInfo(dt.DriverID, amount, refsID)
}

func (dt *DriverTransaction) NewCashAdvanceCouponTransaction(orderID string, amount types.Money) *TransactionInfo {
	return NewCashAdvanceCouponTransaction(dt.DriverID, orderID, amount)
}

func (dt *DriverTransaction) NewRiderReferralIncentiveTransaction(amount types.Money) *TransactionInfo {
	return NewRiderReferralIncentiveTransaction(dt.DriverID, amount)
}

func (dt *DriverTransaction) NewOtherIncentiveTransaction(amount types.Money) *TransactionInfo {
	return NewOtherIncentiveTransactionInfo(dt.DriverID, amount)
}

func (dt *DriverTransaction) NewChargeTransaction(amount types.Money, orderID string, subType TransactionSubType) *TransactionInfo {
	return NewChargeTransactionInfo(dt.DriverID, orderID, subType, amount)
}

func (dt *DriverTransaction) NewPenaltyChargeTransaction(amount types.Money, orderID, tripID string, subType TransactionSubType) (*TransactionInfo, error) {
	switch subType {
	case PenaltyChargeCouponTransactionSubType:
		return NewPenaltyChargeCouponTransactionInfo(dt.DriverID, orderID, tripID, amount), nil
	case PenaltyChargeItemFeeTransactionSubType:
		return NewPenaltyChargeItemFeeTransactionInfo(dt.DriverID, orderID, tripID, amount), nil
	case PenaltyChargeOnTopTransactionSubType:
		return NewPenaltyChargeOnTopTransactionInfo(dt.DriverID, orderID, tripID, amount), nil
	case PenaltyChargeWageTransactionSubType:
		return NewPenaltyChargeWageTransactionInfo(dt.DriverID, orderID, tripID, amount), nil
	default:
		return nil, fmt.Errorf("unknown penalty charge sub type %s", subType)
	}
}

func (dt DriverTransaction) NewTransactionFromScheme(ts TransactionScheme, orderID string, amount types.Money) *TransactionInfo {
	return NewTransactionInfoFromScheme(ts, dt.DriverID, orderID, amount)
}

func (dt *DriverTransaction) NewInstallmentDeductTransaction(amount types.Money, installmentID string, displayText string) ([]TransactionInfo, error) {
	transRefID := crypt.EncryptedString(utils.GenerateUUID())
	var (
		installmentDeductWallet *TransactionInfo
		installmentDeductCredit *TransactionInfo
	)

	var responseInfos []TransactionInfo
	if dt.WalletBalance.GTE(amount) {
		installmentDeductWallet = NewInstallmentDeductInfo(WalletTransactionCategory, dt.DriverID, transRefID, amount, installmentID)
		responseInfos = []TransactionInfo{*installmentDeductWallet}
	} else {
		walletDeduct, creditDeduct := types.Money(0), types.Money(0)
		if dt.WalletBalance.GT(0) {
			walletDeduct = dt.WalletBalance
			installmentDeductWallet = NewInstallmentDeductInfo(WalletTransactionCategory, dt.DriverID, transRefID, walletDeduct, installmentID)
		}
		creditDeduct = amount.Sub(walletDeduct)
		installmentDeductCredit = NewInstallmentDeductInfo(CreditTransactionCategory, dt.DriverID, transRefID, creditDeduct, installmentID)
		if installmentDeductWallet == nil {
			responseInfos = []TransactionInfo{*installmentDeductCredit}
		} else {
			responseInfos = []TransactionInfo{*installmentDeductWallet, *installmentDeductCredit}
		}
	}

	for i := range responseInfos {
		responseInfos[i].DisplayText = displayText
	}
	return dt.AddTransaction(responseInfos)
}

func (dt *DriverTransaction) ReturnCredit(amount types.Money, installmentID string) ([]TransactionInfo, error) {
	returnCredit := NewReturnCreditInfo(dt.DriverID, amount, installmentID)
	returnCredit.DisplayText = string(AdjustNegativeCreditTransactionDisplayText)
	return dt.AddTransaction([]TransactionInfo{*returnCredit})
}

func (dt *DriverTransaction) RefundWalletInstallmentCancellation(amount types.Money, installmentID string) ([]TransactionInfo, error) {
	refundWallet := NewRefundWalletInstallmentCancellationInfo(dt.DriverID, amount, installmentID)
	return dt.AddTransaction([]TransactionInfo{*refundWallet})
}

func (dt *DriverTransaction) AddCreditInstallmentCancellation(amount types.Money, installmentID string) ([]TransactionInfo, error) {
	addCredit := NewAddCreditInstallmentCancellationInfo(dt.DriverID, amount, installmentID)
	return dt.AddTransaction([]TransactionInfo{*addCredit})
}

// adjustCreditToZero will adjust credit follow addCredit credit in case credit is negative and will
// return remain addCredit credit that left after subtract from remain credit
func (dt *DriverTransaction) adjustCreditToZero(addCredit types.Money) types.Money {
	remainAddCredit := addCredit
	remainCredit := dt.PurchaseCreditBalance

	if remainCredit.GTE(0) {
		return remainAddCredit
	}

	if remainCredit < 0 {
		remainCredit := remainCredit.Add(remainAddCredit)
		if remainCredit.LTE(0) {
			remainAddCredit = 0
		} else {
			remainAddCredit = remainCredit.Abs()
			remainCredit = 0
		}

		dt.PurchaseCreditBalance = remainCredit
	}

	return remainAddCredit
}

func (dt *DriverTransaction) addCredit(transaction TransactionInfo) []TransactionInfo {
	result := make([]TransactionInfo, 0, 3)
	var resultTransaction TransactionInfo

	originalToAddCreditAmount := transaction.Amount
	var operationExpense types.Money
	if dt.CreditBalance().LT(0) {
		// PurchaseCreditBalance is negative and InstallmentAmount is negative
		// PurchaseCreditBalance SHOULD ALWAYS BE GREATER (in term of NEGATIVE) than InstallmentAmount
		operationExpense = dt.PurchaseCreditBalance.Sub(dt.InstallmentAmount).Abs()
	}

	if dt.PurchaseCreditBalance.LT(0) {
		resultTransaction = transaction
		transaction.Amount = dt.adjustCreditToZero(transaction.Amount)
		if FreeTransactionCreditType == transaction.CreditType && transaction.Amount.GT(0) {
			dt.FreeCreditTransactions.Add(transaction)
		} else if PositiveCreditTransactionCreditType == transaction.CreditType && transaction.Amount.GT(0) {
			dt.PositiveCreditBalance = dt.PositiveCreditBalance.Add(transaction.Amount)
		} else {
			dt.PurchaseCreditBalance = dt.PurchaseCreditBalance.Add(transaction.Amount)
		}
	} else {
		if FreeTransactionCreditType == transaction.CreditType {
			dt.FreeCreditTransactions.Add(transaction)
		} else if PositiveCreditTransactionCreditType == transaction.CreditType {
			dt.PositiveCreditBalance = dt.PositiveCreditBalance.Add(transaction.Amount)
		} else {
			dt.PurchaseCreditBalance = dt.PurchaseCreditBalance.Add(transaction.Amount)
		}

		resultTransaction = transaction
	}

	// Avoid operating expenses deductions
	if transaction.Type == AddCreditRefinanceTransactionType || transaction.Type == AddCreditInstallmentCancellationTransactionType {
		operationExpense = 0
	}

	if dt.InstallmentAmount.LT(0) {
		toDeductInstallment := originalToAddCreditAmount.Sub(operationExpense)
		if toDeductInstallment.GT(0) {
			dt.InstallmentAmount = dt.InstallmentAmount.Add(toDeductInstallment)
			if dt.InstallmentAmount.GT(0) {
				dt.InstallmentAmount = 0
			}
		}
	}

	resultTransaction.WalletAfter = dt.WalletBalance
	resultTransaction.CreditAfter = dt.CreditBalance()
	resultTransaction.InstallmentAfter = dt.InstallmentAmount

	result = append(result, resultTransaction)
	return result
}

func (dt *DriverTransaction) deductCredit(transaction TransactionInfo) []TransactionInfo {
	result := make([]TransactionInfo, 0, 3)
	remainAmount := transaction.Amount

	if dt.FreeCreditTransactions.Total().GT(0) {
		remainAmount = dt.FreeCreditTransactions.Deduct(remainAmount)

		voidFreeCreditInfo := transaction
		voidFreeCreditInfo.Amount = transaction.Amount.Sub(remainAmount)
		voidFreeCreditInfo.CreditType = FreeTransactionCreditType

		voidFreeCreditInfo.WalletAfter = dt.WalletBalance
		voidFreeCreditInfo.CreditAfter = dt.CreditBalance()
		voidFreeCreditInfo.InstallmentAfter = dt.InstallmentAmount

		transaction.CreditBalance = dt.CreditBalance()

		result = append(result, voidFreeCreditInfo)
	}

	if dt.PositiveCreditBalance.GT(0) && remainAmount.GT(0) {
		var deductedPositiveCredit types.Money
		remainAmount, deductedPositiveCredit = dt.deductPositiveCredit(remainAmount)

		voidPositiveCreditInfo := transaction
		voidPositiveCreditInfo.Amount = deductedPositiveCredit
		voidPositiveCreditInfo.CreditType = PositiveCreditTransactionCreditType

		voidPositiveCreditInfo.WalletAfter = dt.WalletBalance
		voidPositiveCreditInfo.CreditAfter = dt.CreditBalance()
		voidPositiveCreditInfo.InstallmentAfter = dt.InstallmentAmount

		transaction.CreditBalance = dt.CreditBalance()

		result = append(result, voidPositiveCreditInfo)
	}

	if remainAmount.GT(0) {
		creditBefore := dt.CreditBalance()
		dt.PurchaseCreditBalance = dt.PurchaseCreditBalance.Sub(remainAmount)

		voidCreditInfo := transaction
		voidCreditInfo.Amount = remainAmount
		voidCreditInfo.CreditType = ""

		voidCreditInfo.WalletAfter = dt.WalletBalance
		voidCreditInfo.CreditAfter = dt.CreditBalance()
		voidCreditInfo.InstallmentAfter = dt.InstallmentAmount

		if transaction.Type == InstallmentDeductTransactionType {
			if creditBefore.GT(0) {
				toDeductInstallment := creditBefore.Sub(remainAmount)
				if toDeductInstallment.LT(0) {
					dt.InstallmentAmount = dt.InstallmentAmount.Add(toDeductInstallment)
				}
			} else {
				dt.InstallmentAmount = dt.InstallmentAmount.Sub(remainAmount)
			}
			if dt.CreditBalance().LT(0) {
				if creditBefore.GTE(0) {
					voidCreditInfo.InstallmentAfter = dt.CreditBalance()
				} else {
					voidCreditInfo.InstallmentAfter = dt.InstallmentAmount.Sub(remainAmount)
				}
			}
		}
		result = append(result, voidCreditInfo)
	}

	return result
}

func (dt *DriverTransaction) deductPurchaseCredit(transaction TransactionInfo) []TransactionInfo {
	dt.PurchaseCreditBalance = dt.PurchaseCreditBalance.Sub(transaction.Amount)
	transaction.WalletAfter = dt.WalletBalance
	transaction.CreditAfter = dt.CreditBalance()
	transaction.InstallmentAfter = dt.InstallmentAmount

	return []TransactionInfo{transaction}
}

func (dt *DriverTransaction) deductWallet(transaction TransactionInfo) []TransactionInfo {
	dt.WalletBalance = dt.WalletBalance.Sub(transaction.Amount)
	transaction.WalletAfter = dt.WalletBalance
	transaction.CreditAfter = dt.CreditBalance()
	transaction.InstallmentAfter = dt.InstallmentAmount
	return []TransactionInfo{transaction}
}

func (dt *DriverTransaction) addWallet(transaction TransactionInfo) []TransactionInfo {
	dt.WalletBalance = dt.WalletBalance.Add(transaction.Amount)
	transaction.WalletAfter = dt.WalletBalance
	transaction.CreditAfter = dt.CreditBalance()
	transaction.InstallmentAfter = dt.InstallmentAmount
	return []TransactionInfo{transaction}
}

func (dt DriverTransaction) Balance() types.Money {
	return dt.PurchaseCreditBalance
}

func (dt DriverTransaction) FreeCreditBalance() types.Money {
	return dt.FreeCreditTransactions.Total()
}

func (dt DriverTransaction) CreditBalance() types.Money {
	return dt.PurchaseCreditBalance.Add(dt.FreeCreditBalance()).Add(dt.PositiveCreditBalance)
}

func (dt *DriverTransaction) deductPositiveCredit(amount types.Money) (types.Money, types.Money) {
	var remainAmount types.Money
	dt.PositiveCreditBalance = dt.PositiveCreditBalance.Sub(amount)
	if dt.PositiveCreditBalance.LT(0) {
		remainAmount = dt.PositiveCreditBalance.Abs()
		deductedPositiveCredit := remainAmount.Sub(amount).Abs()
		dt.PositiveCreditBalance = types.NewMoney(0)
		return remainAmount, deductedPositiveCredit
	}
	return types.Money(0), amount
}

func (dt *DriverTransaction) RemoveExpiredCredit() ([]Transaction, error) {
	expiredCredits := dt.FreeCreditTransactions.RemoveAllExpired()

	txns := make([]Transaction, len(expiredCredits))
	for i, expired := range expiredCredits {
		info := NewExpiredTransactionInfo(expired.DriverID, expired.TransRefID, expired.Amount)

		uuid := utils.GenerateUUID()
		txn := *NewTransaction(uuid, SystemTransactionChannel, ExpiredTransactionAction, SuccessTransactionStatus, *info)
		txn.CreatedAt = expired.ExpiredDate

		txns[i] = txn
	}

	return txns, nil
}

func (dt DriverTransaction) FreeCreditTransactionSize() int {
	return len(dt.FreeCreditTransactions)
}

func (dt DriverTransaction) IsBanWithdraw() bool {
	return dt.BanWithdrawDetail != BanWithdrawDetail{}
}

type CreditStatus string

const (
	UnspecifiedCreditStatus                  CreditStatus = "UNSPECIFIED_CREDIT_STATUS"
	CreditNegative                           CreditStatus = "CREDIT_NEGATIVE"
	CreditPositiveButLessThanMinimumRequired CreditStatus = "CREDIT_POSITIVE_LESS_THAN_MINIMUM_REQUIRED"
	CreditPositiveMoreThanMinimumRequired    CreditStatus = "CREDIT_POSITIVE_MORE_THAN_MINIMUM_REQUIRED"
)

func (dt DriverTransaction) GetDriverCreditStatus(minimumCreditRequired types.Money) CreditStatus {
	driverCredit := dt.CreditBalance()
	switch {
	case driverCredit.LT(0):
		return CreditNegative
	case driverCredit.GTE(0) && driverCredit.LT(minimumCreditRequired):
		return CreditPositiveButLessThanMinimumRequired
	case driverCredit.GTE(minimumCreditRequired):
		return CreditPositiveMoreThanMinimumRequired
	default:
		return UnspecifiedCreditStatus
	}
}

// Should be use only for internal API only
func (dt *DriverTransaction) SetFreeCreditExpiredDated(index int, expiredDate time.Time) bool {
	return dt.FreeCreditTransactions.SetExpiredDate(index, expiredDate)
}

func (dt *DriverTransaction) BanWithdraw(reason, orderID, banBy string) {
	dt.BanWithdrawDetail = BanWithdrawDetail{
		Reason:  reason,
		OrderID: orderID,
		BanAt:   time.Now().UTC(),
		BanBy:   banBy,
	}
}

func (dt *DriverTransaction) UnbanWithdraw() {
	dt.BanWithdrawDetail = BanWithdrawDetail{}
}
