package model

import (
	"time"

	"git.wndv.co/lineman/absinthe/crypt"
)

type WithdrawalTransactionResult struct {
	DriverID                   string                    `json:"driverId" bson:"driver_id"`
	Firstname                  crypt.LazyEncryptedString `json:"firstName" bson:"first_name"`
	Lastname                   crypt.LazyEncryptedString `json:"lastName" bson:"last_name"`
	AccountNumber              crypt.LazyEncryptedString `json:"accountNumber" bson:"account_number"`
	AccountName                crypt.LazyEncryptedString `json:"accountName" bson:"account_name"`
	BankCode                   string                    `json:"bankCode" bson:"bank_code"`
	RequestedAmount            string                    `json:"requestedAmount" bson:"requested_amount"`
	TransactionReferenceNumber string                    `json:"transactionReferenceNumber" bson:"transaction_reference_number"`
	BankReferenceNumber        string                    `json:"bankReferenceNumber" bson:"bank_reference_number"`
	UOBMakePayment             string                    `json:"uobMakePayment" bson:"uob_make_payment"`
	UpdateTransactionStatus    string                    `json:"updateTransactionStatus" bson:"update_transaction_status"`
	Remark                     string                    `json:"remark" bson:"remark"`
	MakePaymentErrorCode       string                    `json:"makePaymentErrorCode" bson:"make_payment_error_code"`
	MakePaymentErrorMessage    string                    `json:"makePaymentErrorMessage" bson:"make_payment_error_message"`
	EnquiryErrorCode           string                    `json:"enquiryErrorCode" bson:"enquiry_error_code"`
	EnquiryErrorMessage        string                    `json:"enquiryErrorMessage" bson:"enquiry_error_message"`
	CreatedAt                  time.Time                 `json:"createdAt" bson:"created_at"`
	MakePaymentAt              time.Time                 `json:"makePaymentAt" bson:"make_payment_at,omitempty"`
	SucceedAt                  time.Time                 `json:"succeedAt" bson:"succeed_at,omitempty"`
}
