package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/absinthe/crypt"
)

type WhitelistPhoneMapping struct {
	PhoneNumber crypt.EncryptedString `bson:"phone_number"`
	IsUsed      bool                  `bson:"is_used"`

	RequestedBy string    `bson:"requested_by,omitempty"`
	CreatedAt   time.Time `bson:"created_at,omitempty"`
}

func newWhitelistPhoneMapping(w WhitelistPhone) *WhitelistPhoneMapping {
	return &WhitelistPhoneMapping{
		PhoneNumber: w.PhoneNumber,
		IsUsed:      w.IsUsed,
		RequestedBy: w.RequestedBy,
		CreatedAt:   w.Created<PERSON>t,
	}
}

func (m *WhitelistPhoneMapping) WhitelistPhone() *WhitelistPhone {
	return &WhitelistPhone{
		PhoneNumber: m.PhoneNumber,
		IsUsed:      m.IsUsed,
		RequestedBy: m.RequestedBy,
		CreatedAt:   m.<PERSON>,
	}
}

func (w WhitelistPhone) MarshalBSON() ([]byte, error) {
	return bson.Marshal(newWhitelistPhoneMapping(w))
}

func (w *WhitelistPhone) UnmarshalBSON(data []byte) error {
	mapping := &WhitelistPhoneMapping{}
	if err := bson.Unmarshal(data, mapping); err != nil {
		return err
	}

	tmp := mapping.WhitelistPhone()

	*w = *tmp

	return nil
}
