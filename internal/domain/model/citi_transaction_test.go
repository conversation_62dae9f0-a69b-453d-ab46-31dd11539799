package model

import (
	"testing"
	"time"
)

func TestCitiRawTransactionEntry_parsePostingTimeStamp(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected time.Time
	}{
		{
			name:     "WithoutNanoseconds real data in production db",
			input:    "2024-06-05 13:02:06.254",
			expected: time.Date(2024, 6, 5, 13, 2, 6, 254000000, time.UTC),
		},

		{
			name:     "WithoutNanoseconds",
			input:    "2023-07-14T05:54:42Z",
			expected: time.Date(2023, 7, 14, 5, 54, 42, 0, time.UTC),
		},
		{
			name:     "WithNanoseconds",
			input:    "2023-07-14T05:54:42.7Z",
			expected: time.Date(2023, 7, 14, 5, 54, 42, 700000000, time.UTC),
		},
		{
			name:     "WithNanosecondsPadded",
			input:    "2023-07-14T05:54:42.000Z",
			expected: time.Date(2023, 7, 14, 5, 54, 42, 0, time.UTC),
		},
		{
			name:     "InvalidFormat",
			input:    "invalid format",
			expected: time.Time{},
		},
		{
			name:     "Input: 2023-07-14T05:54:0Z",
			input:    "2023-07-14T05:54:0Z",
			expected: time.Time{},
		},
		{
			name:     "Input: 2023-07-14T05:54:42Z",
			input:    "2023-07-14T05:54:42Z",
			expected: time.Date(2023, 7, 14, 5, 54, 42, 0, time.UTC),
		},
		{
			name:     "Input: 2023-07-14T05:54:42.0Z",
			input:    "2023-07-14T05:54:42.0Z",
			expected: time.Date(2023, 7, 14, 5, 54, 42, 0, time.UTC),
		},
		{
			name:     "Input: 2023-06-14T05:54:42",
			input:    "2023-06-14T05:54:42",
			expected: time.Date(2023, 6, 14, 5, 54, 42, 0, time.UTC),
		},
		{
			name:     "Input: 2023-06-14T05:54:42.122",
			input:    "2023-06-14T05:54:42.122",
			expected: time.Date(2023, 6, 14, 5, 54, 42, 122000000, time.UTC),
		},
		{
			name:     "Input: 2023-06-14T05:54:42.000",
			input:    "2023-06-14T05:54:42.000",
			expected: time.Date(2023, 6, 14, 5, 54, 42, 0, time.UTC),
		},
		{
			name:     "Input: 2023-06-14 05:54:42.111",
			input:    "2023-06-14 05:54:42.111",
			expected: time.Date(2023, 6, 14, 5, 54, 42, 111000000, time.UTC),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entry := CitiRawTransactionEntry{}
			result := entry.parsePostingTimeStamp(tt.input)
			if !result.Equal(tt.expected) {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}
