package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type GroupTransactionMapping struct {
	EntityMapping            `bson:",inline"`
	ID                       string                        `bson:"group_transaction_id"`
	Items                    []GroupTransactionItemMapping `bson:"items"`
	Status                   string                        `bson:"status"`
	RequestedBy              string                        `bson:"requested_by"`
	Action                   TransactionAction             `bson:"action"`
	ChangeLog                []GroupTransactionAuditLog    `bson:"audit_logs"`
	GroupTransactionCategory string                        `bson:"group_transaction_category"`
	EffectiveTime            *time.Time                    `bson:"effective_time,omitempty"`
	ExecutedAt               *time.Time                    `bson:"executed_at,omitempty"`
}

func newGroupTransactionMapping(entity GroupTransaction) *GroupTransactionMapping {
	itemMappings := make([]GroupTransactionItemMapping, len(entity.items))
	for i, item := range entity.items {
		itemMappings[i] = *newGroupTransactionItemMapping(item)
	}

	return &GroupTransactionMapping{
		EntityMapping:            NewEntityMapping(entity.Entity),
		ID:                       entity.ID(),
		Items:                    itemMappings,
		Status:                   string(entity.status),
		RequestedBy:              entity.requestedBy,
		Action:                   entity.action,
		ChangeLog:                entity.auditLogs,
		GroupTransactionCategory: entity.groupTransactionCategory,
		EffectiveTime:            entity.effectiveTime,
		ExecutedAt:               entity.executedAt,
	}
}

func (gt *GroupTransactionMapping) GroupTransaction() *GroupTransaction {
	items := make([]GroupTransactionItem, 0, 20)
	for _, im := range gt.Items {
		items = append(items, *im.GroupTransactionItem())
	}

	return &GroupTransaction{
		Entity:                   gt.EntityMapping.Entity(gt.ID),
		items:                    items,
		status:                   GroupTransactionStatus(gt.Status),
		requestedBy:              gt.RequestedBy,
		action:                   gt.Action,
		auditLogs:                gt.ChangeLog,
		groupTransactionCategory: gt.GroupTransactionCategory,
		effectiveTime:            gt.EffectiveTime,
		executedAt:               gt.ExecutedAt,
	}
}

type GroupTransactionItemMapping struct {
	DriverID         string                `bson:"driver_id"`
	Category         string                `bson:"category"`
	Type             string                `bson:"type"`
	SubType          string                `bson:"subType"`
	Amount           types.Money           `bson:"amount"`
	Status           string                `bson:"status"`
	Reason           string                `bson:"reason,omitempty"`
	OrderID          string                `bson:"order_id"`
	TripID           string                `bson:"trip_id"`
	RefID            string                `bson:"ref_id"`
	TransRefID       crypt.EncryptedString `bson:"trans_ref_id"`
	TaxRefID         string                `bson:"tax_ref_id"`
	IncentiveNames   []string              `bson:"incentive_names"`
	IncentiveSources []string              `bson:"incentive_sources"`
	Remark           string                `bson:"remark"`
	FormID           string                `bson:"form_id"`

	TransactionScheme *TransactionScheme `bson:"transaction_scheme,omitempty"`
}

func newGroupTransactionItemMapping(item GroupTransactionItem) *GroupTransactionItemMapping {
	return &GroupTransactionItemMapping{
		DriverID:         item.driverID,
		Category:         string(item.category),
		Type:             string(item.txnType),
		SubType:          string(item.subType),
		Amount:           item.amount,
		Status:           string(item.status),
		Reason:           item.reason,
		OrderID:          item.orderID,
		TripID:           item.tripID,
		RefID:            item.refID,
		TransRefID:       item.transRefID,
		TaxRefID:         item.taxRefID,
		IncentiveNames:   item.incentiveNames,   // LMF-4497
		IncentiveSources: item.incentiveSources, // LMF-4497
		Remark:           item.remark,
		FormID:           item.formID,

		TransactionScheme: item.transactionScheme,
	}
}

func (gt *GroupTransactionItemMapping) GroupTransactionItem() *GroupTransactionItem {
	return &GroupTransactionItem{
		driverID:         gt.DriverID,
		category:         TransactionCategory(gt.Category),
		txnType:          TransactionType(gt.Type),
		subType:          TransactionSubType(gt.SubType),
		amount:           gt.Amount,
		status:           GroupTransactionItemStatus(gt.Status),
		reason:           gt.Reason,
		orderID:          gt.OrderID,
		tripID:           gt.TripID,
		refID:            gt.RefID,
		transRefID:       gt.TransRefID,
		taxRefID:         gt.TaxRefID,
		incentiveNames:   gt.IncentiveNames,   // LMF-4497
		incentiveSources: gt.IncentiveSources, // LMF-4497
		remark:           gt.Remark,
		formID:           gt.FormID,

		transactionScheme: gt.TransactionScheme,
	}
}

func (s GroupTransaction) MarshalBSON() ([]byte, error) {
	mapping := newGroupTransactionMapping(s)
	return bson.Marshal(mapping)
}

func (s *GroupTransaction) UnmarshalBSON(data []byte) error {
	mapping := &GroupTransactionMapping{}
	if err := bson.Unmarshal(data, mapping); err != nil {
		return err
	}

	gt := mapping.GroupTransaction()

	*s = *gt

	return nil
}
