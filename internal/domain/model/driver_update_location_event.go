package model

type DriverUpdateLocationEvent struct {
	ClientTimestampUnixSecond int64
	Lat                       float64
	Lng                       float64
	Region                    string
	Orders                    []DriverUpdateLocationOrder
	OrderQueue                []DriverUpdateLocationOrderQueue
	SavedLocations            []DriverUpdateLocationSavedLocation
	TripId                    string
	TripStatus                string
	IsOfflineLater            bool
	AvailableCapacity         []DriverUpdateLocationAvailableCapacity
}

type DriverUpdateLocationOrder struct {
	OrderId     string
	OrderStatus string
}

type DriverUpdateLocationOrderQueue struct {
	OrderID string
}

type DriverUpdateLocationSavedLocation struct {
	TripId             string
	TripStatus         string
	Orders             []DriverUpdateLocationOrder
	AccuracyInMeter    float64
	Bearing            float64
	Lat                float64
	Lng                float64
	SpeedInMeterPerSec float64
	Timestamp          int64
}

type DriverUpdateLocationAvailableCapacity struct {
	ServiceType string
	Capacity    int32
	Enabled     bool
}
