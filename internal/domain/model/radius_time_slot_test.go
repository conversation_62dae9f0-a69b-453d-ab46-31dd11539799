package model

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestRadiusTimeSlots_GetOverriddenKM(t *testing.T) {
	testCases := []struct {
		name                string
		timeSlots           RadiusTimeSlots
		currentTime         time.Time
		expectedOverridden  bool
		expectedMaxDistance float64
	}{
		{
			name:        "0:59 not in any slots",
			currentTime: time.Date(2022, 11, 14, 0, 59, 0, 0, timeutil.BangkokLocation()),
			timeSlots: RadiusTimeSlots{
				{
					Time: StartEndTime{
						Begin: "01:00:00",
						End:   "03:00:00",
					},
				},
			},
		},
		{
			name:        "1:03 in time slot",
			currentTime: time.Date(2022, 11, 14, 1, 3, 0, 0, timeutil.BangkokLocation()),
			timeSlots: RadiusTimeSlots{
				{
					Time: StartEndTime{
						Begin: "01:00:00",
						End:   "03:00:00",
					},
					MaxRadiusInKm: 20.0,
				},
			},
			expectedOverridden:  true,
			expectedMaxDistance: 20.0,
		},
		{
			name:        "2:59 in time slot",
			currentTime: time.Date(2022, 11, 14, 2, 59, 0, 0, timeutil.BangkokLocation()),
			timeSlots: RadiusTimeSlots{
				{
					Time: StartEndTime{
						Begin: "01:00:00",
						End:   "03:00:00",
					},
					MaxRadiusInKm: 20.0,
				},
			},
			expectedOverridden:  true,
			expectedMaxDistance: 20.0,
		},
		{
			name:        "3:01 not in any slots",
			currentTime: time.Date(2022, 11, 14, 3, 1, 0, 0, timeutil.BangkokLocation()),
			timeSlots: RadiusTimeSlots{
				{
					Time: StartEndTime{
						Begin: "01:00:00",
						End:   "03:00:00",
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(tt *testing.T) {
			isOverridden, km := tc.timeSlots.GetOverriddenKM(tc.currentTime)
			require.Equal(tt, tc.expectedOverridden, isOverridden)
			if tc.expectedMaxDistance != 0 {
				require.Equal(tt, tc.expectedMaxDistance, km)
			}
		})
	}
}

func TestRadiusTimeSlotsWithServiceTypes_GetOverriddenKM(t *testing.T) {
	testCases := []struct {
		name                string
		timeSlots           RadiusTimeSlotsWithServiceTypes
		currentTime         time.Time
		serviceType         Service
		expectedOverridden  bool
		expectedMaxDistance float64
	}{
		{
			name:        "0:59 not in any slots",
			currentTime: time.Date(2022, 11, 14, 0, 59, 0, 0, timeutil.BangkokLocation()),
			timeSlots: RadiusTimeSlotsWithServiceTypes{
				{
					Time: StartEndTime{
						Begin: "01:00:00",
						End:   "03:00:00",
					},
				},
			},
		},
		{
			name:        "1:03 in time slot",
			currentTime: time.Date(2022, 11, 14, 1, 3, 0, 0, timeutil.BangkokLocation()),
			timeSlots: RadiusTimeSlotsWithServiceTypes{
				{
					Time: StartEndTime{
						Begin: "01:00:00",
						End:   "03:00:00",
					},
					RadiiByServiceType: []RadiusByServiceType{
						{
							ServiceType:   ServiceFood,
							MaxRadiusInKm: 20.0,
						},
					},
				},
			},
			expectedOverridden:  true,
			expectedMaxDistance: 20.0,
		},
		{
			name:        "1:03 in time slot but unmatch service type",
			currentTime: time.Date(2022, 11, 14, 1, 3, 0, 0, timeutil.BangkokLocation()),
			timeSlots: RadiusTimeSlotsWithServiceTypes{
				{
					Time: StartEndTime{
						Begin: "01:00:00",
						End:   "03:00:00",
					},
					RadiiByServiceType: []RadiusByServiceType{
						{
							ServiceType:   ServiceMart,
							MaxRadiusInKm: 20.0,
						},
					},
				},
			},
			expectedOverridden: false,
		},
		{
			name:        "2:59 in time slot",
			currentTime: time.Date(2022, 11, 14, 2, 59, 0, 0, timeutil.BangkokLocation()),
			timeSlots: RadiusTimeSlotsWithServiceTypes{
				{
					Time: StartEndTime{
						Begin: "01:00:00",
						End:   "03:00:00",
					},
					RadiiByServiceType: []RadiusByServiceType{
						{
							ServiceType:   ServiceFood,
							MaxRadiusInKm: 20.0,
						},
					},
				},
			},
			expectedOverridden:  true,
			expectedMaxDistance: 20.0,
		},
		{
			name:        "3:01 not in any slots",
			currentTime: time.Date(2022, 11, 14, 3, 1, 0, 0, timeutil.BangkokLocation()),
			timeSlots: RadiusTimeSlotsWithServiceTypes{
				{
					Time: StartEndTime{
						Begin: "01:00:00",
						End:   "03:00:00",
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(tt *testing.T) {
			isOverridden, km := tc.timeSlots.GetOverriddenKM(tc.currentTime, ServiceFood)
			require.Equal(tt, tc.expectedOverridden, isOverridden)
			if tc.expectedMaxDistance != 0 {
				require.Equal(tt, tc.expectedMaxDistance, km)
			}
		})
	}
}
