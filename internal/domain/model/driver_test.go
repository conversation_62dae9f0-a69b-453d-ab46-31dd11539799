package model

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

// avg calculate basically average.
func avg(scores []uint32) float64 {
	s := sum(scores)
	return float64(s) / float64(len(scores))
}

func sum(scores []uint32) uint32 {
	sum := uint32(0)
	for _, v := range scores {
		sum += v
	}
	return sum
}

func TestDriver_SetNewScore(t *testing.T) {
	tests := []struct {
		newRatings            []uint32
		cfg                   config.DriverRatingConfig
		expectedLatestRatings []uint32
		driver                Driver
	}{
		{
			driver:                Driver{},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{1, 2, 3},
			expectedLatestRatings: []uint32{1, 2, 3},
		},
		{
			driver:                Driver{},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{1, 2, 3, 4, 5},
			expectedLatestRatings: []uint32{1, 2, 3, 4, 5},
		},
		{
			driver:                Driver{},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{5, 4, 3, 2, 1},
			expectedLatestRatings: []uint32{5, 4, 3, 2, 1},
		},
		{
			driver:                Driver{},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{1, 10, 3, 2, 5},
			expectedLatestRatings: []uint32{1, 10, 3, 2, 5},
		},
		{
			driver:                Driver{},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{5, 2, 3, 10, 1},
			expectedLatestRatings: []uint32{5, 2, 3, 10, 1},
		},
		{
			driver:                Driver{},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{1, 2, 3, 4, 5, 3},
			expectedLatestRatings: []uint32{2, 3, 4, 5, 3},
		},
		{
			driver: Driver{
				LatestRatings:  []uint32{1, 2, 3, 4},
				N:              4,
				SMARatingScore: avg([]uint32{1, 2, 3, 4}),
			},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{5},
			expectedLatestRatings: []uint32{1, 2, 3, 4, 5},
		},
		{
			driver: Driver{
				LatestRatings:  []uint32{1, 2, 3, 4, 5},
				N:              5,
				SMARatingScore: avg([]uint32{1, 2, 3, 4, 5}),
			},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{3},
			expectedLatestRatings: []uint32{2, 3, 4, 5, 3},
		},
		{
			driver: Driver{
				LatestRatings:  []uint32{1, 2, 3, 4, 5, 6},
				N:              6,
				SMARatingScore: avg([]uint32{1, 2, 3, 4, 5, 6}),
			},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{3},
			expectedLatestRatings: []uint32{3, 4, 5, 6, 3},
		},
		{
			driver: Driver{
				LatestRatings:  []uint32{1, 2, 3},
				N:              10,
				SMARatingScore: avg([]uint32{1, 2, 3}),
			},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{4, 5},
			expectedLatestRatings: []uint32{1, 2, 3, 4, 5},
		},
		{
			driver: Driver{
				LatestRatings:  []uint32{1, 2, 3},
				N:              10,
				SMARatingScore: avg([]uint32{1, 2, 3}),
			},
			cfg:                   config.DriverRatingConfig{MASize: 5},
			newRatings:            []uint32{4, 5, 4},
			expectedLatestRatings: []uint32{2, 3, 4, 5, 4},
		},
	}

	for _, tt := range tests {
		d := tt.driver
		for _, score := range tt.newRatings {
			err := d.SetNewScore(score, tt.cfg)
			require.Nil(t, err)
		}

		require.Equal(t, uint64(len(tt.newRatings))+tt.driver.N, d.N)
		require.Equal(t, tt.expectedLatestRatings, d.LatestRatings)
		require.Equal(t, avg(tt.expectedLatestRatings), d.SMARatingScore)
	}
}

func TestDriver_GetRating(t *testing.T) {
	tests := []struct {
		name           string
		N              uint64
		cfg            config.DriverRatingConfig
		ratingScore    float64
		newRatingScore float64
		expect         float64
	}{
		{
			name:           "should return sma rating when new rating is available",
			N:              1,
			ratingScore:    5,
			newRatingScore: 5,
			cfg:            config.DriverRatingConfig{Min: 0},
			expect:         5,
		},
		{
			name:           "should return no rating if N < cold boot min rate (with old rating only)",
			N:              1,
			ratingScore:    5,
			newRatingScore: 0,
			cfg:            config.DriverRatingConfig{Min: 2},
			expect:         0,
		},
		{
			name:           "should return no rating if N < cold boot min rate (with old and new rating)",
			N:              1,
			ratingScore:    5,
			newRatingScore: 5,
			cfg:            config.DriverRatingConfig{Min: 2},
			expect:         0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			driver := Driver{
				N:              tt.N,
				SMARatingScore: tt.newRatingScore,
			}

			if got := driver.GetRating(tt.cfg); got != tt.expect {
				t.Errorf("%v, actual = %v, expect %v", tt.name, got, tt.expect)
			}
		})
	}
}

func TestAddress_IsEmpty(t *testing.T) {
	type fields struct {
		HouseNumber crypt.LazyEncryptedString
		Moo         crypt.LazyEncryptedString
		Subdistrict crypt.LazyEncryptedString
		District    crypt.LazyEncryptedString
		Province    crypt.LazyEncryptedString
		Zipcode     crypt.LazyEncryptedString
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "all empty",
			want: true,
		},
		{
			name: "house number not empty",
			fields: fields{
				HouseNumber: crypt.NewLazyEncryptedString("1"),
			},
			want: false,
		},

		{
			name: "moo not empty",
			fields: fields{
				Moo: crypt.NewLazyEncryptedString("m"),
			},
			want: false,
		},

		{
			name: "subdistrict not empty",
			fields: fields{
				Subdistrict: crypt.NewLazyEncryptedString("s"),
			},
			want: false,
		},

		{
			name: "district not empty",
			fields: fields{
				District: crypt.NewLazyEncryptedString("d"),
			},
			want: false,
		},

		{
			name: "province not empty",
			fields: fields{
				Province: crypt.NewLazyEncryptedString("p"),
			},
			want: false,
		},

		{
			name: "zipcode not empty",
			fields: fields{
				Zipcode: crypt.NewLazyEncryptedString("z"),
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := Address{
				HouseNumber: tt.fields.HouseNumber,
				Moo:         tt.fields.Moo,
				Subdistrict: tt.fields.Subdistrict,
				District:    tt.fields.District,
				Province:    tt.fields.Province,
				Zipcode:     tt.fields.Zipcode,
			}
			if got := a.IsEmpty(); got != tt.want {
				t.Errorf("IsEmpty() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDriverLicenseInfo_IsEmpty(t *testing.T) {
	type fields struct {
		ID             crypt.LazyEncryptedString
		ExpirationDate *time.Time
		PhotoURL       string
	}
	now := time.Now()
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "all empty",
			fields: fields{
				ID:             crypt.NewLazyEncryptedString(""),
				ExpirationDate: nil,
				PhotoURL:       "",
			},
			want: true,
		},
		{
			name: "id not empty",
			fields: fields{
				ID:             crypt.NewLazyEncryptedString("1"),
				ExpirationDate: nil,
				PhotoURL:       "",
			},
			want: false,
		},
		{
			name: "ExpirationDate empty",
			fields: fields{
				ID:             crypt.NewLazyEncryptedString(""),
				ExpirationDate: &now,
				PhotoURL:       "",
			},
			want: false,
		},
		{
			name: "PhotoURL not empty",
			fields: fields{
				ID:             crypt.NewLazyEncryptedString(""),
				ExpirationDate: nil,
				PhotoURL:       "1",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dli := DriverLicenseInfo{
				ID:             tt.fields.ID,
				ExpirationDate: tt.fields.ExpirationDate,
				PhotoURL:       tt.fields.PhotoURL,
			}
			if got := dli.IsEmpty(); got != tt.want {
				t.Errorf("IsEmpty() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestVehicleInfo_IsEmpty(t *testing.T) {
	type fields struct {
		RegistrationDate     *time.Time
		RegistrationPhotoURL string
		PlateNumber          crypt.LazyEncryptedString
		PhotoURL             string
	}
	now := time.Now()
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "all empty",
			fields: fields{
				RegistrationDate:     nil,
				RegistrationPhotoURL: "",
				PlateNumber:          crypt.NewLazyEncryptedString(""),
				PhotoURL:             "",
			},
			want: true,
		},
		{
			name: "RegistrationDate not empty",
			fields: fields{
				RegistrationDate:     &now,
				RegistrationPhotoURL: "",
				PlateNumber:          crypt.NewLazyEncryptedString(""),
				PhotoURL:             "",
			},
			want: false,
		},
		{
			name: "RegistrationPhotoURL not empty",
			fields: fields{
				RegistrationDate:     nil,
				RegistrationPhotoURL: "1",
				PlateNumber:          crypt.NewLazyEncryptedString(""),
				PhotoURL:             "",
			},
			want: false,
		},
		{
			name: "PlateNumber not empty",
			fields: fields{
				RegistrationDate:     nil,
				RegistrationPhotoURL: "",
				PlateNumber:          crypt.NewLazyEncryptedString("1"),
				PhotoURL:             "",
			},
			want: false,
		},
		{
			name: "PhotoURL not empty",
			fields: fields{
				RegistrationDate:     nil,
				RegistrationPhotoURL: "",
				PlateNumber:          crypt.NewLazyEncryptedString(""),
				PhotoURL:             "1",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			vi := VehicleInfo{
				RegistrationDate:     tt.fields.RegistrationDate,
				RegistrationPhotoURL: tt.fields.RegistrationPhotoURL,
				PlateNumber:          tt.fields.PlateNumber,
				PhotoURL:             tt.fields.PhotoURL,
			}
			if got := vi.IsEmpty(); got != tt.want {
				t.Errorf("IsEmpty() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBankingInfo_IsEmpty(t *testing.T) {
	type fields struct {
		Account       crypt.LazyEncryptedString
		BankName      crypt.LazyEncryptedString
		AccountHolder crypt.LazyEncryptedString
		RefID         crypt.LazyEncryptedString
		CitiRefID     string
		PhotoURL      string
		BranchCode    crypt.LazyEncryptedString
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "all empty",
			want: true,
		},
		{
			name: "Account not empty",
			fields: fields{
				Account: crypt.NewLazyEncryptedString("1"),
			},
			want: false,
		},
		{
			name: "BankName not empty",
			fields: fields{
				BankName: crypt.NewLazyEncryptedString("1"),
			},
			want: false,
		},
		{
			name: "AccountHolder not empty",
			fields: fields{
				AccountHolder: crypt.NewLazyEncryptedString("1"),
			},
			want: false,
		},
		{
			name: "RefID not empty",
			fields: fields{
				RefID: crypt.NewLazyEncryptedString("1"),
			},
			want: false,
		},
		{
			name: "CitiRefID not empty",
			fields: fields{
				CitiRefID: crypt.NewLazyEncryptedString("1").String(),
			},
			want: false,
		},
		{
			name: "PhotoURL not empty",
			fields: fields{
				PhotoURL: "1",
			},
			want: false,
		},
		{
			name: "BranchCode not empty",
			fields: fields{
				BranchCode: crypt.NewLazyEncryptedString("1"),
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			bi := BankingInfo{
				Account:       tt.fields.Account,
				BankName:      tt.fields.BankName,
				AccountHolder: tt.fields.AccountHolder,
				RefID:         tt.fields.RefID,
				CitiRefID:     tt.fields.CitiRefID,
				PhotoURL:      tt.fields.PhotoURL,
				BranchCode:    tt.fields.BranchCode,
			}
			if got := bi.IsEmpty(); got != tt.want {
				t.Errorf("IsEmpty() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDriver_SetCriminalStatus_ValidTransition(t *testing.T) {

	type testCase struct {
		currentStatus CriminalStatus
		newStatus     CriminalStatus
	}

	testCasesToPass := []testCase{
		{currentStatus: CriminalStatusWhitelist, newStatus: CriminalStatusPass},
		{currentStatus: CriminalStatusPending, newStatus: CriminalStatusPass},
		{currentStatus: CriminalStatusInReview, newStatus: CriminalStatusPass},
		{currentStatus: CriminalStatusNotPass, newStatus: CriminalStatusPass},
		{currentStatus: "", newStatus: CriminalStatusPass},
	}

	testCasesToNotPass := []testCase{
		{currentStatus: CriminalStatusWhitelist, newStatus: CriminalStatusNotPass},
		{currentStatus: CriminalStatusPending, newStatus: CriminalStatusNotPass},
		{currentStatus: CriminalStatusInReview, newStatus: CriminalStatusNotPass},
		{currentStatus: CriminalStatusPass, newStatus: CriminalStatusNotPass},
		{currentStatus: "", newStatus: CriminalStatusNotPass},
	}

	testCasePendingToWhitelist := []testCase{
		{currentStatus: CriminalStatusPending, newStatus: CriminalStatusWhitelist},
		{currentStatus: "", newStatus: CriminalStatusWhitelist},
	}

	testCaseSameStatus := []testCase{
		{currentStatus: CriminalStatusWhitelist, newStatus: CriminalStatusWhitelist},
		{currentStatus: CriminalStatusInReview, newStatus: CriminalStatusInReview},
		{currentStatus: CriminalStatusPending, newStatus: CriminalStatusPending},
		{currentStatus: CriminalStatusNotPass, newStatus: CriminalStatusNotPass},
		{currentStatus: CriminalStatusPass, newStatus: CriminalStatusPass},
	}

	var testCases []testCase

	testCases = append(testCases, testCasesToPass...)
	testCases = append(testCases, testCasesToNotPass...)
	testCases = append(testCases, testCasePendingToWhitelist...)
	testCases = append(testCases, testCaseSameStatus...)

	for _, tc := range testCases {
		name := fmt.Sprintf("from %s to %s", tc.currentStatus, tc.newStatus)
		t.Run(name, func(tt *testing.T) {
			d := Driver{
				BaseDriver: BaseDriver{
					CriminalCheckStatus: tc.currentStatus,
				},
			}
			err := d.SetCriminalStatus(tc.newStatus)
			require.NoError(tt, err, name)
			require.Equal(tt, d.EncryptedCriminalCheckStatus.String(), d.StrongEncryptedCriminalCheckStatus.String())
		})
	}
}

func TestDriver_SetCriminalStatus_InvalidTransition(t *testing.T) {

	testCases := []struct {
		currentStatus CriminalStatus
		newStatus     CriminalStatus
	}{
		{currentStatus: CriminalStatusPass, newStatus: CriminalStatusWhitelist},
		{currentStatus: CriminalStatusPending, newStatus: CriminalStatusInReview},
		{currentStatus: CriminalStatusWhitelist, newStatus: CriminalStatusInReview},
		{currentStatus: CriminalStatusInReview, newStatus: CriminalStatusPending},
		{currentStatus: CriminalStatusInReview, newStatus: CriminalStatusWhitelist},
		{currentStatus: "", newStatus: CriminalStatusInReview},
	}

	for _, tc := range testCases {
		name := fmt.Sprintf("from %s to %s", tc.currentStatus, tc.newStatus)
		t.Run(name, func(tt *testing.T) {
			d := Driver{
				BaseDriver: BaseDriver{
					CriminalCheckStatus: tc.currentStatus,
				},
			}
			err := d.SetCriminalStatus(tc.newStatus)
			require.Equal(tt, ErrInvalidTransition, err, name)
		})
	}
}

func TestDriver_HasBankInfo(t *testing.T) {
	type fields struct {
		Account       crypt.LazyEncryptedString
		BankName      crypt.LazyEncryptedString
		AccountHolder crypt.LazyEncryptedString
	}
	tests := []struct {
		name        string
		fields      fields
		hasBankInfo bool
	}{
		{
			name: "all",
			fields: fields{
				AccountHolder: crypt.NewLazyEncryptedString("test"),
				BankName:      crypt.NewLazyEncryptedString("test"),
				Account:       crypt.NewLazyEncryptedString("test"),
			},
			hasBankInfo: true,
		},
		{
			name: "Account empty",
			fields: fields{
				BankName:      crypt.NewLazyEncryptedString("test"),
				AccountHolder: crypt.NewLazyEncryptedString("test"),
			},
			hasBankInfo: false,
		},
		{
			name: "BankName empty",
			fields: fields{
				AccountHolder: crypt.NewLazyEncryptedString("test"),
				Account:       crypt.NewLazyEncryptedString("test"),
			},
			hasBankInfo: false,
		},
		{
			name: "AccountHolder empty",
			fields: fields{
				Account:  crypt.NewLazyEncryptedString("test"),
				BankName: crypt.NewLazyEncryptedString("test"),
			},
			hasBankInfo: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			b := BankingInfo{
				Account:       tt.fields.Account,
				BankName:      tt.fields.BankName,
				AccountHolder: tt.fields.AccountHolder,
			}
			d := Driver{
				BaseDriver: BaseDriver{
					Banking: b,
				},
			}
			if got := d.HasBankInfo(); got != tt.hasBankInfo {
				t.Errorf("HasBankInfo() = %v, want %v", got, tt.hasBankInfo)
			}
		})
	}
}

func TestDriver_AddCompletedOrderQuota(t *testing.T) {
	driver := Driver{}
	driver.AddCompletedOrderQuota(config.CancellationRateConfig{InitialQuotaSize: 3, MaxQuotaSize: 4, CompleteOrdersPerQuota: 2})
	require.Equal(t, 3, driver.CancellationQuota.CancellationRateFreeQuota)
	require.Equal(t, 1, driver.CancellationQuota.CompletedOrdersSinceLastQuota)
}

func TestDriver_UseCancellationQuota(t *testing.T) {
	testCases := []struct {
		name                      string
		driver                    *Driver
		cancellationRateConfig    config.CancellationRateConfig
		expectedCancellationQuota CancellationQuota
		expectedErr               error
	}{
		{
			name:                      "use cancellation quota on driver without cancellation quota model",
			driver:                    &Driver{},
			cancellationRateConfig:    config.CancellationRateConfig{InitialQuotaSize: 2},
			expectedCancellationQuota: CancellationQuota{CancellationRateFreeQuota: 1, CompletedOrdersSinceLastQuota: 0},
			expectedErr:               nil,
		},
		{
			name:                      "use cancellation quota on driver with 0 quota and 0 completed orders till next quota",
			driver:                    &Driver{CancellationQuota: &CancellationQuota{CancellationRateFreeQuota: 0, CompletedOrdersSinceLastQuota: 0}},
			cancellationRateConfig:    config.CancellationRateConfig{InitialQuotaSize: 2},
			expectedCancellationQuota: CancellationQuota{CancellationRateFreeQuota: 0, CompletedOrdersSinceLastQuota: 0},
			expectedErr:               QuotaNotAvailable,
		},
		{
			name:                      "use cancellation quota on driver with 0 quota and 2 completed orders till next quota",
			driver:                    &Driver{CancellationQuota: &CancellationQuota{CancellationRateFreeQuota: 0, CompletedOrdersSinceLastQuota: 2}},
			cancellationRateConfig:    config.CancellationRateConfig{InitialQuotaSize: 2},
			expectedCancellationQuota: CancellationQuota{CancellationRateFreeQuota: 0, CompletedOrdersSinceLastQuota: 2},
			expectedErr:               QuotaNotAvailable,
		},
		{
			name:                      "use cancellation quota on driver with 2 completed orders till next quota",
			driver:                    &Driver{CancellationQuota: &CancellationQuota{CancellationRateFreeQuota: 2, CompletedOrdersSinceLastQuota: 2}},
			cancellationRateConfig:    config.CancellationRateConfig{InitialQuotaSize: 2},
			expectedCancellationQuota: CancellationQuota{CancellationRateFreeQuota: 1, CompletedOrdersSinceLastQuota: 2},
			expectedErr:               nil,
		},
		{
			name:                      "use cancellation quota on driver with max quota",
			driver:                    &Driver{CancellationQuota: &CancellationQuota{CancellationRateFreeQuota: 4, CompletedOrdersSinceLastQuota: 0}},
			cancellationRateConfig:    config.CancellationRateConfig{InitialQuotaSize: 2, MaxQuotaSize: 4},
			expectedCancellationQuota: CancellationQuota{CancellationRateFreeQuota: 3, CompletedOrdersSinceLastQuota: 0},
			expectedErr:               nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.driver.UseCancellationQuota(tc.cancellationRateConfig)
			require.Equal(t, tc.expectedErr, err)
			require.Equal(t, tc.expectedCancellationQuota, *tc.driver.CancellationQuota)
		})
	}
}

func TestDriver_GetDriverTierByTime(t *testing.T) {

	testCases := []struct {
		name    string
		drv     *Driver
		tierRes DriverTier
		date    time.Time
	}{
		{
			name: "get current tier when no profile history",
			drv: &Driver{
				BaseDriver: BaseDriver{
					DriverTier: DriverTierBasic,
				},
				ProfileHistories: []ProfileHistory{},
			},
			tierRes: DriverTierBasic,
			date:    time.Date(2022, 4, 25, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "get valid driver tier member",
			drv: &Driver{
				BaseDriver: BaseDriver{
					DriverTier: DriverTierBasic,
				},
				ProfileHistories: []ProfileHistory{
					{
						Name:  ProfileDriverTierHistoryName,
						Date:  time.Date(2022, 4, 15, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: string(DriverTierVip),
					},
					{
						Name:  ProfileDriverTierHistoryName,
						Date:  time.Date(2022, 4, 17, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: string(DriverTierMember),
					},
					{
						Name:  ProfileDriverTierHistoryName,
						Date:  time.Date(2022, 4, 25, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: string(DriverTierPro),
					},
				},
			},
			tierRes: DriverTierMember,
			date:    time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "get valid driver tier pro",
			drv: &Driver{
				BaseDriver: BaseDriver{
					DriverTier: DriverTierBasic,
				},
				ProfileHistories: []ProfileHistory{
					{
						Name:  ProfileDriverTierHistoryName,
						Date:  time.Date(2022, 4, 25, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: string(DriverTierPro),
					},
				},
			},
			tierRes: DriverTierBasic,
			date:    time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			res := tc.drv.GetDriverTierByTime(tc.date)
			require.Equal(t, tc.tierRes, res)
		})
	}

}

func TestDriver_GetHaveBoxByTime(t *testing.T) {
	testCases := []struct {
		name   string
		drv    *Driver
		result bool
		date   time.Time
	}{
		{
			name: "get valid have box",
			drv: &Driver{
				Options: Options{},
				ProfileHistories: []ProfileHistory{
					{
						Name:  ProfileHaveBoxHistoryName,
						Date:  time.Date(2022, 4, 15, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: false,
					},
					{
						Name:  ProfileHaveBoxHistoryName,
						Date:  time.Date(2022, 4, 17, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: true,
					},
					{
						Name:  ProfileHaveBoxHistoryName,
						Date:  time.Date(2022, 4, 25, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: false,
					},
				},
			},
			result: true,
			date:   time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "get LM box type instead of have box when empty profile history",
			drv: &Driver{
				Options: Options{
					BoxType: BoxTypeLM,
				},
			},
			result: true,
			date:   time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "get Foldable box type instead of have box when empty profile history",
			drv: &Driver{
				Options: Options{
					BoxType: BoxTypeFoldable,
				},
			},
			result: true,
			date:   time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "get box type instead of have box when have only box type in profile histories",
			drv: &Driver{
				Options: Options{
					BoxType: BoxTypeUnknown,
				},
				ProfileHistories: []ProfileHistory{
					{
						Name:  ProfileBoxTypeHistoryName,
						Date:  time.Date(2022, 4, 19, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: BoxTypeStandard.String(),
					},
				},
			},
			result: false,
			date:   time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "get valid have box when combine have box and box type in profile histories",
			drv: &Driver{
				Options: Options{
					BoxType: BoxTypeUnknown,
				},
				ProfileHistories: []ProfileHistory{
					{
						Name:  ProfileHaveBoxHistoryName,
						Date:  time.Date(2022, 4, 18, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: false,
					},
					{
						Name:  ProfileBoxTypeHistoryName,
						Date:  time.Date(2022, 4, 19, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: BoxTypeLM.String(),
					},
				},
			},
			result: true,
			date:   time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "allow box type LM only when empty profile history",
			drv: &Driver{
				Options: Options{
					BoxType: BoxTypeStandard,
				},
			},
			result: false,
			date:   time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "having Standard box type when not empty profile history",
			drv: &Driver{
				Options: Options{
					BoxType: BoxTypeUnknown,
				},
				ProfileHistories: []ProfileHistory{
					{
						Name:  ProfileHaveBoxHistoryName,
						Date:  time.Date(2022, 4, 18, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: false,
					},
					{
						Name:  ProfileBoxTypeHistoryName,
						Date:  time.Date(2022, 4, 19, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: BoxTypeStandard.String(),
					},
				},
			},
			result: false,
			date:   time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "having Foldable box type when not empty profile history",
			drv: &Driver{
				Options: Options{
					BoxType: BoxTypeUnknown,
				},
				ProfileHistories: []ProfileHistory{
					{
						Name:  ProfileHaveBoxHistoryName,
						Date:  time.Date(2022, 4, 18, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: false,
					},
					{
						Name:  ProfileBoxTypeHistoryName,
						Date:  time.Date(2022, 4, 19, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: BoxTypeFoldable.String(),
					},
				},
			},
			result: true,
			date:   time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			res := tc.drv.GetHaveBoxByTime(tc.date)
			require.Equal(t, tc.result, res)
		})
	}
}

func TestDriver_GetHaveJacketByTime(t *testing.T) {
	testCases := []struct {
		name   string
		drv    *Driver
		result bool
		date   time.Time
	}{
		{
			name: "get current have jacket when no profile history",
			drv: &Driver{
				Options:          Options{HaveJacket: true},
				ProfileHistories: []ProfileHistory{},
			},
			result: true,
			date:   time.Date(2022, 4, 25, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "get valid have jacket",
			drv: &Driver{
				Options: Options{},
				ProfileHistories: []ProfileHistory{
					{
						Name:  ProfileHaveJacketHistoryName,
						Date:  time.Date(2022, 4, 15, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: false,
					},
					{
						Name:  ProfileHaveJacketHistoryName,
						Date:  time.Date(2022, 4, 17, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: true,
					},
					{
						Name:  ProfileHaveJacketHistoryName,
						Date:  time.Date(2022, 4, 25, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: false,
					},
				},
			},
			result: true,
			date:   time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "get valid have jacket when no data date less than date param",
			drv: &Driver{
				Options: Options{HaveJacket: true},
				ProfileHistories: []ProfileHistory{
					{
						Name:  ProfileHaveJacketHistoryName,
						Date:  time.Date(2022, 4, 25, 23, 30, 0, 0, timeutil.BangkokLocation()),
						Value: false,
					},
				},
			},
			result: true,
			date:   time.Date(2022, 4, 20, 23, 30, 0, 0, timeutil.BangkokLocation()),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			res := tc.drv.GetHaveJacketByTime(tc.date)
			require.Equal(t, tc.result, res)
		})
	}
}

func TestDriver_SetCriminalStatus_ShouldSetStrongCriminalStatus(t *testing.T) {
	type testCase struct {
		currentStatus CriminalStatus
		newStatus     CriminalStatus
	}
	testCases := []testCase{
		{currentStatus: CriminalStatusWhitelist, newStatus: CriminalStatusPass},
		{currentStatus: CriminalStatusPending, newStatus: CriminalStatusPass},
		{currentStatus: CriminalStatusInReview, newStatus: CriminalStatusPass},
		{currentStatus: CriminalStatusNotPass, newStatus: CriminalStatusPass},
		{currentStatus: "", newStatus: CriminalStatusPass},
	}

	for _, tc := range testCases {
		name := fmt.Sprintf("from %s to %s", tc.currentStatus, tc.newStatus)
		t.Run(name, func(tt *testing.T) {
			d := Driver{
				BaseDriver: BaseDriver{
					CriminalCheckStatus: tc.currentStatus,
				},
			}
			err := d.SetCriminalStatus(tc.newStatus)
			require.NoError(tt, err, name)
			require.Equal(tt, d.EncryptedCriminalCheckStatus.String(), d.StrongEncryptedCriminalCheckStatus.String())
		})
	}
}

func TestGoodness_DisplayInAdminProfile(t *testing.T) {
	type fields struct {
		Level          prediction.RiderLevel
		LevelExpiredAt *time.Time
	}
	type args struct {
		now time.Time
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		wantRiderLevel string
		wantExpiredAt  string
	}{
		{
			name: "active expired at",
			fields: fields{
				Level:          prediction.RiderLevelL10,
				LevelExpiredAt: types.Ptr(time.Date(2024, 2, 15, 10, 0, 0, 0, time.UTC)),
			},
			args: args{
				now: time.Date(2024, 2, 14, 10, 0, 0, 0, time.UTC),
			},
			wantRiderLevel: "Rider_L10",
			wantExpiredAt:  "2024-02-15 17:00:00",
		},
		{
			name: "default level and active expired at",
			fields: fields{
				Level:          prediction.RiderLevelDefault,
				LevelExpiredAt: types.Ptr(time.Date(2024, 2, 15, 10, 0, 0, 0, time.UTC)),
			},
			args: args{
				now: time.Date(2024, 2, 14, 10, 0, 0, 0, time.UTC),
			},
			wantRiderLevel: "",
			wantExpiredAt:  "2024-02-15 17:00:00",
		},
		{
			name: "inactive expired at",
			fields: fields{
				Level:          prediction.RiderLevelL10,
				LevelExpiredAt: types.Ptr(time.Date(2024, 2, 15, 10, 0, 0, 0, time.UTC)),
			},
			args: args{
				now: time.Date(2024, 2, 16, 10, 0, 0, 0, time.UTC),
			},
			wantRiderLevel: "",
			wantExpiredAt:  "",
		},
		{
			name:   "empty goodness",
			fields: fields{},
			args: args{
				now: time.Date(2024, 2, 16, 10, 0, 0, 0, time.UTC),
			},
			wantRiderLevel: "",
			wantExpiredAt:  "",
		},
		{
			name: "nil expired at",
			fields: fields{
				Level: prediction.RiderLevelL10,
			},
			args: args{
				now: time.Date(2024, 2, 16, 10, 0, 0, 0, time.UTC),
			},
			wantRiderLevel: "",
			wantExpiredAt:  "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := Goodness{
				Level:          tt.fields.Level,
				LevelExpiredAt: tt.fields.LevelExpiredAt,
			}
			gotRiderLevel, gotExpiredAt := g.DisplayInAdminProfile(tt.args.now)
			if gotRiderLevel != tt.wantRiderLevel {
				t.Errorf("Goodness.DisplayInAdminProfile() gotRiderLevel = %v, want %v", gotRiderLevel, tt.wantRiderLevel)
			}
			if gotExpiredAt != tt.wantExpiredAt {
				t.Errorf("Goodness.DisplayInAdminProfile() gotExpiredAt = %v, want %v", gotExpiredAt, tt.wantExpiredAt)
			}
		})
	}
}

func TestAddress_HouseNumberAndMoo(t *testing.T) {
	type TestCase struct {
		Name string

		GivenHouseNumber string
		GivenMoo         string

		WantHouseNumberAndMoo string
	}
	testCases := []TestCase{
		{
			Name:                  "both house number and moo exist",
			GivenHouseNumber:      "144/164",
			GivenMoo:              "5",
			WantHouseNumberAndMoo: "144/164 หมู่ 5",
		},
		{
			Name:                  "both house number and moo exist with street and road",
			GivenHouseNumber:      "144/164",
			GivenMoo:              "ซอย ทวีวัฒนา ถนน ทวีวัฒนา",
			WantHouseNumberAndMoo: "144/164 หมู่ ซอย ทวีวัฒนา ถนน ทวีวัฒนา",
		},
		{
			Name:                  "both house number and moo exist with street and road empty prefix",
			GivenHouseNumber:      "144/164",
			GivenMoo:              "ทวีวัฒนา ถนน ทวีวัฒนา",
			WantHouseNumberAndMoo: "144/164 หมู่ ทวีวัฒนา ถนน ทวีวัฒนา",
		},
		{
			Name:                  "both house number and moo exist with street empty street",
			GivenHouseNumber:      "144/164",
			GivenMoo:              "ซอย ถนน ทวีวัฒนา",
			WantHouseNumberAndMoo: "144/164 หมู่ ซอย ถนน ทวีวัฒนา",
		},
		{
			Name:                  "only house number exists",
			GivenHouseNumber:      "144/164",
			GivenMoo:              "",
			WantHouseNumberAndMoo: "144/164 หมู่ -",
		},
		{
			Name:                  "only moo exists",
			GivenHouseNumber:      "",
			GivenMoo:              "5",
			WantHouseNumberAndMoo: "หมู่ 5",
		},
		{
			Name:                  "both house number and moo are empty",
			GivenHouseNumber:      "",
			GivenMoo:              "",
			WantHouseNumberAndMoo: "หมู่ -",
		},
	}

	for _, testCase := range testCases {
		tc := testCase
		t.Run("empty", func(t *testing.T) {
			address := Address{
				HouseNumber: crypt.NewLazyEncryptedString(tc.GivenHouseNumber),
				Moo:         crypt.NewLazyEncryptedString(tc.GivenMoo),
			}
			require.Equal(t, tc.WantHouseNumberAndMoo, address.HouseNumberAndMoo())
		})
	}
}

func TestDriver_GetDSCR(t *testing.T) {

	now := time.Date(2024, 03, 13, 9, 0, 0, 0, &time.Location{})

	testCases := []struct {
		name        string
		d           *Driver
		currentTime time.Time
		expectDSCR  float64
	}{
		{
			name: "when dscr effective date is zero should return dscr 0",
			d: &Driver{
				DSCR: 0,
			},
			currentTime: now,
			expectDSCR:  0,
		},
		{
			name: "when dscr effective date is zero should return dscr 50",
			d: &Driver{
				DSCR: 50,
			},
			currentTime: now,
			expectDSCR:  50,
		},
		{
			name: "when dscr effective date is zero should return dscr float",
			d: &Driver{
				DSCR: 1.5,
			},
			currentTime: now,
			expectDSCR:  1.5,
		},
		{
			name: "when current time is before dscr effective date should return zero",
			d: &Driver{
				DSCR: 75,
				DSCREffectiveDate: types.Period{
					StartAt: now.AddDate(0, 0, 1),
					EndAt:   now.AddDate(0, 0, 2),
				},
			},
			currentTime: now,
			expectDSCR:  0,
		},
		{
			name: "when current time is after dscr effective date should return zero",
			d: &Driver{
				DSCR: 75,
				DSCREffectiveDate: types.Period{
					StartAt: now.AddDate(0, 0, -2),
					EndAt:   now.AddDate(0, 0, -1),
				},
			},
			currentTime: now,
			expectDSCR:  0,
		},
		{
			name: "when current time is within dscr effective date should return dscr",
			d: &Driver{
				DSCR: 75,
				DSCREffectiveDate: types.Period{
					StartAt: now.AddDate(0, 0, -1),
					EndAt:   now.AddDate(0, 0, 1),
				},
			},
			currentTime: now,
			expectDSCR:  75,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actualDSCR := tc.d.GetDSCR(tc.currentTime)
			require.Equal(t, tc.expectDSCR, actualDSCR)
		})
	}
}

func TestDriverMinimal_IsServiceEligible(t *testing.T) {
	t.Parallel()

	t.Run("should return true if services contains", func(t *testing.T) {
		dm := DriverMinimal{
			ServiceTypes: CurrentSupportServices,
		}

		for _, s := range DefaultServices {
			require.True(t, dm.IsServiceEligible(s))
		}
	})

	t.Run("should return false if services contain but opt-outed", func(t *testing.T) {
		dm := DriverMinimal{
			ServiceTypes:   CurrentSupportServices,
			ServicesOptOut: DefaultServices,
		}

		for _, s := range DefaultServices {
			require.False(t, dm.IsServiceEligible(s))
		}
	})

	t.Run("should return true to default service if all services opt outed", func(t *testing.T) {
		dm := DriverMinimal{
			ServicesOptOut: CurrentSupportServices,
		}

		for _, s := range CurrentSupportServices {
			if s == ServiceBike {
				require.False(t, dm.IsServiceEligible(s))
			} else {
				require.True(t, dm.IsServiceEligible(s))
			}
		}
	})

	t.Run("should return true to default service", func(t *testing.T) {
		dm := DriverMinimal{}

		for _, s := range CurrentSupportServices {
			if s == ServiceBike {
				require.False(t, dm.IsServiceEligible(s))
			} else {
				require.True(t, dm.IsServiceEligible(s))
			}
		}
	})
}

type isStillBanTestCase struct {
	Name              string
	InputDriverStatus DriverStatus
	InputBannedUntil  *time.Time
	ExpectedResult    bool
}

func TestDriver_IsStillBan(t *testing.T) {
	timeutils.Freeze()
	testCases := []isStillBanTestCase{
		{
			Name:              "Permanent Ban",
			InputDriverStatus: StatusBanned,
			InputBannedUntil:  nil,
			ExpectedResult:    true,
		},
		{
			Name:              "Temporary Ban - passed the ban until date",
			InputDriverStatus: StatusBanned,
			InputBannedUntil:  fp.ToPointer(timeutils.Now().UTC().Add(-1 * time.Second)),
			ExpectedResult:    false,
		},
		{
			Name:              "Temporary Ban - still in the ban period",
			InputDriverStatus: StatusBanned,
			InputBannedUntil:  fp.ToPointer(timeutils.Now().UTC().Add(1 * time.Second)),
			ExpectedResult:    true,
		},
		{
			Name:              "Temporary Ban - equal the ban period",
			InputDriverStatus: StatusBanned,
			InputBannedUntil:  fp.ToPointer(timeutils.Now().UTC()),
			ExpectedResult:    true,
		},
		{
			Name:              "Status is not ban",
			InputDriverStatus: StatusOffline,
			InputBannedUntil:  fp.ToPointer(timeutils.Now().UTC()),
			ExpectedResult:    false,
		},
		{
			Name:              "Permanent Ban with zero time",
			InputDriverStatus: StatusBanned,
			InputBannedUntil:  fp.ToPointer(time.Time{}),
			ExpectedResult:    true,
		},
	}

	for _, testCase := range testCases {
		t.Run(fmt.Sprintf("IsStillBan - %s", testCase.Name), func(tt *testing.T) {
			underTest := Driver{
				Status:      testCase.InputDriverStatus,
				BannedUntil: testCase.InputBannedUntil,
			}

			result := underTest.IsStillBan()
			require.Equal(tt, testCase.ExpectedResult, result)
		})
	}

	timeutils.Unfreeze()
}

func Test_DriverTierMarshalCSV(t *testing.T) {
	driverTier := DriverTierBasic
	expected := "BASIC"

	actual, err := driverTier.MarshalCSV()

	require.Nil(t, err)
	require.Equal(t, expected, actual)
}

func Test_DriverTierString(t *testing.T) {
	driverTier := DriverTierBasic
	expected := "BASIC"

	actual := driverTier.String()

	require.Equal(t, expected, actual)
}

func Test_IsValidDriverTier_return_true(t *testing.T) {
	driverTier := DriverTierBasic

	actual := IsValidDriverTier(driverTier)

	require.Equal(t, true, actual)
}

func Test_IsValidDriverTier_return_false(t *testing.T) {
	driverTier := DriverTier("invalid")

	actual := IsValidDriverTier(driverTier)

	require.Equal(t, false, actual)
}

func Test_DriverTierUnmarshalCSV_valid(t *testing.T) {
	input := "BASIC"
	driverTier := DriverTier(input)

	err := driverTier.UnmarshalCSV(input)

	require.Nil(t, err)
}

func Test_DriverTierUnmarshalCSV_invalid(t *testing.T) {
	input := "invalid"
	driverTier := DriverTier(input)

	err := driverTier.UnmarshalCSV(input)

	require.Error(t, err)
}

func Test_DriverTierUnmarshalCSV_empty(t *testing.T) {
	input := ""
	driverTier := DriverTier(input)

	err := driverTier.UnmarshalCSV(input)

	require.Error(t, err)
}

func TestDriverTierToTierName(t *testing.T) {
	t.Parallel()

	tests := []struct {
		input    DriverTier
		expected TierDisplayName
	}{
		{DriverTierMember, TierDisplayNameBronze},
		{DriverTierBasic, TierDisplayNameSilver},
		{DriverTierStar, TierDisplayNameGold},
		{DriverTierPro, TierDisplayNameMaster},
		{DriverTierProPlus, TierDisplayNameLegend},
		{DriverTier("INVALID"), ""},
	}

	for _, tt := range tests {
		result := tt.input.ToTierName()
		assert.Equal(t, tt.expected, result, "ToTierName(%v) should return %v", tt.input, tt.expected)
	}
}

func TestTierDisplayNameToDriverTier(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name     string
		input    TierDisplayName
		expected DriverTier
	}{
		{
			name:     "Bronze",
			input:    TierDisplayNameBronze,
			expected: DriverTierMember,
		},
		{
			name:     "Silver",
			input:    TierDisplayNameSilver,
			expected: DriverTierBasic,
		},
		{
			name:     "Gold",
			input:    TierDisplayNameGold,
			expected: DriverTierStar,
		},
		{
			name:     "Master",
			input:    TierDisplayNameMaster,
			expected: DriverTierPro,
		},
		{
			name:     "Legend",
			input:    TierDisplayNameLegend,
			expected: DriverTierProPlus,
		},
		{
			name:     "Unknown",
			input:    TierDisplayName("Unknown"),
			expected: "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			actual := tc.input.ToDriverTier()
			assert.Equal(t, tc.expected, actual)
		})
	}
}

func TestTierDisplayNameIsValid(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name     string
		input    TierDisplayName
		expected bool
	}{
		{
			name:     "Valid Bronze tier",
			input:    TierDisplayNameBronze,
			expected: true,
		},
		{
			name:     "Valid Silver tier",
			input:    TierDisplayNameSilver,
			expected: true,
		},
		{
			name:     "Valid Gold tier",
			input:    TierDisplayNameGold,
			expected: true,
		},
		{
			name:     "Valid Master tier",
			input:    TierDisplayNameMaster,
			expected: true,
		},
		{
			name:     "Valid Legend tier",
			input:    TierDisplayNameLegend,
			expected: true,
		},
		{
			name:     "Invalud tier",
			input:    TierDisplayName("Unknown"),
			expected: false,
		},
		{
			name:     "Invalid empty tier",
			input:    TierDisplayName(""),
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			actual := tc.input.IsValid()
			assert.Equal(t, tc.expected, actual)
		})
	}
}
