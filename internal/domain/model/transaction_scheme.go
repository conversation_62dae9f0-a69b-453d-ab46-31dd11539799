package model

import (
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const TxnSchemeAuditObject ObjectType = "transaction_scheme"
const TxnSchemeCreateAudit AuditAction = "create"
const TxnSchemeUpdateAudit AuditAction = "update"
const TxnSchemeArchivedAudit AuditAction = "archived"

var ErrTxnSchemeAlreadyExist = errors.New("duplicate transaction scheme")

type TransactionSchemeOperator string

const (
	AdditionOperator    TransactionSchemeOperator = "ADDITION"
	SubtractionOperator TransactionSchemeOperator = "SUBTRACTION"
)

type TransactionSchemeStatus string

const (
	ActiveTransactionScheme   TransactionSchemeStatus = "ACTIVE"
	ArchivedTransactionScheme TransactionSchemeStatus = "ARCHIVED"
)

type TransactionScheme struct {
	ID              primitive.ObjectID        `bson:"_id,omitempty"`
	Name            string                    `bson:"name"`
	DisplayText     string                    `bson:"display_text"`
	Channel         TransactionChannel        `bson:"channel"`
	Category        TransactionCategory       `bson:"category"`
	Type            TransactionType           `bson:"type"`
	Operator        TransactionSchemeOperator `bson:"operator"`
	SubType         TransactionSubType        `bson:"sub_type,omitempty"`
	RequiredOrderID bool                      `bson:"required_order_id"`
	RequiredTax     bool                      `bson:"required_tax"`
	Status          TransactionSchemeStatus   `bson:"status"`
	CreatedAt       time.Time                 `bson:"created_at"`
	UpdatedAt       time.Time                 `bson:"updated_at"`
}

func NewTransactionScheme(name, displayText string, channel TransactionChannel, category TransactionCategory, txnType TransactionType, operator TransactionSchemeOperator, subType TransactionSubType, requiredOrderID, requiredTax bool) TransactionScheme {
	now := timeutil.BangkokNow()
	return TransactionScheme{
		Name:            name,
		DisplayText:     displayText,
		Channel:         channel,
		Category:        category,
		Type:            txnType,
		Operator:        operator,
		SubType:         subType,
		RequiredOrderID: requiredOrderID,
		RequiredTax:     requiredTax,
		Status:          ActiveTransactionScheme,
		CreatedAt:       now,
		UpdatedAt:       now,
	}
}

func (ts *TransactionScheme) Archived() {
	ts.Status = ArchivedTransactionScheme
}

func (tso *TransactionSchemeOperator) CompareMoney(before types.Money, after types.Money) {
	if before.Equal(after) {
		return
	}

	if before.LT(after) {
		*tso = AdditionOperator
		return
	}
	*tso = SubtractionOperator
}
