package model

import "time"

type GroupTransactionAuditLog struct {
	message   string
	createdBy string
	createdAt time.Time
}

func NewGroupTransactionAuditLog(message, createdBy string) GroupTransactionAuditLog {
	return GroupTransactionAuditLog{
		message:   message,
		createdBy: createdBy,
		createdAt: time.Now().UTC(),
	}
}

func (al *GroupTransactionAuditLog) Message() string {
	return al.message
}

func (al *GroupTransactionAuditLog) CreatedAt() time.Time {
	return al.createdAt
}

func (al *GroupTransactionAuditLog) CreatedBy() string {
	return al.createdBy
}
