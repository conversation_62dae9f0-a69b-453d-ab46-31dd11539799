// Code generated by MockGen. DO NOT EDIT.
// Source: ./driver_order_info.go

// Package mock_model is a generated GoMock package.
package mock_model

import (
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockGetARCRDriverOrderInfo is a mock of GetARCRDriverOrderInfo interface.
type MockGetARCRDriverOrderInfo struct {
	ctrl     *gomock.Controller
	recorder *MockGetARCRDriverOrderInfoMockRecorder
}

// MockGetARCRDriverOrderInfoMockRecorder is the mock recorder for MockGetARCRDriverOrderInfo.
type MockGetARCRDriverOrderInfoMockRecorder struct {
	mock *MockGetARCRDriverOrderInfo
}

// NewMockGetARCRDriverOrderInfo creates a new mock instance.
func NewMockGetARCRDriverOrderInfo(ctrl *gomock.Controller) *MockGetARCRDriverOrderInfo {
	mock := &MockGetARCRDriverOrderInfo{ctrl: ctrl}
	mock.recorder = &MockGetARCRDriverOrderInfoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGetARCRDriverOrderInfo) EXPECT() *MockGetARCRDriverOrderInfoMockRecorder {
	return m.recorder
}

// GetAR mocks base method.
func (m *MockGetARCRDriverOrderInfo) GetAR(startDate, endDate time.Time) (model.DailyCount, float64, map[model.Service]float64) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAR", startDate, endDate)
	ret0, _ := ret[0].(model.DailyCount)
	ret1, _ := ret[1].(float64)
	ret2, _ := ret[2].(map[model.Service]float64)
	return ret0, ret1, ret2
}

// GetAR indicates an expected call of GetAR.
func (mr *MockGetARCRDriverOrderInfoMockRecorder) GetAR(startDate, endDate interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAR", reflect.TypeOf((*MockGetARCRDriverOrderInfo)(nil).GetAR), startDate, endDate)
}

// GetCR mocks base method.
func (m *MockGetARCRDriverOrderInfo) GetCR(startDate, endDate time.Time) (model.DailyCount, float64, map[model.Service]float64) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCR", startDate, endDate)
	ret0, _ := ret[0].(model.DailyCount)
	ret1, _ := ret[1].(float64)
	ret2, _ := ret[2].(map[model.Service]float64)
	return ret0, ret1, ret2
}

// GetCR indicates an expected call of GetCR.
func (mr *MockGetARCRDriverOrderInfoMockRecorder) GetCR(startDate, endDate interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCR", reflect.TypeOf((*MockGetARCRDriverOrderInfo)(nil).GetCR), startDate, endDate)
}
