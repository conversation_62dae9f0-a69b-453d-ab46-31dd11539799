package model

import (
	"fmt"
	"reflect"
	"time"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/absinthe/crypt"
)

type RequestUpdateDriverProfile struct {
	ID                  primitive.ObjectID                `bson:"_id,omitempty" json:"-"`
	DriverID            string                            `bson:"driver_id" json:"driverId"`
	SectionID           string                            `bson:"section_id" json:"sectionId"`
	SectionLabel        string                            `bson:"section_label" json:"sectionLabel"`
	SectionName         RequestUpdateSectionName          `bson:"section_name" json:"sectionName"`
	Status              RequestProfileStatus              `bson:"status" json:"status"`
	UpdateDriverProfile UpdateDriverProfile               `bson:"update_driver_profile" json:"updateDriverProfile"`
	Fields              []DriverRequestUpdateProfileField `bson:"fields" json:"fields"`
	MessageToDriver     string                            `bson:"message_to_driver" json:"messageToDriver"`
	CreatedAt           time.Time                         `bson:"created_at" json:"createdAt"`
	UpdatedAt           time.Time                         `bson:"updated_at" json:"updatedAt"`
	CompletedAt         time.Time                         `bson:"completed_at,omitempty" json:"completedAt,omitempty"`
}

type RequestProfileStatus string

const (
	// RequestProfileStatusRequestUpdate - To tell a Driver that his profile need to be updated
	RequestProfileStatusRequestUpdate RequestProfileStatus = "REQUESTED_UPDATE"

	// RequestProfileStatusUpdatePending - A Driver already updated a profile and wait for an Admin to approve
	RequestProfileStatusUpdatePending RequestProfileStatus = "UPDATE_PENDING"

	// RequestProfileStatusCancel - An Admin discarded a request, no need action from Driver to re-upload
	RequestProfileStatusCancel RequestProfileStatus = "CANCELLED"

	// RequestProfileStatusComplete - An Admin approved a request
	RequestProfileStatusComplete RequestProfileStatus = "COMPLETED"

	// RequestProfileStatusRequestReUpdate - TBD
	RequestProfileStatusRequestReUpdate RequestProfileStatus = "REQUESTED_REUPDATE"
)

type UpdateDriverProfile struct {
	PersonalInfoRequest      `bson:",inline"`
	CitizenInfoRequest       `bson:",inline"`
	DriverLicenseInfoRequest `bson:",inline"`
	VehicleInfoRequest       `bson:",inline"`
	BankingInfoRequest       `bson:",inline"`
}

type PersonalInfoRequest struct {
	AvatarURL            string                      `bson:"avatar_url,omitempty" json:"avatarUrl,omitempty"`
	EmergencyPhone       crypt.LazyEncryptedString   `bson:"emergency_phone,omitempty" json:"emergencyPhone,omitempty"`
	StrongEmergencyPhone crypt.StrongEncryptedString `bson:"strong_emergency_phone,omitempty" json:"strongEmergencyPhone,omitempty"`
	LinemanEquipment     bool                        `bson:"lineman_equipment,omitempty" json:"linemanEquipment,omitempty"`
}

type CitizenInfoRequest struct {
	CitizenIDCardPhotoURL string     `bson:"citizen_id_card_photo_url,omitempty" json:"citizenIdCardPhotoUrl,omitempty"`
	CitizenIDExpiredDate  *time.Time `bson:"citizen_id_expired_date,omitempty" json:"citizenIdExpiredDate,omitempty"`
}
type DriverLicenseInfoRequest struct {
	DriverLicenseID             crypt.LazyEncryptedString `bson:"driver_license_id,omitempty" json:"driverLicenseId,omitempty"`
	DriverLicenseExpirationDate *time.Time                `bson:"driver_license_expiration_date,omitempty" json:"driverLicenseExpirationDate,omitempty"`
	DriverLicensePhotoURL       string                    `bson:"driver_license_photo_url,omitempty" json:"driverLicensePhotoUrl,omitempty"`
}

type VehicleInfoRequest struct {
	VehicleRegistrationPhotoURL string                    `bson:"vehicle_registration_photo_url,omitempty" json:"vehicleRegistrationPhotoUrl,omitempty"`
	VehiclePhotoURL             string                    `bson:"vehicle_photo_url,omitempty" json:"vehiclePhotoUrl,omitempty"`
	VehiclePlateNumber          crypt.LazyEncryptedString `bson:"vehicle_plate_number,omitempty" json:"vehiclePlateNumber,omitempty"`
	LegislationPhotoURL         string                    `bson:"legislation_photo_url,omitempty" json:"legislationPhotoUrl,omitempty"`
	LegislationExpiredDate      *time.Time                `bson:"legislation_expired_date,omitempty" json:"legislationExpiredDate,omitempty"`
	LendingVehiclePhotoURL      string                    `bson:"lending_vehicle_photo_url,omitempty" json:"lendingVehiclePhotoUrl,omitempty"`
	VehicleRegistrationDate     *time.Time                `bson:"vehicle_registration_date,omitempty" json:"vehicleRegistrationDate,omitempty"`
}

type BankingInfoRequest struct {
	BookBankPhotoURL string                    `bson:"book_bank_photo_url,omitempty" json:"bookBankPhotoUrl,omitempty"`
	BankName         crypt.LazyEncryptedString `bson:"bank_name,omitempty" json:"bankName,omitempty"`
	BankAccount      crypt.LazyEncryptedString `bson:"bank_account,omitempty" json:"bankAccount,omitempty"`
	BankAccountName  crypt.LazyEncryptedString `bson:"bank_account_name,omitempty" json:"bankAccountName,omitempty"`
}

type RequestProfileField string

const (
	// PersonalInfoRequest
	ProfileFieldAvatarPhoto                RequestProfileField = "AVATAR_PHOTO_URL"
	ProfileFieldEmergencyPhoneNumber       RequestProfileField = "EMERGENCY_PHONE_NUMBER"
	ProfileFieldStrongEmergencyPhoneNumber RequestProfileField = "STRONG_EMERGENCY_PHONE_NUMBER"
	ProfileFieldLinemanEquipment           RequestProfileField = "LINEMAN_EQUIPMENT"
	// CitizenIDInfoRequest
	ProfileFieldCitizenIDPhoto          RequestProfileField = "CITIZEN_ID_PHOTO"
	ProfileFieldCitizenIDExpirationDate RequestProfileField = "CITIZEN_ID_EXP"
	// DriverLicenseInfoRequest
	ProfileFieldDriverLicenseNumber         RequestProfileField = "DRIVER_LICENSE_NUMBER"
	ProfileFieldDriverLicensePhoto          RequestProfileField = "DRIVER_LICENSE_PHOTO"
	ProfileFieldDriverLicenseExpirationDate RequestProfileField = "DRIVER_LICENSE_EXP"
	// VehicleInfoRequest
	ProfileFieldVehicleRegistrationPhoto         RequestProfileField = "VEHICLE_REGISTRATION_PHOTO"
	ProfileFieldVehicleRegistrationDate          RequestProfileField = "VEHICLE_REGISTRATION_DATE"
	ProfileFieldVehiclePhoto                     RequestProfileField = "VEHICLE_PHOTO"
	ProfileFieldVehiclePlateNumber               RequestProfileField = "VEHICLE_PLATE_NUMBER"
	ProfileFieldVehicleLegislationPhoto          RequestProfileField = "VEHICLE_LEGISLATION_PHOTO"
	ProfileFieldVehicleLegislationExpirationDate RequestProfileField = "VEHICLE_LEGISLATION_EXP"
	ProfileFieldLendingVehiclePhoto              RequestProfileField = "LENDING_VEHICLE_PHOTO"
	// BankingInfoRequest
	ProfileFieldBookBankPhoto   RequestProfileField = "BOOK_BANK_PHOTO"
	ProfileFieldBankName        RequestProfileField = "BANK_NAME"
	ProfileFieldBankAccount     RequestProfileField = "BANK_ACCOUNT"
	ProfileFieldBankAccountName RequestProfileField = "BANK_ACCOUNT_NAME"
)

type ProfileFieldType string

const (
	ProfileFieldText         ProfileFieldType = "text"
	ProfileFieldImage        ProfileFieldType = "image"
	ProfileFieldProfileImage ProfileFieldType = "profile_image"
	// ProfileFieldFlag is used for a field required from a Driver but don't take any action for response
	//
	// For example, a LINEMAN Equipment field that will redirect the driver from Client to a form page
	// and an Admin will check an uploaded data from the form later
	ProfileFieldFlag ProfileFieldType = "flag"
)

type RequestProfileStatusList []RequestProfileStatus

func GetInProgressRequestProfileStatus() RequestProfileStatusList {
	return []RequestProfileStatus{
		RequestProfileStatusRequestUpdate, RequestProfileStatusUpdatePending,
	}
}

func GetCompletedRequestProfileStatus() RequestProfileStatusList {
	return []RequestProfileStatus{
		RequestProfileStatusComplete, RequestProfileStatusCancel,
	}
}

func (rpsl RequestProfileStatusList) IsContain(src RequestProfileStatus) bool {
	for _, item := range rpsl {
		if item == src {
			return true
		}
	}
	return false
}

type RequestUpdateFieldSetter struct {
	source      interface{}
	destination interface{}
	isValid     bool
}

func NewRequestUpdateFieldSetter(src interface{}, dest interface{}) (RequestUpdateFieldSetter, error) {
	newRequestUpdateFieldSetter := RequestUpdateFieldSetter{
		source:      src,
		destination: dest,
		isValid:     true,
	}
	if newRequestUpdateFieldSetter.isFieldInvalid(newRequestUpdateFieldSetter.source) {
		return RequestUpdateFieldSetter{}, fmt.Errorf("source is invalid")
	}
	if newRequestUpdateFieldSetter.isFieldInvalid(newRequestUpdateFieldSetter.destination) {
		return RequestUpdateFieldSetter{}, errors.New("destination is invalid")
	}
	if reflect.TypeOf(newRequestUpdateFieldSetter.source) != reflect.TypeOf(newRequestUpdateFieldSetter.destination) {
		return RequestUpdateFieldSetter{}, fmt.Errorf("invalid type of source [%v] and destination [%v]", reflect.TypeOf(newRequestUpdateFieldSetter.source), reflect.TypeOf(newRequestUpdateFieldSetter.destination))
	}
	return newRequestUpdateFieldSetter, nil
}

func (fc RequestUpdateFieldSetter) IsValid() bool {
	return fc.isValid
}

func (fc RequestUpdateFieldSetter) isFieldInvalid(src interface{}) bool {
	switch item := src.(type) {
	case *string:
		return item == nil
	case **time.Time:
		return item == nil
	case *bool:
		return item == nil
	case *crypt.LazyEncryptedString:
		return item == nil
	case *crypt.StrongEncryptedString:
		return item == nil
	}
	return true
}

func (fc RequestUpdateFieldSetter) isFieldEmpty(src interface{}) bool {
	switch item := src.(type) {
	case *string:
		return item != nil && *item == ""
	case **time.Time:
		return *item == nil || (*item).IsZero()
	case *bool:
		return item != nil
	case *crypt.LazyEncryptedString:
		return item != nil && (*item).IsZero()
	case *crypt.StrongEncryptedString:
		return item != nil && (*item).IsZero()
	}
	return true
}

func (fc RequestUpdateFieldSetter) IsSourceEmpty() bool {
	return fc.isFieldEmpty(fc.source)
}

func (fc RequestUpdateFieldSetter) getString(src interface{}) string {
	switch item := src.(type) {
	case **time.Time:
		if item != nil && *item != nil {
			return (*item).Format(time.DateTime)
		}
	default:
		return fmt.Sprintf("%v", reflect.Indirect(reflect.ValueOf(item)))
	}
	return ""
}

func (fc RequestUpdateFieldSetter) GetString() (string, string) {
	return fc.getString(fc.source), fc.getString(fc.destination)
}

func (fc RequestUpdateFieldSetter) Set() {
	if fc.isFieldEmpty(fc.source) {
		return
	}
	switch item := fc.destination.(type) {
	case *string:
		*item = *(fc.source.(*string))
	case **time.Time:
		*item = *(fc.source.(**time.Time))
	case *bool:
		*item = *(fc.source.(*bool))
	case *crypt.LazyEncryptedString:
		*item = *(fc.source.(*crypt.LazyEncryptedString))
	case *crypt.StrongEncryptedString:
		*item = *(fc.source.(*crypt.StrongEncryptedString))
	}
}

func NewRequestUpdateFieldSetterByField(field RequestProfileField, srcDriver *Driver, src *UpdateDriverProfile) (RequestUpdateFieldSetter, ProfileFieldType, error) {
	var newRequestUpdateFieldSetter RequestUpdateFieldSetter
	fieldType := ProfileFieldText
	var err error
	switch field {
	// PersonalInfoRequest
	case ProfileFieldAvatarPhoto:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.AvatarURL, &srcDriver.AvatarURL)
		fieldType = ProfileFieldProfileImage
	case ProfileFieldEmergencyPhoneNumber:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.EmergencyPhone, &srcDriver.EmergencyPhone)
	case ProfileFieldStrongEmergencyPhoneNumber:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.StrongEmergencyPhone, &srcDriver.StrongEmergencyPhone)

	case ProfileFieldLinemanEquipment:
		fieldType = ProfileFieldFlag
	// CitizenIDInfoRequest
	case ProfileFieldCitizenIDPhoto:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.CitizenIDCardPhotoURL, &srcDriver.CitizenIDCardPhotoURL)
		fieldType = ProfileFieldImage
	case ProfileFieldCitizenIDExpirationDate:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.CitizenIDExpiredDate, &srcDriver.CitizenIDExpiredDate)
		// DriverLicenseInfoRequest
	case ProfileFieldDriverLicenseNumber:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.DriverLicenseID, &srcDriver.DriverLicense.ID)
	case ProfileFieldDriverLicensePhoto:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.DriverLicensePhotoURL, &srcDriver.DriverLicense.PhotoURL)
		fieldType = ProfileFieldImage
	case ProfileFieldDriverLicenseExpirationDate:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.DriverLicenseExpirationDate, &srcDriver.DriverLicense.ExpirationDate)
	// VehicleInfoRequest
	case ProfileFieldVehicleRegistrationPhoto:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.VehicleRegistrationPhotoURL, &srcDriver.Vehicle.RegistrationPhotoURL)
		fieldType = ProfileFieldImage
	case ProfileFieldVehicleRegistrationDate:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.VehicleRegistrationDate, &srcDriver.Vehicle.RegistrationDate)
	case ProfileFieldVehiclePhoto:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.VehiclePhotoURL, &srcDriver.Vehicle.PhotoURL)
		fieldType = ProfileFieldImage
	case ProfileFieldVehiclePlateNumber:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.VehiclePlateNumber, &srcDriver.Vehicle.PlateNumber)
	case ProfileFieldVehicleLegislationPhoto:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.LegislationPhotoURL, &srcDriver.Vehicle.LegislationPhotoURL)
		fieldType = ProfileFieldImage
	case ProfileFieldVehicleLegislationExpirationDate:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.LegislationExpiredDate, &srcDriver.Vehicle.LegislationExpiredDate)
	case ProfileFieldLendingVehiclePhoto:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.LendingVehiclePhotoURL, &srcDriver.Vehicle.LendingVehiclePhotoURL)
		fieldType = ProfileFieldImage
	// BankingInfoRequest
	case ProfileFieldBookBankPhoto:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.BookBankPhotoURL, &srcDriver.Banking.PhotoURL)
		fieldType = ProfileFieldImage
	case ProfileFieldBankName:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.BankName, &srcDriver.Banking.BankName)
	case ProfileFieldBankAccount:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.BankAccount, &srcDriver.Banking.Account)
	case ProfileFieldBankAccountName:
		newRequestUpdateFieldSetter, err = NewRequestUpdateFieldSetter(&src.BankAccountName, &srcDriver.Banking.AccountHolder)
	}
	return newRequestUpdateFieldSetter, fieldType, err
}
