//go:build integration_test
// +build integration_test

package order_test

import (
	"context"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	legacymock "github.com/golang/mock/gomock"
	"github.com/joho/godotenv"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/mock/gomock"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/order"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
)

func TestServiceIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(ServiceIntegrationTestSuite))
}

type ServiceIntegrationTestSuite struct {
	suite.Suite

	container *ittest.IntegrationTestContainer
	cleanUp   func()

	ctx        context.Context
	legacyCtrl *legacymock.Controller
	ctrl       *gomock.Controller

	underTest order.Service
}

func (s *ServiceIntegrationTestSuite) SetupSuite() {
	err := godotenv.Load(ittest.ToAbsPath("../../.env.integration"))
	s.NoError(err)
	gin.SetMode(gin.ReleaseMode)
}

func (s *ServiceIntegrationTestSuite) SetupSubTest() {
	err := os.Setenv("ENV", "integration")
	s.NoError(err)
	s.legacyCtrl = legacymock.NewController(s.T())
	s.ctrl = gomock.NewController(s.T())
	s.ctx = context.Background()

	ittest.NewContainer(s.T())
	s.container, s.cleanUp, err = ittest.InitializeContainer(s.T(), s.legacyCtrl, s.ctrl)
	s.NoError(err)
	s.NotNil(s.container)

	err = s.container.Fixtures.InitFixture(s.container.DBConnectionForTest, "fixtures_order_service")
	s.NoError(err)

	s.underTest = order.ProvideOrderService(
		s.container.MongoOrderRepository,
		s.container.PendingTransactionService,
		s.container.FeatureFlagService,
	)
}

func (s *ServiceIntegrationTestSuite) TearDownSubTest() {
	s.cleanUp()
}

func (s *ServiceIntegrationTestSuite) TestUpdateQRPromptPayInfoStatusAndUpdatePendingTransactionStatus() {
	s.Run("success update qr promptpay info status to reject by admin", func() {
		beforeResult := s.container.DBConnectionForTest.Database().Collection("orders").FindOne(s.ctx, bson.M{"order_id": "LMT-1"})
		s.NoError(beforeResult.Err())
		var beforeResultOrder model.Order
		err := beforeResult.Decode(&beforeResultOrder)
		s.NoError(err)
		s.Equal("LMT-1", beforeResultOrder.OrderID)
		s.Equal(model.QRPromptPayStatusWaitingForPayment, beforeResultOrder.PriceSummary().DeliveryFee.QRPromptPayInfo.Status)

		err = s.underTest.UpdateQRPromptPayInfoStatusAndUpdatePendingTransactionStatus(s.ctx, "LMT-1", model.QRPromptPayStatusRejectedByAdmin)
		s.NoError(err)

		result := s.container.DBConnectionForTest.Database().Collection("orders").FindOne(s.ctx, bson.M{"order_id": "LMT-1"})
		s.NoError(result.Err())
		var resultOrder model.Order
		err = result.Decode(&resultOrder)
		s.NoError(err)
		s.Equal("LMT-1", resultOrder.OrderID)
		s.Equal(model.QRPromptPayStatusRejectedByAdmin, resultOrder.PriceSummary().DeliveryFee.QRPromptPayInfo.Status)
	})
}
