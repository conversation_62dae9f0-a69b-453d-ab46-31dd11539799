package service

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func Test_effectiveServiceTypes(t *testing.T) {
	t.<PERSON>()
	type args struct {
		serviceAreas           []model.Service
		driverServiceTypes     []model.Service
		optedOutServices       []model.Service
		optOutAllowedServices  []model.Service
		isMergeFoodMartEnabled bool
	}
	type testCase struct {
		name                       string
		args                       args
		want                       []model.EffectiveServiceType
		checkIsMergeOptOutFoodMart *bool
	}

	tests := []testCase{}

	// area is food, mart, messenger
	{
		name := "area is food mart messenger"
		serviceAreas := []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger}
		tc := []testCase{
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
		}

		tests = append(tests, tc...)
	}

	// area is food and mart
	{
		name := "area is food and mart"
		serviceAreas := []model.Service{model.ServiceFood, model.ServiceMart}
		tc := []testCase{
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
		}

		tests = append(tests, tc...)
	}

	// area is food and messenger
	{
		name := "area is food and messenger"
		serviceAreas := []model.Service{model.ServiceFood, model.ServiceMessenger}
		tc := []testCase{
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
		}

		tests = append(tests, tc...)
	}

	// area is mart and messenger
	{
		name := "area is mart and messenger"
		serviceAreas := []model.Service{model.ServiceMart, model.ServiceMessenger}
		tc := []testCase{
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
		}

		tests = append(tests, tc...)
	}

	// area is food
	{
		name := "area is food"
		serviceAreas := []model.Service{model.ServiceFood}
		tc := []testCase{
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
		}

		tests = append(tests, tc...)
	}

	// area is mart
	{
		name := "area is mart"
		serviceAreas := []model.Service{model.ServiceMart}
		tc := []testCase{
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
		}

		tests = append(tests, tc...)
	}

	// area is messenger
	{
		name := "area is messenger"
		serviceAreas := []model.Service{model.ServiceMessenger}
		tc := []testCase{
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
			},
		}

		tests = append(tests, tc...)
	}

	// area is food, mart, messenger, bike
	{
		name := "area is food mart messenger bike"
		serviceAreas := []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger, model.ServiceBike}
		tc := []testCase{
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger, model.ServiceBike},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Visible,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Grayout,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Grayout,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Grayout,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart, model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Grayout,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceFood},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Grayout,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMart},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Grayout,
					},
				},
			},
			{
				name: name,
				args: args{
					serviceAreas:       serviceAreas,
					driverServiceTypes: []model.Service{model.ServiceMessenger},
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Visible,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Grayout,
					},
				},
			},
		}

		tests = append(tests, tc...)
	}
	{
		tests = append(tests, []testCase{
			{
				name: "happy case for merge food mike",
				args: args{
					serviceAreas:           []model.Service{model.ServiceFood, model.ServiceMart},
					driverServiceTypes:     []model.Service{model.ServiceFood, model.ServiceMart},
					optedOutServices:       []model.Service{},
					optOutAllowedServices:  []model.Service{model.ServiceFood, model.ServiceMart},
					isMergeFoodMartEnabled: true,
				},
				want: []model.EffectiveServiceType{
					{
						Type:          model.ServiceFood,
						Visibility:    model.Visible,
						OptOutAllowed: true,
					},
					{
						Type:          model.ServiceMart,
						Visibility:    model.Visible,
						OptOutAllowed: true,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
				checkIsMergeOptOutFoodMart: types.NewBool(true),
			},
			{
				name: "doesn't merge food mart if rider can only do food",
				args: args{
					serviceAreas:           []model.Service{model.ServiceFood, model.ServiceMart},
					driverServiceTypes:     []model.Service{model.ServiceFood},
					optedOutServices:       []model.Service{},
					optOutAllowedServices:  []model.Service{model.ServiceFood, model.ServiceMart},
					isMergeFoodMartEnabled: true,
				},
				want: []model.EffectiveServiceType{
					{
						Type:          model.ServiceFood,
						Visibility:    model.Visible,
						OptOutAllowed: true,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
				checkIsMergeOptOutFoodMart: types.NewBool(false),
			},
			{
				name: "doesn't merge food mart if rider can only do mart",
				args: args{
					serviceAreas:           []model.Service{model.ServiceFood, model.ServiceMart},
					driverServiceTypes:     []model.Service{model.ServiceMart},
					optedOutServices:       []model.Service{},
					optOutAllowedServices:  []model.Service{model.ServiceFood, model.ServiceMart},
					isMergeFoodMartEnabled: true,
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:          model.ServiceMart,
						Visibility:    model.Visible,
						OptOutAllowed: true,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
				checkIsMergeOptOutFoodMart: types.NewBool(false),
			},
			{
				name: "doesn't merge food mart if rider can only do bike",
				args: args{
					serviceAreas:           []model.Service{model.ServiceFood, model.ServiceMart},
					driverServiceTypes:     []model.Service{model.ServiceBike},
					optedOutServices:       []model.Service{},
					optOutAllowedServices:  []model.Service{model.ServiceFood, model.ServiceMart},
					isMergeFoodMartEnabled: true,
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Grayout,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Hide,
					},
				},
				checkIsMergeOptOutFoodMart: types.NewBool(false),
			},
			{
				name: "does not merge if client only has mart",
				args: args{
					serviceAreas:           []model.Service{model.ServiceMart, model.ServiceBike},
					driverServiceTypes:     []model.Service{model.ServiceFood, model.ServiceMart},
					optedOutServices:       []model.Service{},
					optOutAllowedServices:  []model.Service{model.ServiceFood, model.ServiceMart},
					isMergeFoodMartEnabled: true,
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:          model.ServiceMart,
						Visibility:    model.Visible,
						OptOutAllowed: true,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Grayout,
					},
				},
				checkIsMergeOptOutFoodMart: types.NewBool(false),
			},
			{
				name: "does not merge if client only has food",
				args: args{
					serviceAreas:           []model.Service{model.ServiceFood, model.ServiceBike},
					driverServiceTypes:     []model.Service{model.ServiceFood, model.ServiceMart},
					optedOutServices:       []model.Service{},
					optOutAllowedServices:  []model.Service{model.ServiceFood, model.ServiceMart},
					isMergeFoodMartEnabled: true,
				},
				want: []model.EffectiveServiceType{
					{
						Type:          model.ServiceFood,
						Visibility:    model.Visible,
						OptOutAllowed: true,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Grayout,
					},
				},
				checkIsMergeOptOutFoodMart: types.NewBool(false),
			},
			{
				name: "if merge food mart, doesn't force food or mart when client area has neither",
				args: args{
					serviceAreas:           []model.Service{model.ServiceBike},
					driverServiceTypes:     []model.Service{model.ServiceFood, model.ServiceMart},
					optedOutServices:       []model.Service{},
					optOutAllowedServices:  []model.Service{model.ServiceFood, model.ServiceMart},
					isMergeFoodMartEnabled: true,
				},
				want: []model.EffectiveServiceType{
					{
						Type:       model.ServiceFood,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMart,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceMessenger,
						Visibility: model.Hide,
					},
					{
						Type:       model.ServiceBike,
						Visibility: model.Grayout,
					},
				},
				checkIsMergeOptOutFoodMart: types.NewBool(false),
			},
		}...)
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, isMergeOptOutFoodMart := effectiveServiceTypes(tt.args.serviceAreas, tt.args.driverServiceTypes, tt.args.optedOutServices, tt.args.optOutAllowedServices, tt.args.isMergeFoodMartEnabled)

			assert.ElementsMatch(t, got, tt.want)
			if tt.checkIsMergeOptOutFoodMart != nil {
				assert.Equal(t, *tt.checkIsMergeOptOutFoodMart, isMergeOptOutFoodMart)
			}
		})
	}
}
