package service

//go:generate mockgen -source=./supply_positioning_recommender_service.go -destination=./mock_service/mock_supply_positioning_recommender_service.go -package=mock_service

import (
	"context"
	"fmt"
	"time"

	"github.com/hibiken/asynq"
	"github.com/panjf2000/ants/v2"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
	"github.com/uber/h3-go"

	driver_provisionv1 "git.wndv.co/go/proto/lineman/driver_provision/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/asynqclient"
	"git.wndv.co/lineman/fleet-distribution/internal/asynqtask"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/driverprovision"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type SupplyPositioningRecommenderService interface {
	Submit(ctx context.Context, req model.SupplyPositioningRecommenderServiceReq, svcs ...model.Service) error
}

var _ SupplyPositioningRecommenderService = (*supplyPositioningRecommenderServiceImpl)(nil)
var _ prometheus.Collector = (*supplyPositioningRecommenderServiceImpl)(nil)

var _ SupplyPositioningRecommenderService = (*noOpsSupplyPositioningRecommenderServiceImpl)(nil)

type noOpsSupplyPositioningRecommenderServiceImpl struct{}

// Submit implements SupplyPositioningRecommenderService.
func (*noOpsSupplyPositioningRecommenderServiceImpl) Submit(ctx context.Context, req model.SupplyPositioningRecommenderServiceReq, svcs ...model.Service) error {
	return nil
}

type serviceMetric struct {
	workerPoolFree               *prometheus.Desc
	workerPoolRunning            *prometheus.Desc
	workerPoolCap                *prometheus.Desc
	workerPoolUtilization        *prometheus.Desc
	workerProcessingTimeMs       metric.Histogram
	workerFailedPoolOverload     metric.Counter
	recommendationProcessedTotal metric.Counter
	recommendationSucceededTotal metric.Counter
	recommendationFailedTotal    metric.Counter
}

func newServiceMetric(meter metric.Meter) *serviceMetric {
	namespace := "supply_positioning"

	workerPoolFree := prometheus.NewDesc(
		prometheus.BuildFQName(namespace, "", "worker_pool_free"),
		"Number of available goroutines to work",
		[]string{}, nil,
	)
	workerPoolRunning := prometheus.NewDesc(
		prometheus.BuildFQName(namespace, "", "worker_pool_running"),
		"Number of currently running goroutines",
		[]string{}, nil,
	)
	workerPoolCap := prometheus.NewDesc(
		prometheus.BuildFQName(namespace, "", "worker_pool_cap"),
		"Number of capacity of this pool",
		[]string{}, nil,
	)
	workerPoolUtilization := prometheus.NewDesc(
		prometheus.BuildFQName(namespace, "", "worker_pool_utilization"),
		"Utilization rate of this pool",
		[]string{}, nil,
	)
	workerProcessingTimeMs := meter.GetHistogram(
		prometheus.BuildFQName(namespace, "", "worker_pool_processing_time_ms"),
		"Task processing time of this pool",
		metric.DefaultHistogramBucket,
	)
	workerFailedPoolOverload := meter.GetCounter(
		prometheus.BuildFQName(namespace, "", "worker_pool_failed_pool_overload"),
		"Number of task failed to submit due to pool overload",
	)
	recommendationProcessedTotal := meter.GetCounter(
		prometheus.BuildFQName(namespace, "", "recommendation_processed_total"),
		"Number of recommendation processed (both succeeded and failed);",
	)
	recommendationSucceededTotal := meter.GetCounter(
		prometheus.BuildFQName(namespace, "", "recommendation_succeeded_total"),
		"Number of recommendation succeeded total",
		"action",
	)
	recommendationFailedTotal := meter.GetCounter(
		prometheus.BuildFQName(namespace, "", "recommendation_failed_total"),
		"Number of recommendation failed total",
		"category", "error_code",
	)

	return &serviceMetric{
		workerPoolFree:               workerPoolFree,
		workerPoolRunning:            workerPoolRunning,
		workerPoolCap:                workerPoolCap,
		workerPoolUtilization:        workerPoolUtilization,
		workerProcessingTimeMs:       workerProcessingTimeMs,
		workerFailedPoolOverload:     workerFailedPoolOverload,
		recommendationProcessedTotal: recommendationProcessedTotal,
		recommendationSucceededTotal: recommendationSucceededTotal,
		recommendationFailedTotal:    recommendationFailedTotal,
	}
}

type supplyPositioningRecommenderServiceImpl struct {
	wk                         *safe.Worker
	atomicSupplyPositionConfig *config.AtomicSupplyPositioningConfig
	client                     driverprovision.DriverProvisionClient
	driverRepo                 repository.DriverRepository
	serviceAreaRepo            repository.ServiceAreaRepository
	featureFlagSvc             featureflag.Service
	notifier                   Notifier
	serviceMetric              *serviceMetric
	asynqClient                asynqclient.AsynqClient
}

func NewSupplyPositioningRecommenderService(wk *safe.Worker, cfg *config.AtomicSupplyPositioningConfig, c driverprovision.DriverProvisionClient,
	driverRepo repository.DriverRepository, saRepo repository.ServiceAreaRepository, ffSvc featureflag.Service, n Notifier, metric *serviceMetric, ac asynqclient.AsynqClient) *supplyPositioningRecommenderServiceImpl {
	return &supplyPositioningRecommenderServiceImpl{
		wk:                         wk,
		atomicSupplyPositionConfig: cfg,
		client:                     c,
		driverRepo:                 driverRepo,
		serviceAreaRepo:            saRepo,
		featureFlagSvc:             ffSvc,
		notifier:                   n,
		serviceMetric:              metric,
		asynqClient:                ac,
	}
}

func ProvideSupplyPositioningRecommenderService(
	cfg *config.AtomicSupplyPositioningConfig,
	client driverprovision.DriverProvisionClient,
	driverRepo repository.DriverRepository,
	saRepo repository.ServiceAreaRepository,
	ffSvc featureflag.Service,
	notifier Notifier,
	meter metric.Meter,
	asynqClient *asynq.Client,
) (SupplyPositioningRecommenderService, func()) {
	c := cfg.Get()
	if !c.SupplyPositioningEnabled {
		return &noOpsSupplyPositioningRecommenderServiceImpl{}, func() {}
	}
	wk, cleanup := safe.NewWorker(c.SupplyPositioningWorkerPoolSize)
	serviceMetric := newServiceMetric(meter)

	svc := NewSupplyPositioningRecommenderService(wk, cfg, client, driverRepo, saRepo, ffSvc, notifier, serviceMetric, asynqClient)

	meter.RegisterCollector(svc)
	return svc, cleanup
}

// Submit implements RecommenderService.
func (svc *supplyPositioningRecommenderServiceImpl) Submit(ctx context.Context, req model.SupplyPositioningRecommenderServiceReq, svcs ...model.Service) error {
	// only online driver is eligble
	if req.DriverStatus != model.StatusOnline {
		return nil
	}

	// check selective driver and region
	cfg := svc.atomicSupplyPositionConfig.Get()

	if !req.DriverIsSupplyPositioning {
		return nil
	}

	if _, exist := cfg.RegionsMap[req.DriverRegion]; !exist {
		return nil
	}

	// service types validation
	if svc.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, featureflag.SupplyPositioningServices.Name) {
		area, err := svc.serviceAreaRepo.GetByRegion(ctx, req.DriverRegion)
		if err != nil {
			return err
		}
		valid := isDriverValidServices(svcs, area.SupplyPositioningServices)
		if !valid {
			return nil
		}
	}

	err := safe.WorkerPoolGoFuncWithCtx(ctx, svc.wk, svc.metric(req, svc.task(req)))
	if err != nil {
		if errors.Is(err, ants.ErrPoolOverload) {
			logrus.Error(formatLog(fmt.Sprintf("unable to sumbit task due to pool is overload: %v", err)))
			svc.serviceMetric.workerFailedPoolOverload.Inc()
			return err
		}
		return err
	}
	return nil
}

type successAction string

// String implements fmt.Stringer.
func (s successAction) String() string {
	return string(s)
}

var _ fmt.Stringer = (*successAction)(nil)

const (
	createRecommendation    successAction = "create_recommendation"
	enterRecommendationArea successAction = "enter_recommendation_area"
)

type taskSuccessResult struct {
	action successAction
}

var (
	errRecoIdleTimeStartPointIsNil = errors.New("reco idle time start point is nil")
)

var _ error = (*valdiationErr)(nil)

type valdiationErr struct {
	supplyPositioningServiceError
}

func (e *valdiationErr) Error() string {
	return e.error.Error()
}

type systemErr struct {
	supplyPositioningServiceError
}

func (e *systemErr) Error() string {
	return e.error.Error()
}

var _ fmt.Stringer = (*supplyPositioningServiceErrorCode)(nil)

type supplyPositioningServiceErrorCode int

// String implements fmt.Stringer.
func (s supplyPositioningServiceErrorCode) String() string {
	return fmt.Sprintf("%d", s)
}

const (
	defaultValidationErrorCode supplyPositioningServiceErrorCode = iota + 1000
	noRecommendationActiveErrorCode
	underCooldownPeriodErrorCode
	recoIdleTimeStartPointNilErrorCode
	idlenessNotExceedThresholdErrorCode
	emptyH3SuggestionErrorCode
	outOfRecommendationAreaErrorCode
	duplicateCreateRecommendationRequestErrorCode
)

const (
	defaultSystemErrorCode supplyPositioningServiceErrorCode = iota + 2000
	callEnterRecommendationErrorCode
	calculateIdlenessErrorCode
	callCreateRecommendationErrorCode
	dbSetRecoIdleTimeStartPointErrorCode
	parseExpireAtErrorCode
	dbSetH3RecommendationErrorCode
	newExpireTaskErrorCode
	alreadyExpiredErrorCode
	enqueueTaskErrorCode
	callNotifyErrorCode
)

type supplyPositioningServiceError struct {
	error
	code supplyPositioningServiceErrorCode
}

func vErr(err error, code supplyPositioningServiceErrorCode) *valdiationErr {
	return &valdiationErr{
		supplyPositioningServiceError: supplyPositioningServiceError{
			error: err,
			code:  code,
		},
	}
}

func sErr(err error, code supplyPositioningServiceErrorCode) *systemErr {
	return &systemErr{
		supplyPositioningServiceError: supplyPositioningServiceError{
			error: err,
			code:  code,
		},
	}
}

func isDriverValidServices(driverServices model.Services, areaServices model.Services) bool {
	// current area not set any services, don't allow to continue
	if len(areaServices) == 0 {
		return false
	}

	// len 0 will treat as all services
	if len(driverServices) == 0 {
		return true
	}

	m := make(map[model.Service]bool)
	for _, s := range areaServices {
		m[s] = true
	}

	for _, ds := range driverServices {
		if _, found := m[ds]; found {
			return true
		}
	}

	return false
}

func (svc *supplyPositioningRecommenderServiceImpl) task(req model.SupplyPositioningRecommenderServiceReq) func(ctx context.Context) (taskSuccessResult, error) {
	return func(ctx context.Context) (taskSuccessResult, error) {
		now := timeutil.GetTimeFromContext(ctx)

		// check is on recommendation?
		if req.H3Recommendation.IsActive(now) {
			if len(req.H3Recommendation.Areas) > 0 {
				h3Resolution := h3.Resolution(h3.FromString(req.H3Recommendation.Areas[0].H3ID))
				driverH3 := h3.ToString(h3.FromGeo(h3.GeoCoord{Latitude: req.DriverLatitude, Longitude: req.DriverLongtitude}, h3Resolution))
				_, exists := req.H3Recommendation.H3AreasIndex[driverH3]
				if !exists {
					err := fmt.Errorf("driver has active recommendation and still on the way to recommendation areas")
					return taskSuccessResult{}, vErr(err, outOfRecommendationAreaErrorCode)
				}
				// enter recommended area
				req := &driver_provisionv1.EnterRecommendedAreaRequest{
					RecommendationId: req.H3Recommendation.RecommendationID,
					EnteredH3:        driverH3,
					Lat:              req.DriverLatitude,
					Lng:              req.DriverLongtitude,
				}
				if _, err := svc.client.EnterRecommendedArea(ctx, req); err != nil {
					err := fmt.Errorf("error stamp enter recommended area event to driver-provision: %s", err.Error())
					return taskSuccessResult{}, sErr(err, callEnterRecommendationErrorCode)
				}
				return taskSuccessResult{
					action: enterRecommendationArea,
				}, nil
			}
			return taskSuccessResult{}, vErr(errors.New("no recommendation active"), noRecommendationActiveErrorCode)
		}

		cfg := svc.atomicSupplyPositionConfig.Get()
		if !req.H3Recommendation.ExpiredAt.IsZero() {
			cooldownPeriod := req.H3Recommendation.ExpiredAt.In(timeutil.BangkokLocation()).Add(cfg.SupplyPositioningExpireCooldownTime)
			if now.Before(cooldownPeriod) { // if still in cooldown period, system will block to create recommendation
				err := errors.New("driver is under cooldown period")
				return taskSuccessResult{}, vErr(err, underCooldownPeriodErrorCode)
			}
		}

		// calculate idleness
		idleness, err := svc.calculateIdleness(now, req.RecoIdleTimeStartPoint)
		if err != nil {
			if errors.Is(err, errRecoIdleTimeStartPointIsNil) {
				return taskSuccessResult{}, vErr(err, recoIdleTimeStartPointNilErrorCode)
			}
			err := fmt.Errorf("calculate idleness err: %v", err)
			return taskSuccessResult{}, sErr(err, calculateIdlenessErrorCode)
		}

		threshold := cfg.SupplyPositioningIdleThreshold
		if idleness < threshold {
			err := fmt.Errorf("calculate idleness err: %v", err)
			return taskSuccessResult{}, vErr(err, idlenessNotExceedThresholdErrorCode)
		}

		// create recommendation
		createReq := &driver_provisionv1.CreateRecommendationRequest{
			DriverId: req.DriverID,
			Lat:      req.DriverLatitude,
			Lng:      req.DriverLongtitude,
		}
		resp, err := svc.client.CreateRecommendation(ctx, createReq)
		if err != nil {
			err := fmt.Errorf("create recommendation err: %v", err)
			return taskSuccessResult{}, sErr(err, callEnterRecommendationErrorCode)
		}

		if resp.Id == req.H3Recommendation.RecommendationID {
			err := fmt.Errorf("duplicate recommendation create request: %v", resp.Id)
			return taskSuccessResult{}, vErr(err, duplicateCreateRecommendationRequestErrorCode)
		}

		if len(resp.Areas) == 0 {
			if err := svc.driverRepo.SetRecoIdleTimeStartPoint(ctx, req.DriverID, timeutil.BangkokNow()); err != nil {
				err := fmt.Errorf("set reco idle time start point when areas empty err : %v", err)
				return taskSuccessResult{}, sErr(err, dbSetRecoIdleTimeStartPointErrorCode)
			}
			return taskSuccessResult{}, vErr(err, emptyH3SuggestionErrorCode)
		}

		expiredAt, err := time.Parse(time.RFC3339, resp.ExpiredAt)
		if err != nil {
			err := fmt.Errorf("parse expire at err: %v", err)
			return taskSuccessResult{}, sErr(err, parseExpireAtErrorCode)
		}

		areas := make([]model.RecommendedH3, len(resp.Areas))
		h3AreasIndex := make(map[string]model.RecommendedH3)
		for i, area := range resp.Areas {
			recommendedH3 := model.RecommendedH3{
				H3ID:        area.H3Id,
				Name:        area.Name,
				FullAddress: area.FullAddress,
				WaitingTime: area.WaitingTime,
				PDSScore:    area.PdsScore,
				Lat:         area.Lat,
				Lng:         area.Lng,
			}
			areas[i] = recommendedH3
			h3AreasIndex[recommendedH3.H3ID] = recommendedH3
		}

		rec := &model.H3Recommendation{
			RecommendationID: resp.Id,
			Areas:            areas,
			H3AreasIndex:     h3AreasIndex,
			ExpiredAt:        expiredAt,
		}
		if err := svc.driverRepo.SetDriverH3Recommendation(ctx, req.DriverID, rec); err != nil {
			err := fmt.Errorf("set driver h3 recommendation err: %v", err)
			return taskSuccessResult{}, sErr(err, dbSetH3RecommendationErrorCode)
		}

		t1, err := asynqtask.NewExpireRecommendationTask(req.DriverID)
		if err != nil {
			err := fmt.Errorf("new expire recommendation task err: %v", err)
			return taskSuccessResult{}, sErr(err, newExpireTaskErrorCode)
		}
		if now.Sub(expiredAt) > 0 {
			err := fmt.Errorf("now: %v is more than expire at: %v, please check expired at", now, expiredAt)
			return taskSuccessResult{}, sErr(err, alreadyExpiredErrorCode)
		}

		task, err := svc.asynqClient.Enqueue(t1, asynq.ProcessAt(expiredAt), asynq.Retention(svc.atomicSupplyPositionConfig.Config.SupplyPositioningExpireRecoRetention))
		if err != nil {
			err := fmt.Errorf("enqueue expire recommendation task err: %v", err)
			return taskSuccessResult{}, sErr(err, enqueueTaskErrorCode)
		}

		// notify to client
		notifyMsg := EventRecommendedArea(task.ID)
		if err := svc.notifier.Notify(ctx, []string{req.DriverID}, notifyMsg); err != nil {
			err := fmt.Errorf("notify err: %v", err)
			return taskSuccessResult{}, sErr(err, callNotifyErrorCode)
		}

		return taskSuccessResult{
			action: createRecommendation,
		}, nil
	}
}

func (svc *supplyPositioningRecommenderServiceImpl) metric(req model.SupplyPositioningRecommenderServiceReq, fn func(ctx context.Context) (taskSuccessResult, error)) func(ctx context.Context) {
	return func(ctx context.Context) {
		svc.serviceMetric.recommendationProcessedTotal.Inc()
		now := time.Now()
		logCtx := logrus.Fields{
			"driver_id":                         req.DriverID,
			"driver_region":                     req.DriverRegion,
			"driver_status":                     req.DriverStatus,
			"driver_latitude":                   req.DriverLatitude,
			"driver_longtitude":                 req.DriverLongtitude,
			"driver_reco_idle_time_start_point": req.RecoIdleTimeStartPoint,
		}
		logrus := logrus.WithFields(logCtx)
		result, err := fn(ctx)
		if err != nil {
			var vErr *valdiationErr
			var sErr *systemErr
			if errors.As(err, &vErr) {
				svc.serviceMetric.recommendationFailedTotal.Inc("validation", fmt.Sprint(vErr.code))
			} else if errors.As(err, &sErr) {
				logrus.Error(formatLog(err.Error()))
				svc.serviceMetric.recommendationFailedTotal.Inc("system", fmt.Sprint(sErr.code))
			} else {
				logrus.Error(formatLog(err.Error()))
				svc.serviceMetric.recommendationFailedTotal.Inc("unknown")
			}
		} else {
			svc.serviceMetric.recommendationSucceededTotal.Inc(result.action.String())
		}
		elasped := time.Since(now)
		svc.serviceMetric.workerProcessingTimeMs.Observe(float64(elasped.Milliseconds()))
	}
}

// Collect implements prometheus.Collector.
func (svc *supplyPositioningRecommenderServiceImpl) Collect(ch chan<- prometheus.Metric) {
	free, running, cap := float64(svc.wk.Free()), float64(svc.wk.Running()), float64(svc.wk.Cap())
	utilization := (running / cap) * 100
	ch <- prometheus.MustNewConstMetric(
		svc.serviceMetric.workerPoolFree,
		prometheus.GaugeValue,
		free,
	)

	ch <- prometheus.MustNewConstMetric(
		svc.serviceMetric.workerPoolRunning,
		prometheus.GaugeValue,
		running,
	)

	ch <- prometheus.MustNewConstMetric(
		svc.serviceMetric.workerPoolCap,
		prometheus.GaugeValue,
		cap,
	)

	ch <- prometheus.MustNewConstMetric(
		svc.serviceMetric.workerPoolUtilization,
		prometheus.GaugeValue,
		utilization,
	)
}

// Describe implements prometheus.Collector.
func (svc *supplyPositioningRecommenderServiceImpl) Describe(ch chan<- *prometheus.Desc) {
	prometheus.DescribeByCollect(svc, ch)
}

func (svc *supplyPositioningRecommenderServiceImpl) calculateIdleness(now time.Time, t *time.Time) (time.Duration, error) {
	if t == nil {
		return 0, errRecoIdleTimeStartPointIsNil
	}

	idleness := now.Sub(*t)

	if idleness < 0 {
		return 0, errors.New("idleness cannot be negative")
	}

	return idleness, nil
}

func formatLog(msg string) string {
	return fmt.Sprintf("[%s]: %v", "supply_positioning_recommender_service", msg)
}
