// Code generated by MockGen. DO NOT EDIT.
// Source: ./translation_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"

	commonv1 "git.wndv.co/go/proto/lineman/shared/common/v1"
	gomock "github.com/golang/mock/gomock"
)

// MockTranslationService is a mock of TranslationService interface.
type MockTranslationService struct {
	ctrl     *gomock.Controller
	recorder *MockTranslationServiceMockRecorder
}

// MockTranslationServiceMockRecorder is the mock recorder for MockTranslationService.
type MockTranslationServiceMockRecorder struct {
	mock *MockTranslationService
}

// NewMockTranslationService creates a new mock instance.
func NewMockTranslationService(ctrl *gomock.Controller) *MockTranslationService {
	mock := &MockTranslationService{ctrl: ctrl}
	mock.recorder = &MockTranslationServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTranslationService) EXPECT() *MockTranslationServiceMockRecorder {
	return m.recorder
}

// Translate mocks base method.
func (m *MockTranslationService) Translate(ctx context.Context, message string, source, target commonv1.TranslationLanguage) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Translate", ctx, message, source, target)
	ret0, _ := ret[0].(string)
	return ret0
}

// Translate indicates an expected call of Translate.
func (mr *MockTranslationServiceMockRecorder) Translate(ctx, message, source, target interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Translate", reflect.TypeOf((*MockTranslationService)(nil).Translate), ctx, message, source, target)
}

// TranslateENToTH mocks base method.
func (m *MockTranslationService) TranslateENToTH(ctx context.Context, message string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TranslateENToTH", ctx, message)
	ret0, _ := ret[0].(string)
	return ret0
}

// TranslateENToTH indicates an expected call of TranslateENToTH.
func (mr *MockTranslationServiceMockRecorder) TranslateENToTH(ctx, message interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TranslateENToTH", reflect.TypeOf((*MockTranslationService)(nil).TranslateENToTH), ctx, message)
}
