// Code generated by MockGen. DO NOT EDIT.
// Source: ./area_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	service "git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	gomock "github.com/golang/mock/gomock"
)

// MockAreaService is a mock of AreaService interface.
type MockAreaService struct {
	ctrl     *gomock.Controller
	recorder *MockAreaServiceMockRecorder
}

// MockAreaServiceMockRecorder is the mock recorder for MockAreaService.
type MockAreaServiceMockRecorder struct {
	mock *MockAreaService
}

// NewMockAreaService creates a new mock instance.
func NewMockAreaService(ctrl *gomock.Controller) *MockAreaService {
	mock := &MockAreaService{ctrl: ctrl}
	mock.recorder = &MockAreaServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAreaService) EXPECT() *MockAreaServiceMockRecorder {
	return m.recorder
}

// GetDistributeRegion mocks base method.
func (m *MockAreaService) GetDistributeRegion(ctx context.Context, serviceType model.Service, area string, loc service.Location) ([]model.RegionCode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDistributeRegion", ctx, serviceType, area, loc)
	ret0, _ := ret[0].([]model.RegionCode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDistributeRegion indicates an expected call of GetDistributeRegion.
func (mr *MockAreaServiceMockRecorder) GetDistributeRegion(ctx, serviceType, area, loc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDistributeRegion", reflect.TypeOf((*MockAreaService)(nil).GetDistributeRegion), ctx, serviceType, area, loc)
}

// GetEffectiveServiceTypes mocks base method.
func (m *MockAreaService) GetEffectiveServiceTypes(ctx context.Context, serviceArea model.ServiceArea, driverServiceTypes, optedOutServices []model.Service, driverID string) (model.EffectiveServiceTypes, bool, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEffectiveServiceTypes", ctx, serviceArea, driverServiceTypes, optedOutServices, driverID)
	ret0, _ := ret[0].(model.EffectiveServiceTypes)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetEffectiveServiceTypes indicates an expected call of GetEffectiveServiceTypes.
func (mr *MockAreaServiceMockRecorder) GetEffectiveServiceTypes(ctx, serviceArea, driverServiceTypes, optedOutServices, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEffectiveServiceTypes", reflect.TypeOf((*MockAreaService)(nil).GetEffectiveServiceTypes), ctx, serviceArea, driverServiceTypes, optedOutServices, driverID)
}
