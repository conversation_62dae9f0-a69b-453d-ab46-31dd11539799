// Code generated by MockGen. DO NOT EDIT.
// Source: ./mission_log_event_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockMissionLogEventService is a mock of MissionLogEventService interface.
type MockMissionLogEventService struct {
	ctrl     *gomock.Controller
	recorder *MockMissionLogEventServiceMockRecorder
}

// MockMissionLogEventServiceMockRecorder is the mock recorder for MockMissionLogEventService.
type MockMissionLogEventServiceMockRecorder struct {
	mock *MockMissionLogEventService
}

// NewMockMissionLogEventService creates a new mock instance.
func NewMockMissionLogEventService(ctrl *gomock.Controller) *MockMissionLogEventService {
	mock := &MockMissionLogEventService{ctrl: ctrl}
	mock.recorder = &MockMissionLogEventServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMissionLogEventService) EXPECT() *MockMissionLogEventServiceMockRecorder {
	return m.recorder
}

// PublishMissionLogOrderEvent mocks base method.
func (m *MockMissionLogEventService) PublishMissionLogOrderEvent(ctx context.Context, order *model.Order) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PublishMissionLogOrderEvent", ctx, order)
}

// PublishMissionLogOrderEvent indicates an expected call of PublishMissionLogOrderEvent.
func (mr *MockMissionLogEventServiceMockRecorder) PublishMissionLogOrderEvent(ctx, order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishMissionLogOrderEvent", reflect.TypeOf((*MockMissionLogEventService)(nil).PublishMissionLogOrderEvent), ctx, order)
}

// PublishMissionLogTripEvent mocks base method.
func (m *MockMissionLogEventService) PublishMissionLogTripEvent(ctx context.Context, trip *model.Trip) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PublishMissionLogTripEvent", ctx, trip)
}

// PublishMissionLogTripEvent indicates an expected call of PublishMissionLogTripEvent.
func (mr *MockMissionLogEventServiceMockRecorder) PublishMissionLogTripEvent(ctx, trip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishMissionLogTripEvent", reflect.TypeOf((*MockMissionLogEventService)(nil).PublishMissionLogTripEvent), ctx, trip)
}
