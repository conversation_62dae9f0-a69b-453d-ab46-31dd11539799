// Code generated by MockGen. DO NOT EDIT.
// Source: ./delivery_fee_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockDeliveryFeeService is a mock of DeliveryFeeService interface.
type MockDeliveryFeeService struct {
	ctrl     *gomock.Controller
	recorder *MockDeliveryFeeServiceMockRecorder
}

// MockDeliveryFeeServiceMockRecorder is the mock recorder for MockDeliveryFeeService.
type MockDeliveryFeeServiceMockRecorder struct {
	mock *MockDeliveryFeeService
}

// NewMockDeliveryFeeService creates a new mock instance.
func NewMockDeliveryFeeService(ctrl *gomock.Controller) *MockDeliveryFeeService {
	mock := &MockDeliveryFeeService{ctrl: ctrl}
	mock.recorder = &MockDeliveryFeeServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeliveryFeeService) EXPECT() *MockDeliveryFeeServiceMockRecorder {
	return m.recorder
}

// GetDeliveryFeeCalculator mocks base method.
func (m *MockDeliveryFeeService) GetDeliveryFeeCalculator(ctx context.Context, serviceType model.Service, region string, loc model.Location, key model.DeliveryFeeSettingSchemeKey) (model.DeliveryFeeCalculator, model.SettingDeliveryFeePriceScheme, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeliveryFeeCalculator", ctx, serviceType, region, loc, key)
	ret0, _ := ret[0].(model.DeliveryFeeCalculator)
	ret1, _ := ret[1].(model.SettingDeliveryFeePriceScheme)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetDeliveryFeeCalculator indicates an expected call of GetDeliveryFeeCalculator.
func (mr *MockDeliveryFeeServiceMockRecorder) GetDeliveryFeeCalculator(ctx, serviceType, region, loc, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeliveryFeeCalculator", reflect.TypeOf((*MockDeliveryFeeService)(nil).GetDeliveryFeeCalculator), ctx, serviceType, region, loc, key)
}

// GetZoneDeliveryFee mocks base method.
func (m *MockDeliveryFeeService) GetZoneDeliveryFee(ctx context.Context, region string) (model.RegionZoneDeliveryFee, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetZoneDeliveryFee", ctx, region)
	ret0, _ := ret[0].(model.RegionZoneDeliveryFee)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetZoneDeliveryFee indicates an expected call of GetZoneDeliveryFee.
func (mr *MockDeliveryFeeServiceMockRecorder) GetZoneDeliveryFee(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetZoneDeliveryFee", reflect.TypeOf((*MockDeliveryFeeService)(nil).GetZoneDeliveryFee), ctx, region)
}
