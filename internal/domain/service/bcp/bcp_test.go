package bcp_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	connector_bcp "git.wndv.co/lineman/fleet-distribution/internal/connector/bcp"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/bcp/mock_bcp"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/bcp"
)

func TestBCPTestSuite(t *testing.T) {
	suite.Run(t, new(BCPTestSuite))
}

type BCPTestSuite struct {
	suite.Suite
	ctx context.Context

	legacyCtrl                 *gomock.Controller
	mockBCPOrderRepository     *mock_repository.MockBCPOrderRepository
	mockMinimalOrderRepository *mock_repository.MockMinimalOrderRepository
	mockBCPClient              *mock_bcp.MockBCPClient

	underTest bcp.BCPStatusTransitionerService
}

func (s *BCPTestSuite) SetupSubTest() {
	s.legacyCtrl = gomock.NewController(s.T())
	s.ctx = context.Background()
	s.mockBCPOrderRepository = mock_repository.NewMockBCPOrderRepository(s.legacyCtrl)
	s.mockMinimalOrderRepository = mock_repository.NewMockMinimalOrderRepository(s.legacyCtrl)
	s.mockBCPClient = mock_bcp.NewMockBCPClient(s.legacyCtrl)

	s.underTest = bcp.ProvideBCPStatusTransitionerService(
		s.mockBCPOrderRepository,
		s.mockMinimalOrderRepository,
		s.mockBCPClient,
	)
}

func (s *BCPTestSuite) TearDownSubTest() {
	s.legacyCtrl.Finish()
}

func (s *BCPTestSuite) TestCompleteOrder() {
	driverID := "DRIVER001"
	tripID := "TRIP_ID_001"
	orderID := "ORDER_ID_001"

	s.Run("success", func() {
		order := model.BCPOrder{
			DriverID: driverID,
			TripID:   tripID,
			OrderID:  orderID,
			Status:   model.BCPOrderStatusCompleted,
		}

		s.mockBCPOrderRepository.EXPECT().Create(s.ctx, order).Return(nil)
		err := s.underTest.CompleteOrder(s.ctx, driverID, tripID, orderID)
		s.NoError(err)
	})
}

func (s *BCPTestSuite) TestIsOutSync() {
	driverID := "DRIVER001"
	s.Run("out-sync: order status not ended", func() {
		s.mockBCPOrderRepository.EXPECT().Find(gomock.Any(), gomock.Any()).Return(
			[]model.BCPOrder{
				{
					OrderID: "mock",
					Status:  model.BCPOrderStatusCompleted,
				},
			}, nil)
		// querying, ended orders
		s.mockMinimalOrderRepository.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.Order{}, nil)
		ret := s.underTest.IsOutSync(s.ctx, driverID)
		assert.Equal(s.T(), true, ret)
	})

	s.Run("out-sync: can't query bcp order", func() {
		s.mockBCPOrderRepository.EXPECT().Find(gomock.Any(), gomock.Any()).Return(
			[]model.BCPOrder{}, errors.New("err"))
		ret := s.underTest.IsOutSync(s.ctx, driverID)
		assert.Equal(s.T(), true, ret)
	})

	s.Run("in-sync: bcp order empty", func() {
		s.mockBCPOrderRepository.EXPECT().Find(gomock.Any(), gomock.Any()).Return(
			[]model.BCPOrder{}, nil)
		ret := s.underTest.IsOutSync(s.ctx, driverID)
		assert.Equal(s.T(), false, ret)
	})

	s.Run("in-sync: order status ended", func() {
		s.mockBCPOrderRepository.EXPECT().Find(gomock.Any(), gomock.Any()).Return(
			[]model.BCPOrder{
				{
					OrderID: "mock",
					Status:  model.BCPOrderStatusCompleted,
				},
			}, nil)
		// querying, ended orders
		s.mockMinimalOrderRepository.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.Order{
				{
					OrderID: "mock",
					Status:  model.StatusCompleted,
				},
			}, nil)
		ret := s.underTest.IsOutSync(s.ctx, driverID)
		assert.Equal(s.T(), false, ret)
	})
}

func (s *BCPTestSuite) TestIsEnabled() {
	s.Run("fleet config: enabled true", func() {
		s.mockBCPClient.EXPECT().GetFleetConfig(gomock.Any()).Return(&connector_bcp.FleetConfigResponse{
			Enabled: true,
		}, nil)
		ret := s.underTest.IsEnabled(s.ctx)
		assert.Equal(s.T(), true, ret)
	})

	s.Run("fleet config: enabled false", func() {
		s.mockBCPClient.EXPECT().GetFleetConfig(gomock.Any()).Return(&connector_bcp.FleetConfigResponse{
			Enabled: false,
		}, nil)
		ret := s.underTest.IsEnabled(s.ctx)
		assert.Equal(s.T(), false, ret)
	})

	s.Run("fleet config: error", func() {
		s.mockBCPClient.EXPECT().GetFleetConfig(gomock.Any()).Return(
			&connector_bcp.FleetConfigResponse{},
			errors.New("err"),
		)
		ret := s.underTest.IsEnabled(s.ctx)

		assert.Equal(s.T(), false, ret)
	})
}
