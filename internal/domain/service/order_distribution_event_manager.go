package service

//go:generate mockgen -source=./order_distribution_event_manager.go -destination=./mock_service/order_distribution_event_manager.go -package=mock_service

import (
	"context"
	"fmt"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type OrderDistributionEventManager interface {
	PublishCreated(ctx context.Context, order model.Order, eventTime time.Time) error
	PublishSearchingOrAssigningDriver(ctx context.Context, order model.Order, eventTime time.Time) error
	PublishDriverMatched(ctx context.Context, order model.Order, updateActualAssigningAtOnAccepting bool) error
}

type OrderDistributionEventManagerImpl struct {
	service OrderDistributionEventService
}

func NewOrderDistributionEventManager(service OrderDistributionEventService) OrderDistributionEventManager {
	return OrderDistributionEventManagerImpl{
		service: service,
	}
}

func (m OrderDistributionEventManagerImpl) PublishCreated(ctx context.Context, order model.Order, eventTime time.Time) error {
	return m.service.Publish(ctx, model.FleetCreatedDistributionEvent{
		Order:     order,
		EventTime: eventTime,
	})
}

func (m OrderDistributionEventManagerImpl) PublishSearchingOrAssigningDriver(ctx context.Context, order model.Order, eventTime time.Time) error {
	if order.ActualAssigningAt == nil {
		return m.service.Publish(ctx, model.SearchingDriverDistributionEvent{
			Order:     order,
			EventTime: eventTime,
		})
	}
	return m.service.Publish(ctx, model.AssigningDriverDistributionEvent{
		Order:     order,
		EventTime: eventTime,
	})
}

func (m OrderDistributionEventManagerImpl) PublishDriverMatched(ctx context.Context, order model.Order, updateActualAssigningAtOnAccepting bool) error {
	if order.ActualAssigningAt == nil || updateActualAssigningAtOnAccepting {
		if err := m.service.Publish(ctx, model.AssigningDriverDistributionEvent{
			Order:     order,
			EventTime: order.UpdatedAt,
		}); err != nil {
			return fmt.Errorf("cannot publish assigning driver: %w", err)
		}
	}
	if err := m.service.Publish(ctx, model.DriverMatchedDistributionEvent{
		Order:     order,
		DriverID:  order.Driver,
		EventTime: order.UpdatedAt,
	}); err != nil {
		return fmt.Errorf("cannot publish publish driver matched: %w", err)
	}
	return nil
}

func ProvideOrderDistributionEventManager(service OrderDistributionEventService) OrderDistributionEventManager {
	return NewOrderDistributionEventManager(service)
}
