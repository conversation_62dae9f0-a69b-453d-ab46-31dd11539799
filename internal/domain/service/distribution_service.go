package service

//go:generate mockgen -source=./distribution_service.go -destination=./mock_service/mock_distribution_service.go -package=mock_service

import (
	"context"

	"github.com/kelseyhightower/envconfig"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"

	kafcclientdistribution "git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/kafcdistribution"
	driverv1 "git.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1"
)

type DistributionService interface {
	PublishDistributeOrderEvent(ctx context.Context, orderID string) error
	PublishRedistributeOrderEvent(ctx context.Context, orderID string, ridersTriedAssigning *int) error
	DistributeOrder(ctx context.Context, event *driverv1.DistributeOrderEvent) error
}

type DistributionServiceConfig struct {
	DistributeOrderTopic string `envconfig:"DISTRIBUTE_ORDER_TOPIC" default:""`
}

func ProvideDistributionServiceConfig() (cfg DistributionServiceConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

func ProvideDistributionService(cfg DistributionServiceConfig, producer kafcclientdistribution.SecureIMFKafkaSyncProducer) DistributionService {
	return &DistributionServiceImpl{
		cfg:      cfg,
		producer: producer,
	}
}

var _ DistributionService = &DistributionServiceImpl{}

type DistributionServiceImpl struct {
	cfg      DistributionServiceConfig
	producer kafcclientdistribution.SecureIMFKafkaSyncProducer
}

func (ds *DistributionServiceImpl) PublishDistributeOrderEvent(ctx context.Context, orderID string) error {
	if orderID == "" {
		return errors.New("empty orderID")
	}

	event := &driverv1.DistributeOrderEvent{
		OrderId: orderID,
	}
	headers := map[string]string{}
	message, err := proto.Marshal(event)
	if err != nil {
		return err
	}

	_, _, err = ds.producer.SendMessage(ctx, ds.cfg.DistributeOrderTopic, orderID, message, headers)
	if err != nil {
		return err
	}
	return nil
}

func (ds *DistributionServiceImpl) PublishRedistributeOrderEvent(ctx context.Context, orderID string, ridersTriedAssigning *int) error {
	if orderID == "" {
		return errors.New("empty orderID")
	}

	var ridersTriedAssigningInt32 *int32 = nil
	if ridersTriedAssigning != nil {
		v := int32(*ridersTriedAssigning)
		ridersTriedAssigningInt32 = &v
	}

	event := &driverv1.DistributeOrderEvent{
		OrderId:              orderID,
		RidersTriedAssigning: ridersTriedAssigningInt32,
	}
	headers := map[string]string{}
	message, err := proto.Marshal(event)
	if err != nil {
		return err
	}

	_, _, err = ds.producer.SendMessage(ctx, ds.cfg.DistributeOrderTopic, orderID, message, headers)
	if err != nil {
		return err
	}
	return nil
}

func NewRedistributeOrderReq(orderID string, ridersTriedAssigning *int, isRedistributionRequired bool) *driverv1.DistributeOrderEvent {
	var ridersTriedAssigningInt32 *int32 = nil
	if ridersTriedAssigning != nil {
		v := int32(*ridersTriedAssigning)
		ridersTriedAssigningInt32 = &v
	}

	return &driverv1.DistributeOrderEvent{
		OrderId:                  orderID,
		RidersTriedAssigning:     ridersTriedAssigningInt32,
		IsRedistributionRequired: isRedistributionRequired,
	}
}

func (ds *DistributionServiceImpl) DistributeOrder(ctx context.Context, payload *driverv1.DistributeOrderEvent) error {
	orderID := payload.OrderId
	if orderID == "" {
		return errors.New("empty orderID")
	}

	headers := map[string]string{}
	message, err := proto.Marshal(payload)
	if err != nil {
		return err
	}

	_, _, err = ds.producer.SendMessage(ctx, ds.cfg.DistributeOrderTopic, orderID, message, headers)
	if err != nil {
		return err
	}
	return nil
}
