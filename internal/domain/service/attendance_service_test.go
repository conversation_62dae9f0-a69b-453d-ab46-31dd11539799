package service

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func generateTime(hr int, min int) time.Time {
	return time.Date(2022, 1, 10, hr, min, 0, 0, timeutil.BangkokLocation())
}

func TestAttendance_GetAttendance(t *testing.T) {
	wednesday := time.Date(2022, 1, 26, 12, 30, 0, 0, timeutil.BangkokLocation())
	t.Run("get valid today attendance log", func(t *testing.T) {
		yesterday := wednesday.Add(time.Hour * (24 * -1))
		today := wednesday
		at := &Attendance{}
		logs := []model.AttendanceLog{
			{
				Status: model.AttendanceStatusOffline,
				Time:   yesterday,
			},
			{
				Status: model.AttendanceStatusOnline,
				Time:   today,
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   today.Add(time.Minute * 30),
			},
		}
		ls := at.getTodayAttendance(logs, today)
		require.Equal(t, 2, len(ls))
		require.Equal(t, 26, ls[0].Time.Day())
		require.Equal(t, 26, ls[1].Time.Day())
	})

	t.Run("get valid get past shift today", func(t *testing.T) {
		today := wednesday
		yesterday := wednesday.Add(time.Hour * (24 * -1))
		at := &Attendance{}
		shifts := []model.Shift{
			{
				Start: yesterday,
				End:   yesterday.Add(time.Hour),
			},
			{
				Start: today.Add(time.Hour * -4),
				End:   today.Add(time.Hour * -3),
			},
			{
				Start: today.Add(time.Hour * 2),
				End:   today.Add(time.Hour * 3),
			},
		}
		s := at.getPastShiftToday(shifts, today)
		require.Equal(t, 1, len(s))
		require.Equal(t, 26, s[0].Start.Day())
	})

	t.Run("get valid get past shift today (2)", func(t *testing.T) {
		current := generateTime(14, 0)
		at := &Attendance{}
		shifts := []model.Shift{
			{
				Start: generateTime(8, 0),
				End:   generateTime(8, 30),
			},
			{
				Start: generateTime(13, 0),
				End:   generateTime(15, 0),
			},
			{
				Start: generateTime(20, 0),
				End:   generateTime(21, 0),
			},
		}
		s := at.getPastShiftToday(shifts, current)
		require.Equal(t, 2, len(s))
		require.Equal(t, 8, s[0].Start.Hour())
		require.Equal(t, 13, s[1].Start.Hour())
	})

	t.Run("get valid weekly attendance stat", func(t *testing.T) {
		lastSunday := time.Date(2022, 1, 23, 12, 30, 0, 0, timeutil.BangkokLocation())
		monday := time.Date(2022, 1, 24, 12, 30, 0, 0, timeutil.BangkokLocation())
		today := wednesday
		att := model.AttendanceStat{
			timeutil.ToYYYYMMDD(lastSunday): []model.AttendanceTime{
				{
					ShiftId:   "s1",
					Actual:    5,
					ShiftTime: 5,
				},
			},
			timeutil.ToYYYYMMDD(monday): []model.AttendanceTime{
				{
					ShiftId:   "s2",
					Actual:    5,
					ShiftTime: 5,
				},
			},
			"2022-01-25": []model.AttendanceTime{
				{
					ShiftId:   "s3",
					Actual:    5,
					ShiftTime: 5,
				},
			},
			timeutil.ToYYYYMMDD(today): []model.AttendanceTime{
				{
					ShiftId:   "s4",
					Actual:    5,
					ShiftTime: 5,
				},
				{
					ShiftId:   "s5",
					Actual:    5,
					ShiftTime: 5,
				},
			},
		}

		ls := getWeeklyAttStat(att, wednesday, findAttStat)
		require.Equal(t, 4, len(ls))
		require.NotContains(t, ls, model.AttendanceTimeItem{
			ShiftId:   "s1",
			Actual:    5,
			ShiftTime: 5,
		})
	})

	t.Run("get valid monthly attendance stat", func(t *testing.T) {
		// last day of last month
		lastMonth := time.Date(2021, 12, 31, 12, 30, 0, 0, timeutil.BangkokLocation())
		// first day of this month
		firstDay := time.Date(2022, 1, 1, 12, 30, 0, 0, timeutil.BangkokLocation())
		today := wednesday
		att := model.AttendanceStat{
			timeutil.ToYYYYMMDD(lastMonth): []model.AttendanceTime{
				{
					ShiftId:   "s1",
					Actual:    5,
					ShiftTime: 5,
				},
			},
			timeutil.ToYYYYMMDD(firstDay): []model.AttendanceTime{
				{
					ShiftId:   "s2",
					Actual:    5,
					ShiftTime: 5,
				},
			},
			"2022-01-25": []model.AttendanceTime{
				{
					ShiftId:   "s3",
					Actual:    5,
					ShiftTime: 5,
				},
			},
			timeutil.ToYYYYMMDD(today): []model.AttendanceTime{
				{
					ShiftId:   "s4",
					Actual:    5,
					ShiftTime: 5,
				},
				{
					ShiftId:   "s5",
					Actual:    5,
					ShiftTime: 5,
				},
			},
		}

		ls := getMonthlyAttStat(att, wednesday, findAttStat)
		require.Equal(t, 4, len(ls))

		require.NotContains(t, ls, model.AttendanceTimeItem{
			ShiftId:   "s1",
			Actual:    5,
			ShiftTime: 5,
		})
	})
}

func TestAttendance_GetAttendanceRateByRange(t *testing.T) {
	t.Run("get rate correctly with today (with att logs)", func(t *testing.T) {
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()

		now := timeutil.BangkokNow()
		// set current time at 11am after shift end time
		timeutils.Now = func() time.Time {
			return time.Date(now.Year(), now.Month(), now.Day(), 11, 0, 0, 0, timeutil.BangkokLocation())
		}
		defer timeutils.Unfreeze()

		dayTruncate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, timeutil.BangkokLocation())
		dayCeil := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, timeutil.BangkokLocation())
		ctx := context.Background()

		shifts := []model.Shift{
			{
				Label: "a",
				Start: time.Date(now.Year(), now.Month(), now.Day(), 8, 0, 0, 0, timeutil.BangkokLocation()),
				End:   time.Date(now.Year(), now.Month(), now.Day(), 10, 0, 0, 0, timeutil.BangkokLocation()),
			},
		}

		logs := []model.AttendanceLog{
			{
				Status: model.AttendanceStatusOnline,
				Time:   time.Date(now.Year(), now.Month(), now.Day(), 9, 0, 0, 0, timeutil.BangkokLocation()),
			},
		}

		deps.shiftRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(shifts, 1, nil)
		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DriverAttendance{DriverStatus: model.StatusOnline, AttendanceLogs: logs}, nil)

		rate, total, er := att.GetAttendanceRateByRange(ctx, driverId, dayTruncate, dayCeil)

		require.Equal(t, nil, er)
		require.Equal(t, 50.0, rate)
		require.Equal(t, 60.0, total.TotalAttendingTime)
		require.Equal(t, 120.0, total.TotalBookingTime)
		require.Equal(t, 60.0, total.TotalExceedQuotaBreakTime)
	})

	t.Run("get rate correctly with today (no att logs but offline)", func(t *testing.T) {
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()

		now := timeutil.BangkokNow()
		// set current time at 11am after shift end time
		timeutils.Now = func() time.Time {
			return time.Date(now.Year(), now.Month(), now.Day(), 11, 0, 0, 0, timeutil.BangkokLocation())
		}
		defer timeutils.Unfreeze()

		dayTruncate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, timeutil.BangkokLocation())
		dayCeil := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, timeutil.BangkokLocation())
		ctx := context.Background()

		shifts := []model.Shift{
			{
				Label: "a",
				Start: time.Date(now.Year(), now.Month(), now.Day(), 8, 0, 0, 0, timeutil.BangkokLocation()),
				End:   time.Date(now.Year(), now.Month(), now.Day(), 10, 0, 0, 0, timeutil.BangkokLocation()),
			},
		}

		deps.shiftRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(shifts, 1, nil)
		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DriverAttendance{DriverStatus: model.StatusOffline}, nil)

		rate, total, er := att.GetAttendanceRateByRange(ctx, driverId, dayTruncate, dayCeil)

		require.Equal(t, nil, er)
		require.Equal(t, 0.0, rate)
		require.Equal(t, 0.0, total.TotalAttendingTime)
		require.Equal(t, 120.0, total.TotalBookingTime)
		require.Equal(t, 120.0, total.TotalExceedQuotaBreakTime)
	})

	t.Run("get rate correctly with today (no att logs but online)", func(t *testing.T) {
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()

		now := timeutil.BangkokNow()
		// set current time at 11am after shift end time
		timeutils.Now = func() time.Time {
			return time.Date(now.Year(), now.Month(), now.Day(), 11, 0, 0, 0, timeutil.BangkokLocation())
		}
		defer timeutils.Unfreeze()

		dayTruncate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, timeutil.BangkokLocation())
		dayCeil := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, timeutil.BangkokLocation())
		ctx := context.Background()

		shifts := []model.Shift{
			{
				Label: "a",
				Start: time.Date(now.Year(), now.Month(), now.Day(), 8, 0, 0, 0, timeutil.BangkokLocation()),
				End:   time.Date(now.Year(), now.Month(), now.Day(), 10, 0, 0, 0, timeutil.BangkokLocation()),
			},
		}

		deps.shiftRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(shifts, 1, nil)
		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DriverAttendance{DriverStatus: model.StatusOnline}, nil)

		rate, total, er := att.GetAttendanceRateByRange(ctx, driverId, dayTruncate, dayCeil)

		require.Equal(t, nil, er)
		require.Equal(t, 100.0, rate)
		require.Equal(t, 120.0, total.TotalAttendingTime)
		require.Equal(t, 120.0, total.TotalBookingTime)
		require.Equal(t, 0.0, total.TotalExceedQuotaBreakTime)
	})

	t.Run("get rate correctly with weekly", func(t *testing.T) {
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()

		weekStart := time.Date(222, 3, 14, 0, 0, 0, 0, timeutil.BangkokLocation())
		weekEnd := time.Date(2022, 3, 20, 0, 0, 0, 0, timeutil.BangkokLocation())
		ctx := context.Background()

		doi := model.DriverOrderInfo{}

		atds := make(model.AttendanceStat)
		atds[weekEnd.Format(time.DateOnly)] = []model.AttendanceTime{
			{
				Actual:    50,
				ShiftTime: 100,
			},
		}
		doi.AttendanceStat = atds

		deps.driverOrderInfoRepo.EXPECT().FindDriverAttendanceStat(gomock.Any(), gomock.Any(), gomock.Any()).Return(doi, nil)

		rate, total, er := att.GetAttendanceRateByRange(ctx, driverId, weekStart, weekEnd)

		require.Equal(t, nil, er)
		require.Equal(t, 50.0, rate)
		require.Equal(t, 50.0, total.TotalAttendingTime)
		require.Equal(t, 100.0, total.TotalBookingTime)
		require.Equal(t, 50.0, total.TotalExceedQuotaBreakTime)
	})

	t.Run("get rate correctly with weekly (no shift)", func(t *testing.T) {
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()

		weekStart := time.Date(222, 3, 14, 0, 0, 0, 0, timeutil.BangkokLocation())
		weekEnd := time.Date(2022, 3, 20, 0, 0, 0, 0, timeutil.BangkokLocation())
		ctx := context.Background()

		doi := model.DriverOrderInfo{}

		atds := make(model.AttendanceStat)
		atds[weekEnd.Add(-24*3*time.Hour).Format(time.DateOnly)] = []model.AttendanceTime{
			{
				Actual:    50,
				ShiftTime: 100,
			},
		}
		doi.AttendanceStat = atds

		deps.shiftRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.Shift{}, 0, nil)
		deps.driverOrderInfoRepo.EXPECT().FindDriverAttendanceStat(gomock.Any(), gomock.Any(), gomock.Any()).Return(doi, nil)

		rate, total, er := att.GetAttendanceRateByRange(ctx, driverId, weekStart, weekEnd)

		require.Equal(t, nil, er)
		require.Equal(t, 50.0, rate)
		require.Equal(t, 50.0, total.TotalAttendingTime)
		require.Equal(t, 100.0, total.TotalBookingTime)
		require.Equal(t, 50.0, total.TotalExceedQuotaBreakTime)
	})

	t.Run("get rate correctly with weekly when driver browsing from the 2nd day of the week", func(t *testing.T) {
		// LMF-4712 This was an issue on Production.
		// So, to be able to reproduce and solve issue on production, this test data will based on Production data.

		// Given
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()
		ctx := context.Background()

		// Freeze time for reproduce issue which happened only at the 2nd week day or Tuesday.
		timeutils.Now = func() time.Time {
			return time.Date(2022, 4, 26, 13, 0, 0, 0, timeutil.BangkokLocation())
		}
		defer timeutils.Unfreeze()

		ObjectId := func(s string) primitive.ObjectID {
			id, _ := primitive.ObjectIDFromHex(s)
			return id
		}

		ISODate := func(s string) time.Time {
			t, _ := time.Parse(time.RFC3339, s)
			return t.In(timeutil.BangkokLocation())
		}

		doi := model.DriverOrderInfo{
			AttendanceStat: model.AttendanceStat{
				"2022-04-22": []model.AttendanceTime{
					{
						ShiftId:   "6258ea553e69894d407618b0",
						Actual:    239.983333333333,
						ShiftTime: 239.983333333333,
					},
				},
				"2022-04-25": []model.AttendanceTime{
					{
						ShiftId:   "6262378116b7427ae39b3967",
						Actual:    238.983333333333,
						ShiftTime: 239.983333333333,
					},
				},
			},
		}

		deps.driverOrderInfoRepo.EXPECT().FindDriverAttendanceStat(gomock.Any(), gomock.Any(), gomock.Any()).Return(doi, nil)
		deps.shiftRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, query repository.ShiftQuery, skip, limit int, opts ...repository.Option) ([]model.Shift, int, error) {
				db := []model.Shift{
					{
						ID:     ObjectId("6262378116b7427ae39b3967"),
						Active: true,
						Start:  ISODate("2022-04-25T04:00:00.000Z"),
						End:    ISODate("2022-04-25T07:59:59.000Z"),
					},
					{
						ID:     ObjectId("626237ab5aefc3649ca991f0"),
						Active: true,
						Start:  ISODate("2022-04-26T04:00:00.000Z"),
						End:    ISODate("2022-04-26T07:59:59.000Z"),
					},
					{
						ID:     ObjectId("62623887cec8a2dc3a5cb2f2"),
						Active: true,
						Start:  ISODate("2022-04-27T03:00:00.000Z"),
						End:    ISODate("2022-04-27T06:59:59.000Z"),
					},
				}

				var filtered []model.Shift
				for _, d := range db {
					if timeutil.IsBetweenEqual(d.Start, query.StartDate, query.EndDate) && timeutil.IsBetweenEqual(d.End, query.StartDate, query.EndDate) {
						filtered = append(filtered, d)
					}
				}

				return filtered, len(filtered), nil
			},
		)

		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DriverAttendance{
			AttendanceLogs: []model.AttendanceLog{
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T02:01:35.238Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T02:27:07.812Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T02:39:16.641Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T02:51:49.181Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T03:13:56.037Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T03:48:59.470Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T04:15:45.377Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T04:33:38.788Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T04:50:49.232Z"),
				},
				{
					Status: "OFFLINE",
					Time:   ISODate("2022-04-26T04:52:42.611Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T04:53:04.905Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T05:07:15.237Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T05:19:07.798Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T05:31:11.186Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T05:57:56.115Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T06:21:36.316Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T06:52:51.216Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-26T07:57:49.923Z"),
				},
				{
					Status: "OFFLINE",
					Time:   ISODate("2022-04-26T08:02:13.114Z"),
				},
			},
			DriverStatus: model.StatusOnline,
		}, nil)

		// When
		start := time.Date(2022, 4, 25, 0, 0, 0, 0, timeutil.BangkokLocation())
		end := time.Date(2022, 5, 1, 0, 0, 0, 0, timeutil.BangkokLocation())
		rate, total, er := att.GetAttendanceRateByRange(ctx, driverId, start, end)

		// Then
		require.Equal(t, nil, er)
		require.Equal(t, 99.71, rate)                           // 478.595 (Total Attending Time) ÷ 479.96 (Total Booking Time)
		require.Equal(t, 478.6, total.TotalAttendingTime)       // 479.97 (Total Booking Time) - 1.37 (Total Break Time)
		require.Equal(t, 479.97, total.TotalBookingTime)        // 2022-04-25 (239.98 minutes) + 2022-04-26 (239.98 minutes)
		require.Equal(t, 1.37, total.TotalExceedQuotaBreakTime) // 2022-04-25 (1 Minute break) + 2022-04-26 (0.37 Minute break)
	})

	t.Run("get rate correctly with monthly when browsing from the 2nd day of the month", func(t *testing.T) {
		// LMF-4712 This was an issue on Production.
		// So, to be able to reproduce and solve issue on production, this test data will based on Production data.

		// Given
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()
		ctx := context.Background()

		// Freeze time for reproduce issue which happened only at the 2nd day of the month.
		timeutils.Now = func() time.Time {
			return time.Date(2022, 4, 2, 13, 0, 0, 0, timeutil.BangkokLocation())
		}
		defer timeutils.Unfreeze()

		ObjectId := func(s string) primitive.ObjectID {
			id, _ := primitive.ObjectIDFromHex(s)
			return id
		}

		ISODate := func(s string) time.Time {
			t, _ := time.Parse(time.RFC3339, s)
			return t.In(timeutil.BangkokLocation())
		}

		doi := model.DriverOrderInfo{
			AttendanceStat: model.AttendanceStat{
				"2022-03-31": []model.AttendanceTime{
					{
						ShiftId:   "6262378116b7427ae39b3967",
						Actual:    238.983333333333,
						ShiftTime: 239.983333333333,
					},
				},
				"2022-04-01": []model.AttendanceTime{
					{
						ShiftId:   "626237ab5aefc3649ca991f0",
						Actual:    238.983333333333,
						ShiftTime: 239.983333333333,
					},
				},
			},
		}

		deps.driverOrderInfoRepo.EXPECT().FindDriverAttendanceStat(gomock.Any(), gomock.Any(), gomock.Any()).Return(doi, nil)
		deps.shiftRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, query repository.ShiftQuery, skip, limit int, opts ...repository.Option) ([]model.Shift, int, error) {
				db := []model.Shift{
					{
						ID:     ObjectId("6262378116b7427ae39b3967"),
						Active: true,
						Start:  ISODate("2022-03-31T04:00:00.000Z"),
						End:    ISODate("2022-03-31T07:59:59.000Z"),
					},
					{
						ID:     ObjectId("626237ab5aefc3649ca991f0"),
						Active: true,
						Start:  ISODate("2022-04-01T04:00:00.000Z"),
						End:    ISODate("2022-04-01T07:59:59.000Z"),
					},
					{
						ID:     ObjectId("62623887cec8a2dc3a5cb2f2"),
						Active: true,
						Start:  ISODate("2022-04-02T03:00:00.000Z"),
						End:    ISODate("2022-04-02T06:59:59.000Z"),
					},
				}

				var filtered []model.Shift
				for _, d := range db {
					if timeutil.IsBetweenEqual(d.Start, query.StartDate, query.EndDate) && timeutil.IsBetweenEqual(d.End, query.StartDate, query.EndDate) {
						filtered = append(filtered, d)
					}
				}

				return filtered, len(filtered), nil
			},
		)

		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DriverAttendance{
			AttendanceLogs: []model.AttendanceLog{
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T02:01:35.238Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T02:27:07.812Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T02:39:16.641Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T02:51:49.181Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T03:13:56.037Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T03:48:59.470Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T04:15:45.377Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T04:33:38.788Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T04:50:49.232Z"),
				},
				{
					Status: "OFFLINE",
					Time:   ISODate("2022-04-02T04:52:42.611Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T04:53:04.905Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T05:07:15.237Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T05:19:07.798Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T05:31:11.186Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T05:57:56.115Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T06:21:36.316Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T06:52:51.216Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-02T07:57:49.923Z"),
				},
				{
					Status: "OFFLINE",
					Time:   ISODate("2022-04-02T08:02:13.114Z"),
				},
			},
			DriverStatus: model.StatusOnline,
		}, nil)

		// When
		start := time.Date(2022, 4, 1, 0, 0, 0, 0, timeutil.BangkokLocation())
		end := time.Date(2022, 4, 30, 0, 0, 0, 0, timeutil.BangkokLocation())
		rate, total, er := att.GetAttendanceRateByRange(ctx, driverId, start, end)

		// Then
		require.Equal(t, nil, er)
		require.Equal(t, 99.71, rate)                           // 477 (Total Attending Time) ÷ 479 (Total Booking Time)
		require.Equal(t, 478.6, total.TotalAttendingTime)       // 479 (Total Booking Time) - 2 (Total Break Time)
		require.Equal(t, 479.97, total.TotalBookingTime)        // 2022-04-01 (239.98 minutes) + 2022-04-02 (239.98 minutes)
		require.Equal(t, 1.37, total.TotalExceedQuotaBreakTime) // 2022-04-25 (1 Minute break) + 2022-04-26 (0.37 Minute)
	})

	t.Run("get rate correctly with weekly when driver has shift on monday and wednesday", func(t *testing.T) {
		// LMF-4712 This was an issue on Production.
		// So, to be able to reproduce and solve issue on production, this test data will based on Production data.

		// Given
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()
		ctx := context.Background()

		// Freeze time for reproduce issue
		timeutils.Now = func() time.Time {
			return time.Date(2022, 4, 27, 22, 0, 0, 0, timeutil.BangkokLocation())
		}
		defer timeutils.Unfreeze()

		ObjectId := func(s string) primitive.ObjectID {
			id, _ := primitive.ObjectIDFromHex(s)
			return id
		}

		ISODate := func(s string) time.Time {
			t, _ := time.Parse(time.RFC3339, s)
			return t.In(timeutil.BangkokLocation())
		}

		doi := model.DriverOrderInfo{
			AttendanceStat: model.AttendanceStat{
				"2022-04-25": []model.AttendanceTime{
					{
						ShiftId:   "6262378116b7427ae39b3967",
						Actual:    238.983333333333,
						ShiftTime: 239.983333333333,
					},
				},
			},
		}

		deps.driverOrderInfoRepo.EXPECT().FindDriverAttendanceStat(gomock.Any(), gomock.Any(), gomock.Any()).Return(doi, nil)
		deps.shiftRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, query repository.ShiftQuery, skip, limit int, opts ...repository.Option) ([]model.Shift, int, error) {
				db := []model.Shift{
					{
						ID:     ObjectId("62618af3ede94039254f311e"),
						Active: true,
						Start:  ISODate("2022-04-27T11:00:00.000Z"),
						End:    ISODate("2022-04-27T14:59:59.000Z"),
					},
					{
						ID:     ObjectId("62618b8dd5650c7593b9f8de"),
						Active: true,
						Start:  ISODate("2022-04-25T02:00:00.000Z"),
						End:    ISODate("2022-04-25T05:59:59.000Z"),
					},
				}

				var filtered []model.Shift
				for _, d := range db {
					if timeutil.IsBetweenEqual(d.Start, query.StartDate, query.EndDate) && timeutil.IsBetweenEqual(d.End, query.StartDate, query.EndDate) {
						filtered = append(filtered, d)
					}
				}

				return filtered, len(filtered), nil
			},
		)

		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DriverAttendance{
			AttendanceLogs: []model.AttendanceLog{
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-27T08:22:59.844Z"),
				},
				{
					Status: "OFFLINE",
					Time:   ISODate("2022-04-27T08:23:22.172Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-27T11:01:45.056Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-27T11:25:07.327Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-27T11:50:38.514Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-27T12:03:15.398Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-27T12:31:03.026Z"),
				},
				{
					Status: "OFFLINE",
					Time:   ISODate("2022-04-27T12:43:47.635Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-27T12:45:20.961Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-27T13:21:08.719Z"),
				},
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-27T13:36:37.882Z"),
				},
			},
			DriverStatus: model.StatusOnline,
		}, nil)

		// When
		start := time.Date(2022, 4, 25, 0, 0, 0, 0, timeutil.BangkokLocation())
		end := time.Date(2022, 5, 1, 0, 0, 0, 0, timeutil.BangkokLocation())
		rate, total, er := att.GetAttendanceRateByRange(ctx, driverId, start, end)

		// Then
		require.Equal(t, nil, er)
		require.Equal(t, 99.1, rate)                            // 474 (Total Attending Time) ÷ 479 (Total Booking Time)
		require.Equal(t, 475.66, total.TotalAttendingTime)      // 479 (Total Booking Time) - 5 (Total Break Time)
		require.Equal(t, 479.97, total.TotalBookingTime)        // 2022-04-25 (239.98 minutes) + 2022-04-27 (239.98 minutes)
		require.Equal(t, 4.31, total.TotalExceedQuotaBreakTime) // 2022-04-25 (1 Minute break) + 2022-04-27 (3.3 Minute break)
	})

	t.Run("get rate correctly with monthly after cutoff time 21-04-2022", func(t *testing.T) {
		// Given
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()
		ctx := context.Background()

		timeutils.Now = func() time.Time {
			return time.Date(2022, 4, 25, 13, 0, 0, 0, timeutil.BangkokLocation())
		}
		defer timeutils.Unfreeze()

		ObjectId := func(s string) primitive.ObjectID {
			id, _ := primitive.ObjectIDFromHex(s)
			return id
		}

		ISODate := func(s string) time.Time {
			t, _ := time.Parse(time.RFC3339, s)
			return t.In(timeutil.BangkokLocation())
		}

		doi := model.DriverOrderInfo{
			AttendanceStat: model.AttendanceStat{},
		}

		deps.driverOrderInfoRepo.EXPECT().FindDriverAttendanceStat(gomock.Any(), gomock.Any(), gomock.Any()).Return(doi, nil)
		deps.shiftRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, query repository.ShiftQuery, skip, limit int, opts ...repository.Option) ([]model.Shift, int, error) {
				db := []model.Shift{
					{
						ID:     ObjectId("6262378116b7427ae39b3961"),
						Active: true,
						Start:  ISODate("2022-04-19T04:00:00.000Z"),
						End:    ISODate("2022-04-19T07:59:59.000Z"),
					},
					{
						ID:     ObjectId("6262378116b7427ae39b3962"),
						Active: true,
						Start:  ISODate("2022-04-20T04:00:00.000Z"),
						End:    ISODate("2022-04-20T07:59:59.000Z"),
					},
					{
						ID:     ObjectId("6262378116b7427ae39b3963"),
						Active: true,
						Start:  ISODate("2022-04-25T04:00:00.000Z"),
						End:    ISODate("2022-04-25T07:59:59.000Z"),
					},
					{
						ID:     ObjectId("626237ab5aefc3649ca991f4"),
						Active: true,
						Start:  ISODate("2022-04-26T04:00:00.000Z"),
						End:    ISODate("2022-04-26T07:59:59.000Z"),
					},
					{
						ID:     ObjectId("62623887cec8a2dc3a5cb2f5"),
						Active: true,
						Start:  ISODate("2022-04-27T03:00:00.000Z"),
						End:    ISODate("2022-04-27T06:59:59.000Z"),
					},
				}

				var filtered []model.Shift
				for _, d := range db {
					if timeutil.IsBetweenEqual(d.Start, query.StartDate, query.EndDate) && timeutil.IsBetweenEqual(d.End, query.StartDate, query.EndDate) {
						filtered = append(filtered, d)
					}
				}

				return filtered, len(filtered), nil
			},
		)

		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DriverAttendance{
			AttendanceLogs: []model.AttendanceLog{
				{
					Status: "ONLINE",
					Time:   ISODate("2022-04-25T02:01:35.238Z"),
				},
			},
			DriverStatus: model.StatusOnline,
		}, nil)

		// When
		start := time.Date(2022, 4, 1, 0, 0, 0, 0, timeutil.BangkokLocation())
		end := time.Date(2022, 4, 30, 0, 0, 0, 0, timeutil.BangkokLocation())
		rate, total, er := att.GetAttendanceRateByRange(ctx, driverId, start, end)

		// Then
		require.Equal(t, nil, er)
		require.Equal(t, float64(100), rate)
		require.Equal(t, 239.98, total.TotalAttendingTime) // from 2022-04-25
		require.Equal(t, 239.98, total.TotalBookingTime)   // from 2022-04-25
		require.Equal(t, 0.0, total.TotalExceedQuotaBreakTime)
	})
}

func getTime(hr int, min int, sec int) time.Time {
	return time.Date(2021, 5, 27, hr, min, sec, 0, timeutil.BangkokLocation())
}

func TestAttendance_GetCurrentShiftAttendance(t *testing.T) {
	t.Run("should return rate 100 when ONLINE with no logs", func(t *testing.T) {
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()
		tt := time.Date(2021, 1, 1, 8, 0, 0, 0, timeutil.BangkokLocation())
		shifts := []model.Shift{
			{
				Label: "a",
				Start: tt.Add(time.Hour * -1),
				End:   tt.Add(time.Hour),
			},
		}
		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.DriverAttendance{DriverStatus: model.StatusOnline, AttendanceLogs: []model.AttendanceLog{}}, nil).Times(2)

		attr, er := att.GetCurrentShiftAttendance(context.Background(), driverId, shifts, tt)
		require.Equal(t, nil, er)
		require.Equal(t, ShiftAttendanceRate{
			ShiftID:   "000000000000000000000000",
			Rate:      100,
			BreakTime: 120,
		}, attr)
	})

	t.Run("should return rate 0 when OFFLINE with no logs", func(t *testing.T) {
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()
		tt := time.Date(2021, 1, 1, 8, 0, 0, 0, timeutil.BangkokLocation())
		shifts := []model.Shift{
			{
				Label: "a",
				Start: tt.Add(time.Hour * -1),
				End:   tt.Add(time.Hour),
			},
		}
		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.DriverAttendance{DriverStatus: model.StatusOffline, AttendanceLogs: []model.AttendanceLog{}}, nil).Times(2)

		attr, er := att.GetCurrentShiftAttendance(context.Background(), driverId, shifts, tt)
		require.Equal(t, nil, er)
		require.Equal(t, ShiftAttendanceRate{
			ShiftID:   "000000000000000000000000",
			Rate:      0,
			BreakTime: 120,
		}, attr)
	})

	t.Run("should return break time", func(t *testing.T) {
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()
		tt := time.Date(2021, 1, 1, 8, 0, 0, 0, timeutil.BangkokLocation())
		shifts := []model.Shift{
			{
				Label: "a",
				Start: tt,
				End:   tt.Add(time.Hour),
			},
		}
		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.DriverAttendance{DriverStatus: model.StatusOnline, AttendanceLogs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   tt,
				},
			}}, nil).Times(2)

		timeutils.Now = func() time.Time {
			return tt
		}
		defer timeutils.Unfreeze()

		attr, er := att.GetCurrentShiftAttendance(context.Background(), driverId, shifts, tt)
		require.Equal(t, nil, er)
		require.Equal(t, ShiftAttendanceRate{
			ShiftID:   "000000000000000000000000",
			Rate:      100,
			BreakTime: 0,
		}, attr)
	})

	t.Run("should filter out logs that not on shift interval", func(t *testing.T) {
		driverId := "driver"
		att, deps, finish := newTestAttendance(t)
		defer finish()
		shifts := []model.Shift{
			{
				Label:      "a",
				Start:      time.Date(2021, 5, 27, 11, 1, 0, 0, timeutil.BangkokLocation()),
				End:        time.Date(2021, 5, 27, 12, 0, 0, 0, timeutil.BangkokLocation()),
				BreakQuota: 2,
			},
		}
		deps.driverRepo.EXPECT().GetAttendanceLog(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.DriverAttendance{
				DriverStatus: model.StatusOnline,
				AttendanceLogs: []model.AttendanceLog{
					{
						Status: model.AttendanceStatusOnline,
						Time:   time.Date(2021, 5, 27, 10, 42, 51, 0, timeutil.BangkokLocation()),
					},
					{
						Status: model.AttendanceStatusOffline,
						Time:   time.Date(2021, 5, 27, 10, 42, 54, 0, timeutil.BangkokLocation()),
					},
					{
						Status: model.AttendanceStatusOnline,
						Time:   time.Date(2021, 5, 27, 11, 0, 5, 0, timeutil.BangkokLocation()),
					},
				},
			}, nil).Times(2)

		tt := time.Date(2021, 5, 27, 11, 1, 4, 0, timeutil.BangkokLocation())
		attr, er := att.GetCurrentShiftAttendance(context.Background(), driverId, shifts, tt)
		require.Equal(t, nil, er)
		require.Equal(t, ShiftAttendanceRate{
			ShiftID:   "000000000000000000000000",
			Rate:      100,
			BreakTime: 59,
		}, attr)
	})
}

type AttendanceDeps struct {
	shiftRepo           *mock_repository.MockShiftRepository
	driverRepo          *mock_repository.MockDriverRepository
	driverOrderInfoRepo *mock_repository.MockDriverOrderInfoRepository
	config              config.AttendanceRateConfig
}

func newTestAttendance(r gomock.TestReporter) (*Attendance, *AttendanceDeps, func()) {
	ctrl := gomock.NewController(r)
	deps := &AttendanceDeps{
		shiftRepo:           mock_repository.NewMockShiftRepository(ctrl),
		driverRepo:          mock_repository.NewMockDriverRepository(ctrl),
		driverOrderInfoRepo: mock_repository.NewMockDriverOrderInfoRepository(ctrl),
		config: config.AttendanceRateConfig{
			AttendanceRateEnabled: true,
		},
	}
	att := &Attendance{
		ShiftRepo:       deps.shiftRepo,
		DriverRepo:      deps.driverRepo,
		DriverOrderRepo: deps.driverOrderInfoRepo,
		Config:          deps.config,
	}

	return att, deps, func() { ctrl.Finish() }
}
