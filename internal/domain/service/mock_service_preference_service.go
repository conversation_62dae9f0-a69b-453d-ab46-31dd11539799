// Code generated by MockGen. DO NOT EDIT.
// Source: ./service_preference_service.go

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockServicePreferenceService is a mock of ServicePreferenceService interface.
type MockServicePreferenceService struct {
	ctrl     *gomock.Controller
	recorder *MockServicePreferenceServiceMockRecorder
}

// MockServicePreferenceServiceMockRecorder is the mock recorder for MockServicePreferenceService.
type MockServicePreferenceServiceMockRecorder struct {
	mock *MockServicePreferenceService
}

// NewMockServicePreferenceService creates a new mock instance.
func NewMockServicePreferenceService(ctrl *gomock.Controller) *MockServicePreferenceService {
	mock := &MockServicePreferenceService{ctrl: ctrl}
	mock.recorder = &MockServicePreferenceServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServicePreferenceService) EXPECT() *MockServicePreferenceServiceMockRecorder {
	return m.recorder
}

// GetExperimentWhitelistedDriverIDs mocks base method.
func (m *MockServicePreferenceService) GetExperimentWhitelistedDriverIDs() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExperimentWhitelistedDriverIDs")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetExperimentWhitelistedDriverIDs indicates an expected call of GetExperimentWhitelistedDriverIDs.
func (mr *MockServicePreferenceServiceMockRecorder) GetExperimentWhitelistedDriverIDs() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExperimentWhitelistedDriverIDs", reflect.TypeOf((*MockServicePreferenceService)(nil).GetExperimentWhitelistedDriverIDs))
}

// OptOutAllowedServicesFromPreference mocks base method.
func (m *MockServicePreferenceService) OptOutAllowedServicesFromPreference(ctx context.Context, preference model.ServicePreference) ([]model.Service, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OptOutAllowedServicesFromPreference", ctx, preference)
	ret0, _ := ret[0].([]model.Service)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// OptOutAllowedServicesFromPreference indicates an expected call of OptOutAllowedServicesFromPreference.
func (mr *MockServicePreferenceServiceMockRecorder) OptOutAllowedServicesFromPreference(ctx, preference interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OptOutAllowedServicesFromPreference", reflect.TypeOf((*MockServicePreferenceService)(nil).OptOutAllowedServicesFromPreference), ctx, preference)
}

// OptOutAllowedServicesFromPreferenceWithWhitelist mocks base method.
func (m *MockServicePreferenceService) OptOutAllowedServicesFromPreferenceWithWhitelist(ctx context.Context, preference model.ServicePreference, driverID string) ([]model.Service, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OptOutAllowedServicesFromPreferenceWithWhitelist", ctx, preference, driverID)
	ret0, _ := ret[0].([]model.Service)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// OptOutAllowedServicesFromPreferenceWithWhitelist indicates an expected call of OptOutAllowedServicesFromPreferenceWithWhitelist.
func (mr *MockServicePreferenceServiceMockRecorder) OptOutAllowedServicesFromPreferenceWithWhitelist(ctx, preference, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OptOutAllowedServicesFromPreferenceWithWhitelist", reflect.TypeOf((*MockServicePreferenceService)(nil).OptOutAllowedServicesFromPreferenceWithWhitelist), ctx, preference, driverID)
}

// OverridePreferenceForAdmin mocks base method.
func (m *MockServicePreferenceService) OverridePreferenceForAdmin(ctx context.Context, preference model.ServicePreference) (model.ServicePreference, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OverridePreferenceForAdmin", ctx, preference)
	ret0, _ := ret[0].(model.ServicePreference)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OverridePreferenceForAdmin indicates an expected call of OverridePreferenceForAdmin.
func (mr *MockServicePreferenceServiceMockRecorder) OverridePreferenceForAdmin(ctx, preference interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OverridePreferenceForAdmin", reflect.TypeOf((*MockServicePreferenceService)(nil).OverridePreferenceForAdmin), ctx, preference)
}
