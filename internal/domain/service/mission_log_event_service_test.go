package service_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"

	drivermissionrewardpb "git.wndv.co/go/proto/lineman/driver_mission_reward/v1"
	dmrv1 "git.wndv.co/go/proto/lineman/event/driver_mission_reward/v1"
	mock_kafka "git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/mock_kafka"
	domainModel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestMissionLogEventServiceImpl_PublishMissionLogOrderEvent(t *testing.T) {
	t.<PERSON>()
	mockTime := time.Now()
	type fields struct {
		Cfg service.MissionLogEventServiceConfig
	}
	type args struct {
		ctx   context.Context
		order *domainModel.Order
	}
	type expects struct {
		kafkaCalled int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		expects expects
	}{
		{
			"should publish mission log event",
			fields{
				Cfg: service.MissionLogEventServiceConfig{},
			},
			args{
				ctx: context.Background(),
				order: &domainModel.Order{
					TripID: "Trip-1",
					Driver: "Driver-1",
					Quote: domainModel.Quote{
						CreatedAt:   mockTime,
						ServiceType: domainModel.ServiceFood,
						Routes: []domainModel.Stop{
							{
								Location: domainModel.Location{
									Lat: 1.0,
									Lng: 1.0,
								},
							},
						},
					},
				},
			},
			expects{
				kafkaCalled: 1,
			},
		},
		{
			"not should publish mission log event",
			fields{
				Cfg: service.MissionLogEventServiceConfig{},
			},
			args{
				ctx:   context.Background(),
				order: nil,
			},
			expects{
				kafkaCalled: 0,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			kafkaProducer := mock_kafka.NewMockIMFKafkaProducer(ctrl)
			mes := service.MissionLogEventServiceImpl{
				Cfg:              tt.fields.Cfg,
				IMFKafkaProducer: kafkaProducer,
			}
			kafkaProducer.EXPECT().SendMessage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(tt.expects.kafkaCalled)
			mes.PublishMissionLogOrderEvent(tt.args.ctx, tt.args.order)
		})
	}
}

type missionLogEventDeps struct {
	orderRepo                    *mock_repository.MockOrderRepository
	tripRepo                     *mock_repository.MockTripRepository
	IMFkafka                     *mock_kafka.MockIMFKafkaProducer
	driverServiceTypeCapacitySvc *mock_service.MockDriverServiceTypeCapacityService
}

func newMissionLogEventService(r gomock.TestReporter) (service.MissionLogEventService, missionLogEventDeps, func()) {
	ctrl := gomock.NewController(r)
	kafkaProducer := mock_kafka.NewMockIMFKafkaProducer(ctrl)
	orderRepo := mock_repository.NewMockOrderRepository(ctrl)
	tripRepo := mock_repository.NewMockTripRepository(ctrl)
	driverServiceTypeCapacitySvc := mock_service.NewMockDriverServiceTypeCapacityService(ctrl)

	return service.ProvideMissionLogEventService(
			service.MissionLogEventServiceConfig{},
			kafkaProducer,
			orderRepo,
			tripRepo,
			driverServiceTypeCapacitySvc,
		),
		missionLogEventDeps{
			orderRepo:                    orderRepo,
			tripRepo:                     tripRepo,
			IMFkafka:                     kafkaProducer,
			driverServiceTypeCapacitySvc: driverServiceTypeCapacitySvc,
		}, ctrl.Finish
}

func TestMissionLogEventServiceImpl_PublishMissionLogTripEvent(t *testing.T) {
	t.Parallel()
	type fields struct {
		Cfg service.MissionLogEventServiceConfig
	}

	type args struct {
		ctx  context.Context
		trip *domainModel.Trip
	}
	type expects struct {
		repoCall bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		expects expects
	}{
		{
			"should publish mission log event",
			fields{
				Cfg: service.MissionLogEventServiceConfig{},
			},
			args{
				ctx: context.Background(),
				trip: &domainModel.Trip{
					TripID: "Trip-1",
					Orders: domainModel.TripOrders{{
						OrderID: "a",
					}},
					Status: domainModel.TripStatusCompleted,
				},
			},
			expects{
				repoCall: true,
			},
		},
		{
			"not should publish mission log event",
			fields{
				Cfg: service.MissionLogEventServiceConfig{},
			},
			args{
				ctx:  context.Background(),
				trip: nil,
			},
			expects{
				repoCall: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, deps, finish := newMissionLogEventService(t)
			defer finish()
			if tt.expects.repoCall {
				deps.orderRepo.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return([]domainModel.Order{
					{
						OrderID: "a",
					},
				}, nil)
				deps.driverServiceTypeCapacitySvc.EXPECT().GetAllServiceTypeCapacityByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ServiceTypeCapacity{}, nil)
				deps.IMFkafka.EXPECT().SendMessage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			}
			svc.PublishMissionLogTripEvent(tt.args.ctx, tt.args.trip)
		})
	}
}

func Test_toMissionLogOrderEvent(t *testing.T) {
	type args struct {
		order *domainModel.Order
	}
	mockTime := time.Now()
	tests := []struct {
		name    string
		args    args
		want    *dmrv1.MissionLogEvent
		wantErr assert.ErrorAssertionFunc
	}{
		{name: "should return mission log event", args: args{order: &domainModel.Order{
			OrderID: "Order-1",
			TripID:  "Trip-1",
			Driver:  "Driver-1",
			Quote: domainModel.Quote{
				CreatedAt:   mockTime,
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						Location: domainModel.Location{
							Lat: 1.0,
							Lng: 1.0,
						},
					},
				},
			},
		}}, want: &dmrv1.MissionLogEvent{
			RefId:       "Order-1",
			DriverId:    "Driver-1",
			Source:      drivermissionrewardpb.Source_SOURCE_FLEET,
			ServiceType: drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_FOOD,
			OrderType:   drivermissionrewardpb.OrderType_ORDER_TYPE_FLEET_FOOD,
			Location: &drivermissionrewardpb.Location{
				Lat: 1.0,
				Lng: 1.0,
			},
			AchievedAt: timestamppb.New(mockTime),
		}, wantErr: assert.NoError},
		{
			name: "should return first stop location mission log event when driver location is nil", args: args{
				order: &domainModel.Order{
					TripID:  "Trip-1",
					OrderID: "Order-1",
					Driver:  "Driver-1",
					Quote: domainModel.Quote{
						Routes: []domainModel.Stop{
							{
								Location: domainModel.Location{
									Lat: 1.0,
									Lng: 1.0,
								},
							},
						},
						CreatedAt:   mockTime,
						ServiceType: domainModel.ServiceFood,
					},
				},
			},
			want: &dmrv1.MissionLogEvent{
				RefId:       "Order-1",
				DriverId:    "Driver-1",
				Source:      drivermissionrewardpb.Source_SOURCE_FLEET,
				ServiceType: drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_FOOD,
				OrderType:   drivermissionrewardpb.OrderType_ORDER_TYPE_FLEET_FOOD,
				Location: &drivermissionrewardpb.Location{
					Lat: 1.0,
					Lng: 1.0,
				},
				AchievedAt: timestamppb.New(mockTime),
			}, wantErr: assert.NoError,
		},
		{
			name: "should return null location mission log", args: args{
				order: &domainModel.Order{
					TripID:  "Trip-1",
					OrderID: "Order-1",
					Driver:  "Driver-1",
					Quote: domainModel.Quote{
						Routes:      []domainModel.Stop{},
						CreatedAt:   mockTime,
						ServiceType: domainModel.ServiceFood,
					},
				},
			},
			want: &dmrv1.MissionLogEvent{
				RefId:       "Order-1",
				DriverId:    "Driver-1",
				Source:      drivermissionrewardpb.Source_SOURCE_FLEET,
				ServiceType: drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_FOOD,
				OrderType:   drivermissionrewardpb.OrderType_ORDER_TYPE_FLEET_FOOD,
				Location:    nil,
				AchievedAt:  timestamppb.New(mockTime),
			}, wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := service.ToMissionLogOrderEvent(tt.args.order)
			if !tt.wantErr(t, err, fmt.Sprintf("ToMissionLogOrderEvent(%v)", tt.args.order)) {
				return
			}
			assert.Equalf(t, tt.want, got, "ToMissionLogOrderEvent(%v)", tt.args.order)
		})
	}
}

func Test_toMissionLogTripEvent(t *testing.T) {
	type args struct {
		trip         *domainModel.Trip
		previousTrip *domainModel.Trip
		orders       []domainModel.Order
		capa         []domainModel.ServiceTypeCapacity
	}
	tests := []struct {
		name    string
		args    args
		want    *dmrv1.MissionLogEvent
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "Single orders",
			args: args{
				trip: &domainModel.Trip{
					TripID: "trip-1",
					Routes: []domainModel.TripRoute{
						{
							Action: domainModel.TripActionPickUp,
							Location: domainModel.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  0,
								},
							},
						},
						{
							Action: domainModel.TripActionDropOff,
							Location: domainModel.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  1,
								},
							},
						},
					},
					Orders: []domainModel.TripOrder{
						{
							OrderID: "order-1",
							Status:  domainModel.StatusCompleted,
						},
					},
					DriverID: "Driver-1",
					History: domainModel.TripHistory{
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToRestaurant, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrivedRestaurant, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 15, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToDestination, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 30, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrived, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 35, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDropOffDone, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusCompleted, TripStatus: domainModel.TripStatusCompleted, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
					},
					Status:    domainModel.TripStatusCompleted,
					CreatedAt: time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation()),
				},
				orders: []domainModel.Order{
					{
						OrderID: "order-1",
						Status:  domainModel.StatusCompleted,
						HistoryLocation: map[string]domainModel.LocationWithUpdatedAt{
							domainModel.StatusDriverMatched.String(): {
								Lat: 0.5,
								Lng: 0.5,
							},
						},
					},
				},
			},
			want: &dmrv1.MissionLogEvent{
				RefId:    "trip-1",
				DriverId: "Driver-1",
				Source:   drivermissionrewardpb.Source_SOURCE_FLEET,
				Orders: []*dmrv1.Order{
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_PICKUP,
							Location: &drivermissionrewardpb.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_DROPOFF,
							Location: &drivermissionrewardpb.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
				},
				AchievedAt:  timestamppb.New(time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation())),
				ToPickupAt:  timestamppb.New(time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())),
				CompletedAt: timestamppb.New(time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())),
				AcceptedTripLocation: &drivermissionrewardpb.Location{
					Lat: 0.5,
					Lng: 0.5,
				},
				CurrentServicePreferences: []drivermissionrewardpb.ServiceType{},
			},
			wantErr: assert.NoError,
		},
		{
			name: "MO 2 orders",
			args: args{
				trip: &domainModel.Trip{
					TripID: "trip-1",
					Routes: []domainModel.TripRoute{
						{
							Action: domainModel.TripActionPickUp,
							Location: domainModel.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  0,
								},
							},
						},
						{
							Action: domainModel.TripActionPickUp,
							Location: domainModel.Location{
								Lat: 2.0,
								Lng: 2.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-2",
									StopID:  0,
								},
							},
						},
						{
							Action: domainModel.TripActionDropOff,
							Location: domainModel.Location{
								Lat: 3.0,
								Lng: 3.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-2",
									StopID:  1,
								},
							},
						},
						{
							Action: domainModel.TripActionDropOff,
							Location: domainModel.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  1,
								},
							},
						},
					},
					Orders: []domainModel.TripOrder{
						{
							OrderID: "order-1",
							Status:  domainModel.StatusCompleted,
						},
						{
							OrderID: "order-2",
							Status:  domainModel.StatusCompleted,
						},
					},
					DriverID: "Driver-1",
					History: domainModel.TripHistory{
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToRestaurant, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrivedRestaurant, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 15, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusDriverToRestaurant, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 30, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusDriverArrivedRestaurant, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 35, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusDriverToDestination, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusDriverArrived, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 50, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusDropOffDone, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 50, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusCompleted, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 50, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToDestination, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 55, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrived, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 11, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDropOffDone, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 11, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusCompleted, TripStatus: domainModel.TripStatusCompleted, Timestamp: time.Date(2025, 1, 1, 11, 0, 0, 0, timeutil.BangkokLocation())},
					},
					Status:    domainModel.TripStatusCompleted,
					CreatedAt: time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation()),
				},
				orders: []domainModel.Order{
					{
						OrderID: "order-1",
						Status:  domainModel.StatusCompleted,
						HistoryLocation: map[string]domainModel.LocationWithUpdatedAt{
							domainModel.StatusDriverMatched.String(): {
								Lat: 0.5,
								Lng: 0.5,
							},
						},
					},
					{
						OrderID: "order-2",
						Status:  domainModel.StatusCompleted,
						HistoryLocation: map[string]domainModel.LocationWithUpdatedAt{
							domainModel.StatusDriverMatched.String(): {
								Lat: 1.5,
								Lng: 1.5,
							},
						},
					},
				},
			},
			want: &dmrv1.MissionLogEvent{
				RefId:    "trip-1",
				DriverId: "Driver-1",
				Source:   drivermissionrewardpb.Source_SOURCE_FLEET,
				Orders: []*dmrv1.Order{
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_PICKUP,
							Location: &drivermissionrewardpb.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-2",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_PICKUP,
							Location: &drivermissionrewardpb.Location{
								Lat: 2.0,
								Lng: 2.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-2",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_DROPOFF,
							Location: &drivermissionrewardpb.Location{
								Lat: 3.0,
								Lng: 3.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_DROPOFF,
							Location: &drivermissionrewardpb.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
				},
				AchievedAt:  timestamppb.New(time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation())),
				ToPickupAt:  timestamppb.New(time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())),
				CompletedAt: timestamppb.New(time.Date(2025, 1, 1, 11, 0, 0, 0, timeutil.BangkokLocation())),
				AcceptedTripLocation: &drivermissionrewardpb.Location{
					Lat: 0.5,
					Lng: 0.5,
				},
				CurrentServicePreferences: []drivermissionrewardpb.ServiceType{},
			},
			wantErr: assert.NoError,
		},
		{
			name: "MO 3 orders",
			args: args{
				trip: &domainModel.Trip{
					TripID: "trip-1",
					Routes: []domainModel.TripRoute{
						{
							Action: domainModel.TripActionPickUp,
							Location: domainModel.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  0,
								},
							},
						},
						{
							Action: domainModel.TripActionPickUp,
							Location: domainModel.Location{
								Lat: 2.0,
								Lng: 2.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-2",
									StopID:  0,
								},
							},
						},
						{
							Action: domainModel.TripActionPickUp,
							Location: domainModel.Location{
								Lat: 3.0,
								Lng: 3.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-3",
									StopID:  0,
								},
							},
						},
						{
							Action: domainModel.TripActionDropOff,
							Location: domainModel.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-2",
									StopID:  1,
								},
							},
						},
						{
							Action: domainModel.TripActionDropOff,
							Location: domainModel.Location{
								Lat: 5.0,
								Lng: 5.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  1,
								},
							},
						},
						{
							Action: domainModel.TripActionDropOff,
							Location: domainModel.Location{
								Lat: 6.0,
								Lng: 6.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-3",
									StopID:  1,
								},
							},
						},
					},
					Orders: []domainModel.TripOrder{
						{
							OrderID: "order-1",
							Status:  domainModel.StatusCompleted,
						},
						{
							OrderID: "order-2",
							Status:  domainModel.StatusCompleted,
						},
					},
					DriverID: "Driver-1",
					History: domainModel.TripHistory{
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToRestaurant, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrivedRestaurant, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 15, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusDriverToRestaurant, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 30, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusDriverArrivedRestaurant, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 35, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-3", OrderStatus: domainModel.StatusDriverToRestaurant, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-3", OrderStatus: domainModel.StatusDriverArrivedRestaurant, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 45, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusDriverToDestination, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 45, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusDriverArrived, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 50, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusDropOffDone, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 50, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-2", OrderStatus: domainModel.StatusCompleted, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 50, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToDestination, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 55, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrived, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 11, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDropOffDone, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 11, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusCompleted, TripStatus: domainModel.TripStatusCompleted, Timestamp: time.Date(2025, 1, 1, 11, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-3", OrderStatus: domainModel.StatusDriverToDestination, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 11, 5, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-3", OrderStatus: domainModel.StatusDriverArrived, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 11, 10, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-3", OrderStatus: domainModel.StatusDropOffDone, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 11, 10, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-3", OrderStatus: domainModel.StatusCompleted, TripStatus: domainModel.TripStatusCompleted, Timestamp: time.Date(2025, 1, 1, 11, 10, 0, 0, timeutil.BangkokLocation())},
					},
					Status:    domainModel.TripStatusCompleted,
					CreatedAt: time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation()),
				},
				orders: []domainModel.Order{
					{
						OrderID: "order-1",
						Status:  domainModel.StatusCompleted,
						HistoryLocation: map[string]domainModel.LocationWithUpdatedAt{
							domainModel.StatusDriverMatched.String(): {
								Lat: 0.5,
								Lng: 0.5,
							},
						},
					},
					{
						OrderID: "order-2",
						Status:  domainModel.StatusCompleted,
						HistoryLocation: map[string]domainModel.LocationWithUpdatedAt{
							domainModel.StatusDriverMatched.String(): {
								Lat: 1.5,
								Lng: 1.5,
							},
						},
					},
					{
						OrderID: "order-3",
						Status:  domainModel.StatusCompleted,
						HistoryLocation: map[string]domainModel.LocationWithUpdatedAt{
							domainModel.StatusDriverMatched.String(): {
								Lat: 2.5,
								Lng: 2.5,
							},
						},
					},
				},
			},
			want: &dmrv1.MissionLogEvent{
				RefId:    "trip-1",
				DriverId: "Driver-1",
				Source:   drivermissionrewardpb.Source_SOURCE_FLEET,
				Orders: []*dmrv1.Order{
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_PICKUP,
							Location: &drivermissionrewardpb.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-2",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_PICKUP,
							Location: &drivermissionrewardpb.Location{
								Lat: 2.0,
								Lng: 2.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-3",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_PICKUP,
							Location: &drivermissionrewardpb.Location{
								Lat: 3.0,
								Lng: 3.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-2",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_DROPOFF,
							Location: &drivermissionrewardpb.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_DROPOFF,
							Location: &drivermissionrewardpb.Location{
								Lat: 5.0,
								Lng: 5.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-3",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_DROPOFF,
							Location: &drivermissionrewardpb.Location{
								Lat: 6.0,
								Lng: 6.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
				},
				AchievedAt:  timestamppb.New(time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation())),
				ToPickupAt:  timestamppb.New(time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())),
				CompletedAt: timestamppb.New(time.Date(2025, 1, 1, 11, 10, 0, 0, timeutil.BangkokLocation())),
				AcceptedTripLocation: &drivermissionrewardpb.Location{
					Lat: 0.5,
					Lng: 0.5,
				},
				CurrentServicePreferences: []drivermissionrewardpb.ServiceType{},
			},
			wantErr: assert.NoError,
		},
		{
			name: "B2B trips",
			args: args{
				trip: &domainModel.Trip{
					TripID: "trip-1",
					Routes: []domainModel.TripRoute{
						{
							Action: domainModel.TripActionPickUp,
							Location: domainModel.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  0,
								},
							},
						},
						{
							Action: domainModel.TripActionDropOff,
							Location: domainModel.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  1,
								},
							},
						},
					},
					Orders: []domainModel.TripOrder{
						{
							OrderID: "order-1",
							Status:  domainModel.StatusCompleted,
						},
					},
					DriverID: "Driver-1",
					History: domainModel.TripHistory{
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToRestaurant, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrivedRestaurant, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 15, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToDestination, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 30, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrived, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 35, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDropOffDone, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusCompleted, TripStatus: domainModel.TripStatusCompleted, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
					},
					Status:    domainModel.TripStatusCompleted,
					CreatedAt: time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation()),
				},
				previousTrip: &domainModel.Trip{
					TripID: "previous-trip",
					Routes: []domainModel.TripRoute{
						{
							Action: domainModel.TripActionPickUp,
							Location: domainModel.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  0,
								},
							},
						},
						{
							Action: domainModel.TripActionDropOff,
							Location: domainModel.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  1,
								},
							},
						},
					},
					Orders: []domainModel.TripOrder{
						{
							OrderID: "order-1",
							Status:  domainModel.StatusCompleted,
						},
					},
					DriverID: "Driver-1",
					History: domainModel.TripHistory{
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToRestaurant, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrivedRestaurant, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 15, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToDestination, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 30, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrived, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 35, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDropOffDone, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusCompleted, TripStatus: domainModel.TripStatusCompleted, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
					},
					Status:    domainModel.TripStatusCompleted,
					CreatedAt: time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation()),
				},
				orders: []domainModel.Order{
					{
						OrderID: "order-1",
						Status:  domainModel.StatusCompleted,
						HistoryLocation: map[string]domainModel.LocationWithUpdatedAt{
							domainModel.StatusDriverMatched.String(): {
								Lat: 0.5,
								Lng: 0.5,
							},
						},
					},
				},
			},
			want: &dmrv1.MissionLogEvent{
				RefId:    "trip-1",
				DriverId: "Driver-1",
				Source:   drivermissionrewardpb.Source_SOURCE_FLEET,
				Orders: []*dmrv1.Order{
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_PICKUP,
							Location: &drivermissionrewardpb.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_DROPOFF,
							Location: &drivermissionrewardpb.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
				},
				AchievedAt:  timestamppb.New(time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation())),
				ToPickupAt:  timestamppb.New(time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())),
				CompletedAt: timestamppb.New(time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())),
				AcceptedTripLocation: &drivermissionrewardpb.Location{
					Lat: 0.5,
					Lng: 0.5,
				},
				PreviousTripLastDropOffLocation: &drivermissionrewardpb.Location{
					Lat: 4.0,
					Lng: 4.0,
				},
				CurrentServicePreferences: []drivermissionrewardpb.ServiceType{},
			},
			wantErr: assert.NoError,
		},
		{
			name: "should publish message with service preference = food&mart, messenger, bike when rider service preference = food,mart,messenger,bike and complete the trip",
			args: args{
				trip: &domainModel.Trip{
					TripID: "trip-1",
					Routes: []domainModel.TripRoute{
						{
							Action: domainModel.TripActionPickUp,
							Location: domainModel.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  0,
								},
							},
						},
						{
							Action: domainModel.TripActionDropOff,
							Location: domainModel.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  1,
								},
							},
						},
					},
					Orders: []domainModel.TripOrder{
						{
							OrderID: "order-1",
							Status:  domainModel.StatusCompleted,
						},
					},
					DriverID: "Driver-1",
					History: domainModel.TripHistory{
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToRestaurant, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrivedRestaurant, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 15, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToDestination, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 30, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrived, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 35, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDropOffDone, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusCompleted, TripStatus: domainModel.TripStatusCompleted, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
					},
					Status:    domainModel.TripStatusCompleted,
					CreatedAt: time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation()),
				},
				orders: []domainModel.Order{
					{
						OrderID: "order-1",
						Status:  domainModel.StatusCompleted,
						HistoryLocation: map[string]domainModel.LocationWithUpdatedAt{
							domainModel.StatusDriverMatched.String(): {
								Lat: 0.5,
								Lng: 0.5,
							},
						},
					},
				},
				capa: []domainModel.ServiceTypeCapacity{
					{
						ServiceType: domainModel.ServiceFood,
						Enabled:     true,
					},
					{
						ServiceType: domainModel.ServiceMart,
						Enabled:     true,
					},
					{
						ServiceType: domainModel.ServiceMessenger,
						Enabled:     true,
					},
					{
						ServiceType: domainModel.ServiceBike,
						Enabled:     true,
					},
				},
			},
			want: &dmrv1.MissionLogEvent{
				RefId:    "trip-1",
				DriverId: "Driver-1",
				Source:   drivermissionrewardpb.Source_SOURCE_FLEET,
				Orders: []*dmrv1.Order{
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_PICKUP,
							Location: &drivermissionrewardpb.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_DROPOFF,
							Location: &drivermissionrewardpb.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
				},
				AchievedAt:  timestamppb.New(time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation())),
				ToPickupAt:  timestamppb.New(time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())),
				CompletedAt: timestamppb.New(time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())),
				AcceptedTripLocation: &drivermissionrewardpb.Location{
					Lat: 0.5,
					Lng: 0.5,
				},
				CurrentServicePreferences: []drivermissionrewardpb.ServiceType{
					drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_FOOD,
					drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_MART,
					drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_MESSENGER,
					drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_TRANSPORTATION,
				},
			},
			wantErr: assert.NoError,
		},
		{
			name: "should publish message with service preference = messenger&bike when rider service preference = messenger&bike and complete the trip",
			args: args{
				trip: &domainModel.Trip{
					TripID: "trip-1",
					Routes: []domainModel.TripRoute{
						{
							Action: domainModel.TripActionPickUp,
							Location: domainModel.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  0,
								},
							},
						},
						{
							Action: domainModel.TripActionDropOff,
							Location: domainModel.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
							StopOrders: []domainModel.TripStopOrder{
								{
									OrderID: "order-1",
									StopID:  1,
								},
							},
						},
					},
					Orders: []domainModel.TripOrder{
						{
							OrderID: "order-1",
							Status:  domainModel.StatusCompleted,
						},
					},
					DriverID: "Driver-1",
					History: domainModel.TripHistory{
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToRestaurant, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrivedRestaurant, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 15, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverToDestination, TripStatus: domainModel.TripStatusDriveTo, Timestamp: time.Date(2025, 1, 1, 10, 30, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDriverArrived, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 35, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusDropOffDone, TripStatus: domainModel.TripStatusArrivedAt, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
						{OrderID: "order-1", OrderStatus: domainModel.StatusCompleted, TripStatus: domainModel.TripStatusCompleted, Timestamp: time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())},
					},
					Status:    domainModel.TripStatusCompleted,
					CreatedAt: time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation()),
				},
				orders: []domainModel.Order{
					{
						OrderID: "order-1",
						Status:  domainModel.StatusCompleted,
						HistoryLocation: map[string]domainModel.LocationWithUpdatedAt{
							domainModel.StatusDriverMatched.String(): {
								Lat: 0.5,
								Lng: 0.5,
							},
						},
					},
				},
				capa: []domainModel.ServiceTypeCapacity{
					{
						ServiceType: domainModel.ServiceFood,
						Enabled:     false,
					},
					{
						ServiceType: domainModel.ServiceMart,
						Enabled:     false,
					},
					{
						ServiceType: domainModel.ServiceMessenger,
						Enabled:     true,
					},
					{
						ServiceType: domainModel.ServiceBike,
						Enabled:     true,
					},
				},
			},
			want: &dmrv1.MissionLogEvent{
				RefId:    "trip-1",
				DriverId: "Driver-1",
				Source:   drivermissionrewardpb.Source_SOURCE_FLEET,
				Orders: []*dmrv1.Order{
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_PICKUP,
							Location: &drivermissionrewardpb.Location{
								Lat: 1.0,
								Lng: 1.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
					{
						OrderId: "order-1",
						Stop: &dmrv1.Stop{
							Type: dmrv1.StopType_STOP_TYPE_DROPOFF,
							Location: &drivermissionrewardpb.Location{
								Lat: 4.0,
								Lng: 4.0,
							},
						},
						Status: domainModel.StatusCompleted.String(),
					},
				},
				AchievedAt:  timestamppb.New(time.Date(2025, 1, 1, 9, 55, 0, 0, timeutil.BangkokLocation())),
				ToPickupAt:  timestamppb.New(time.Date(2025, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())),
				CompletedAt: timestamppb.New(time.Date(2025, 1, 1, 10, 40, 0, 0, timeutil.BangkokLocation())),
				AcceptedTripLocation: &drivermissionrewardpb.Location{
					Lat: 0.5,
					Lng: 0.5,
				},
				CurrentServicePreferences: []drivermissionrewardpb.ServiceType{
					drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_MESSENGER,
					drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_TRANSPORTATION,
				},
			},
			wantErr: assert.NoError,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(tt *testing.T) {
			got, err := service.ToMissionLogTripEvent(test.args.trip, test.args.previousTrip, test.args.orders, test.args.capa)
			if !test.wantErr(t, err) {
				return
			}
			assert.Equalf(tt, test.want, got, test.name)
		})
	}
}

func Test_toServiceType(t *testing.T) {
	type args struct {
		service domainModel.Service
	}
	tests := []struct {
		name    string
		args    args
		want    drivermissionrewardpb.ServiceType
		wantErr assert.ErrorAssertionFunc
	}{
		{name: "should return service type", args: args{service: domainModel.ServiceFood}, want: drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_FOOD, wantErr: assert.NoError},
		{name: "should return service type", args: args{service: domainModel.ServiceMart}, want: drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_MART, wantErr: assert.NoError},
		{name: "should return service type", args: args{service: domainModel.ServiceMessenger}, want: drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_MESSENGER, wantErr: assert.NoError},
		{name: "should return service type", args: args{service: domainModel.ServiceBike}, want: drivermissionrewardpb.ServiceType_SERVICE_TYPE_FLEET_TRANSPORTATION, wantErr: assert.NoError},
		{name: "should return error", args: args{service: domainModel.Service("unknown")}, want: 0, wantErr: assert.Error},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := service.ToServiceType(tt.args.service)
			if !tt.wantErr(t, err, fmt.Sprintf("ToServiceType(%v)", tt.args.service)) {
				return
			}
			assert.Equalf(t, tt.want, got, "ToServiceType(%v)", tt.args.service)
		})
	}
}
