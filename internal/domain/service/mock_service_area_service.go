// Code generated by MockGen. DO NOT EDIT.
// Source: ./service_area_service.go

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	config "git.wndv.co/lineman/fleet-distribution/internal/config"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockServiceAreaService is a mock of ServiceAreaService interface.
type MockServiceAreaService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceAreaServiceMockRecorder
}

// MockServiceAreaServiceMockRecorder is the mock recorder for MockServiceAreaService.
type MockServiceAreaServiceMockRecorder struct {
	mock *MockServiceAreaService
}

// NewMockServiceAreaService creates a new mock instance.
func NewMockServiceAreaService(ctrl *gomock.Controller) *MockServiceAreaService {
	mock := &MockServiceAreaService{ctrl: ctrl}
	mock.recorder = &MockServiceAreaServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceAreaService) EXPECT() *MockServiceAreaServiceMockRecorder {
	return m.recorder
}

// GetBackToBackAllowStatus mocks base method.
func (m *MockServiceAreaService) GetBackToBackAllowStatus(ctx context.Context, region string) []model.Status {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBackToBackAllowStatus", ctx, region)
	ret0, _ := ret[0].([]model.Status)
	return ret0
}

// GetBackToBackAllowStatus indicates an expected call of GetBackToBackAllowStatus.
func (mr *MockServiceAreaServiceMockRecorder) GetBackToBackAllowStatus(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBackToBackAllowStatus", reflect.TypeOf((*MockServiceAreaService)(nil).GetBackToBackAllowStatus), ctx, region)
}

// GetByID mocks base method.
func (m *MockServiceAreaService) GetByID(ctx context.Context, id string) (*model.ServiceArea, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.ServiceArea)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockServiceAreaServiceMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockServiceAreaService)(nil).GetByID), ctx, id)
}

// GetCompleteTimeCalculationConfig mocks base method.
func (m *MockServiceAreaService) GetCompleteTimeCalculationConfig(ctx context.Context, region string) *config.CompleteTimeCalculation {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCompleteTimeCalculationConfig", ctx, region)
	ret0, _ := ret[0].(*config.CompleteTimeCalculation)
	return ret0
}

// GetCompleteTimeCalculationConfig indicates an expected call of GetCompleteTimeCalculationConfig.
func (mr *MockServiceAreaServiceMockRecorder) GetCompleteTimeCalculationConfig(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCompleteTimeCalculationConfig", reflect.TypeOf((*MockServiceAreaService)(nil).GetCompleteTimeCalculationConfig), ctx, region)
}

// RemoveDriverFromShiftWhitelist mocks base method.
func (m *MockServiceAreaService) RemoveDriverFromShiftWhitelist(ctx context.Context, driverId, region string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveDriverFromShiftWhitelist", ctx, driverId, region)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveDriverFromShiftWhitelist indicates an expected call of RemoveDriverFromShiftWhitelist.
func (mr *MockServiceAreaServiceMockRecorder) RemoveDriverFromShiftWhitelist(ctx, driverId, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveDriverFromShiftWhitelist", reflect.TypeOf((*MockServiceAreaService)(nil).RemoveDriverFromShiftWhitelist), ctx, driverId, region)
}
