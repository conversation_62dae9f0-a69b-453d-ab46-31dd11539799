package drivertransaction

import (
	"context"
	"errors"
	"fmt"

	"git.wndv.co/go/logx/v2"
	domainModel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
)

var (
	ErrInvalidMinimalDriverData    = errors.New("invalid minimal driver data")
	ErrNoInstallmentOnTopQuotaData = errors.New("no installation on top quota")
	ErrNotEnoughRemainingQuota     = errors.New("not enough remaining quota")
)

type InstallmentOnTopTransactionProvider struct {
	driverRepository repository.DriverRepository
}

// ProvideInstallmentOnTopTransactionProvider provide the InstallmentOnTopTransactionProvider that implement Provider[domainModel.TransactionInfo]
// @@wire-set-name@@ name:"DriverTransactionProvider"
func ProvideInstallmentOnTopTransactionProvider(driverRepository repository.DriverRepository) *InstallmentOnTopTransactionProvider {
	return &InstallmentOnTopTransactionProvider{
		driverRepository: driverRepository,
	}
}

func (i *InstallmentOnTopTransactionProvider) GetName() string {
	return "InstallmentOnTopTransactionProvider"
}

func (i *InstallmentOnTopTransactionProvider) ProvideTransaction(ctx context.Context, order domainModel.Order, focusedOnTopSchemes []domainModel.OnTopScheme) (ProviderResult[*domainModel.TransactionInfo], error) {
	focusedDriverID := order.Driver

	installmentOnTopQuotaByID, err := i.getInstallmentOnTopQuota(ctx, focusedDriverID)
	if err != nil {
		return ProviderResult[*domainModel.TransactionInfo]{}, err
	}

	// To Generate OnTop Scheme
	toBeGeneratedOnTopScheme := make([]domainModel.OnTopScheme, 0, len(focusedOnTopSchemes))
	for _, eachFocusedOnTopScheme := range focusedOnTopSchemes {
		focusedInstallmentOnTopQuota, ok := installmentOnTopQuotaByID[eachFocusedOnTopScheme.ID]
		if !ok {
			logx.Error().
				Context(ctx).
				Msg("Installment on top quota not found")
			continue
		}
		err = i.validate(ctx, focusedInstallmentOnTopQuota.OnTopQuota)
		if err != nil {
			safe.SentryError(
				err,
				safe.WithOrderID(order.OrderID),
				safe.WithTripID(order.TripID),
				safe.WithDriverID(order.Driver),
			)
			continue
		}
		toBeGeneratedOnTopScheme = append(toBeGeneratedOnTopScheme, eachFocusedOnTopScheme)
	}

	if len(toBeGeneratedOnTopScheme) == 0 {
		return ProviderResult[*domainModel.TransactionInfo]{
			Result: nil,
		}, nil
	}

	return ProviderResult[*domainModel.TransactionInfo]{
		Result: i.mergeOnTopTransaction(order, toBeGeneratedOnTopScheme),
	}, nil
}

type OnTopQuotaWithIndex struct {
	OnTopQuota domainModel.OnTopQuota
	Index      int
}

func (i *InstallmentOnTopTransactionProvider) validate(ctx context.Context, installmentOnTopQuota domainModel.OnTopQuota) error {
	// we check < 0 because there might be a case where new order deducted the quota
	// leaving the remaining quota to be 0 which might causes this validation to fail and not generate any transactions
	if installmentOnTopQuota.RemainingQuota < 0 {
		logx.Info().
			Context(ctx).
			Err(ErrNotEnoughRemainingQuota).
			Msg("InstallmentOnTopTransactionProvider - Validate: Driver have not enough remaining quota")
		return ErrNotEnoughRemainingQuota
	}
	return nil
}

func (i *InstallmentOnTopTransactionProvider) getInstallmentOnTopQuota(ctx context.Context, driverID string) (map[string]OnTopQuotaWithIndex, error) {
	result, err := i.driverRepository.GetMinimalProfilesByID(ctx, []string{driverID})
	if err != nil {
		logx.Error().
			Context(ctx).
			Err(err).
			Str(logutil.DriverIDKey, driverID).
			Msg("InstallmentOnTopTransactionProvider - Validate: Fail to get driver minimal profile by id")
		return nil, err
	}

	driverData, ok := result[driverID]
	if !ok || driverData == nil {
		logx.Error().
			Context(ctx).
			Err(ErrInvalidMinimalDriverData).
			Str(logutil.DriverIDKey, driverID).
			Msg("InstallmentOnTopTransactionProvider - Validate: Focused Driver Data is not found or nil")
		return nil, ErrInvalidMinimalDriverData
	}
	onTopQuotaByID := i.getInstallmentOnTopQuotaFromDriverData(driverData)
	if len(onTopQuotaByID) == 0 {
		logx.Error().
			Context(ctx).
			Err(ErrNoInstallmentOnTopQuotaData).
			Str(logutil.DriverIDKey, driverID).
			Msg("InstallmentOnTopTransactionProvider - Validate: No Installment On Top Quota in driver on top quota data")
		return nil, ErrNoInstallmentOnTopQuotaData
	}

	return onTopQuotaByID, nil
}

func (i *InstallmentOnTopTransactionProvider) getInstallmentOnTopQuotaFromDriverData(driverData *domainModel.DriverMinimal) map[string]OnTopQuotaWithIndex {
	result := make(map[string]OnTopQuotaWithIndex)
	for index, eachOnTopQuota := range driverData.OnTopQuotas {
		if eachOnTopQuota.QuotaType == domainModel.InstallmentOnTopQuotaType {
			result[eachOnTopQuota.OnTopFareID] = OnTopQuotaWithIndex{
				OnTopQuota: eachOnTopQuota,
				Index:      index,
			}
		}
	}
	return result
}

func (i *InstallmentOnTopTransactionProvider) mergeOnTopTransaction(order domainModel.Order, in []domainModel.OnTopScheme) *domainModel.TransactionInfo {
	result := domainModel.TransactionInfo{
		Category: domainModel.WalletTransactionCategory,
		Type:     domainModel.OnTopTransactionType,
		SubType:  domainModel.InstallmentOnTopSubType,
		DriverID: order.Driver,
		OrderID:  order.OrderID,
		TripID:   order.TripID,
	}

	amount := types.NewMoney(0)
	remark := ""
	transactionOnTopDetails := make([]domainModel.TransactionInfoOnTopDetail, 0)
	for _, eachOnTopScheme := range in {
		amount = amount.Add(types.NewMoney(eachOnTopScheme.Amount))
		remark = fmt.Sprintf("%sOn-Top: %s amount: %.2f THB\n", remark, domainModel.InstallmentOnTopScheme, eachOnTopScheme.Amount)
		transactionOnTopDetails = append(transactionOnTopDetails, domainModel.TransactionInfoOnTopDetail{
			Amount: types.NewMoney(eachOnTopScheme.Amount),
			Name:   "Installment On Top",
		})
	}
	result.Amount = amount
	result.Remark = remark
	result.OnTopDetails = transactionOnTopDetails
	return &result
}
