package featureflag

import (
	"github.com/Unleash/unleash-client-go/v3"
	"github.com/Unleash/unleash-client-go/v3/api"
	"github.com/Unleash/unleash-client-go/v3/context"

	"git.wndv.co/go/unleash/lmwnunleash"
)

var _ lmwnunleash.LMWNUnleasher = &DefaultLMWNUnleasher{}

type DefaultLMWNUnleasher struct {
}

func (s DefaultLMWNUnleasher) IsEnabled(feature string, options ...unleash.FeatureOption) bool {
	return true
}

func (s DefaultLMWNUnleasher) IsEnabledWithDefault(feature string, defaultValue bool, options ...unleash.FeatureOption) bool {
	return defaultValue
}

func (s DefaultLMWNUnleasher) GetVariant(feature string, options ...unleash.VariantOption) *api.Variant {
	return &api.Variant{}
}

func (s DefaultLMWNUnleasher) GetVariantPayload(feature string, options ...unleash.VariantOption) (isEnable bool, payloadType, payloadValue string) {
	return true, "", ""
}

func (s <PERSON>fault<PERSON><PERSON>NUnleasher) ListFeatures(features []string) []api.Feature {
	return []api.Feature{}
}

func (s DefaultLMWNUnleasher) IsEnabledWithContext(feature string, defaultValue bool, ctx context.Context, options ...unleash.FeatureOption) bool {
	return true
}

func (s DefaultLMWNUnleasher) GetVariantPayloadWithContext(feature string, ctx context.Context, options ...unleash.VariantOption) (isEnable bool, payloadType, payloadValue string) {
	return true, "", ""
}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideStubLMWNUnleasher() *DefaultLMWNUnleasher {
	return &DefaultLMWNUnleasher{}
}
