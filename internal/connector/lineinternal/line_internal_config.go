package lineinternal

import "github.com/kelseyhightower/envconfig"

type LINEInternalConfig struct {
	// Unacceptable ban within x days
	Host                        string `envconfig:"LINE_INTERNAL_HOST" default:"https://internal-api.line-apps-beta.com"`
	LINEChannelID               string `envconfig:"MID_MIGRATOR_LINE_CHANNEL_ID" default:""`
	LINEChannelSecret           string `envconfig:"MID_MIGRATOR_LINE_CHANNEL_SECRET" default:""`
	DecryptionLINEChannelID     string `envconfig:"DECRYPTION_LINE_CHANNEL_ID" default:""`
	DecryptionLINEChannelSecret string `envconfig:"DECRYPTION_LINE_CHANNEL_SECRET" default:""`
}

func ProvideLINEInternalConfig() (cfg LINEInternalConfig) {
	envconfig.MustProcess("", &cfg)
	return
}
