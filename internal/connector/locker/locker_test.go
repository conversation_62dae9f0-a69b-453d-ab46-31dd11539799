package locker_test

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore/mock_redis"
)

func TestRedisLocker(t *testing.T) {
	t.Parallel()

	stateName := "d:s"
	stateValue := "v"
	ttl := time.Duration(2)
	ctx := context.Background()

	t.Run("setstate", func(tt *testing.T) {
		tt.Parallel()

		underTest, mockRedisClient, finish := createRedisLocker(tt)
		defer finish()

		mockRedisClient.EXPECT().SetNX(ctx, "atomstate:"+stateName, stateValue, ttl).Return(redis.NewBoolResult(true, nil))

		require.Equal(tt, true, underTest.SetState(ctx, stateName, stateValue, ttl))
	})

	t.Run("setstate already existed", func(tt *testing.T) {
		tt.Parallel()

		underTest, mockRedisClient, finish := createRedisLocker(tt)
		defer finish()

		mockRedisClient.EXPECT().SetNX(ctx, "atomstate:"+stateName, stateValue, ttl).Return(redis.NewBoolResult(false, nil))

		require.Equal(tt, false, underTest.SetState(ctx, stateName, stateValue, ttl))
	})

	t.Run("getstate", func(tt *testing.T) {
		tt.Parallel()

		underTest, mockRedisClient, finish := createRedisLocker(tt)
		defer finish()

		v := "ddd"
		mockRedisClient.EXPECT().Get(ctx, "atomstate:"+stateName).Return(redis.NewStringResult(v, nil))

		require.Equal(tt, v, underTest.GetState(ctx, stateName))
	})

	t.Run("removestate", func(tt *testing.T) {
		tt.Parallel()

		underTest, mockRedisClient, finish := createRedisLocker(tt)
		defer finish()

		mockRedisClient.EXPECT().Del(ctx, "atomstate:"+stateName).Return(redis.NewIntResult(1, nil))

		underTest.RemoveState(ctx, stateName)
	})
}

func createRedisLocker(t *testing.T) (*locker.RedisLocker, *mock_redis.MockUniversalClient, func()) {
	ctrl := gomock.NewController(t)
	redisClient := mock_redis.NewMockUniversalClient(ctrl)

	return locker.ProvideRedisLocker(redisClient), redisClient, ctrl.Finish
}
