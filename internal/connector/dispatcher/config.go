package dispatcher

import (
	"time"

	"github.com/kelseyhightower/envconfig"
)

type DispatcherConfig struct {
	DispatcherDistributeOrderURL              string        `envconfig:"DISPATCHER_DISTRIBUTE_ORDER_URL"`
	DispatcherDistributeOrderV2URL            string        `envconfig:"DISPATCHER_DISTRIBUTE_ORDER_V2_URL"`
	DispatcherRedistributeOrderURL            string        `envconfig:"DISPATCHER_REDISTRIBUTE_ORDER_URL"`
	DispatcherDistributeOrdersInZoneURL       string        `envconfig:"DISPATCHER_DISTRIBUTE_ORDERS_IN_ZONE_URL"`
	DispatcherCheckCandidateEnoughURL         string        `envconfig:"DISPATCHER_CHECK_CANDIDATE_ENOUGH_URL"`
	DispatcherRequestTimeout                  time.Duration `envconfig:"DISPATCHER_REQUEST_TIMEOUT" default:"5s"`
	DispatcherRetryLimit                      uint          `envconfig:"DISPATCHER_RETRY_LIMIT" default:"3"`
	DispatcherRetryDelay                      time.Duration `envconfig:"DISPATCHER_RETRY_DELAY" default:"1s"`
	DispatcherFleetSingleDistributeOrderV2URL string        `envconfig:"DISPATCHER_FLEET_SINGLE_DISTRIBUTE_ORDER_V2_URL"`

	ForceDistributeWithV2   bool `envconfig:"DISPATCHER_FORCE_DISTRIBUTE_WITH_V2" default:"true"`
	ForceRedistributeWithV2 bool `envconfig:"DISPATCHER_FORCE_REDISTRIBUTE_WITH_V2" default:"true"`
}

func ProvideDispatcherConfig() (cfg DispatcherConfig) {
	envconfig.MustProcess("", &cfg)
	return
}
