package line

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/jarcoal/httpmock"
	"github.com/kelseyhightower/envconfig"
	"github.com/pkg/errors"

	"git.wndv.co/go/cnt"
	cnthttp "git.wndv.co/go/cnt/http"
	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
)

const (
	verifyEndpoint                           = "/oauth2/v2.1/verify"
	userProfileEndpoint                      = "/v2/profile"
	revokeEndpoint                           = "/oauth2/v2.1/revoke"
	issueStatelessChannelAccessTokenEndpoint = "/oauth2/v3/token"
)

// Error is error from LINE api.
type Error struct {
	Err              string `json:"error" validate:"required"`
	ErrorDescription string `json:"error_description" validate:"required"`
}

func (err Error) Error() string {
	return fmt.Sprintf("%s: %s", err.Err, err.ErrorDescription)
}

// Client for line social api.
type Client struct {
	endpoint Endpoint
	client   *httpclient.Client
}

type LINEHTTPClientConfig struct {
	cnthttp.CircuitBreakerConfig
	endpoint Endpoint
}

func ProvideLINEHTTPClientConfig(ep Endpoint) LINEHTTPClientConfig {
	var cfg LINEHTTPClientConfig
	envconfig.MustProcess("LINE_LOGIN", &cfg)
	cfg.endpoint = ep
	return cfg
}

// ProvideClient constructs *Client.
// @@wire-set-name@@ name:"Main"
// @@no-locator-generation@@
func ProvideClient(cfg LINEHTTPClientConfig) *Client {
	outgoingLog := cnthttp.DefaultOutgoingLogDecorator().
		LogBody(true).
		RedactHeaders("Authorization").
		RedactQueryStrings("access_token").
		AddProcessorByPath(cnthttp.ProcessorByPath{
			Path:           "/oauth2/v3/token",
			IsWriteLog:     true,
			IsWriteReqBody: false,
			IsWriteResBody: false,
		})

	httpClient := cnt.NewHTTPClient(
		cnthttp.CustomOutgoingLog(outgoingLog),
		cnthttp.WithCircuitBreaker(httpclient.FinalizeCircuitBreakerConfig("line_login", cfg.CircuitBreakerConfig)),
		cnthttp.WithDNSCache(),
	)

	return &Client{
		endpoint: cfg.endpoint,
		client: &httpclient.Client{
			HTTPClient: httpClient,
		},
	}
}

func (c *Client) buildEndpoint(path string) (*url.URL, error) {
	u, err := url.Parse(string(c.endpoint))
	if err != nil {
		return nil, err
	}
	u.Path = path
	return u, nil
}

// decoding error from response body. caller must close body manually.
func (c *Client) decodeErr(resp *http.Response) error {
	var err Error
	_ = safe.DecodeJSON(resp.Body, &err)
	return err
}

// VerifyResponse is response data for verify api.
type VerifyResponse struct {
	Scope     string `json:"scope"`
	ClientID  string `json:"client_id"`
	ExpiresIn int64  `json:"expires_in"`
}

// Verify access token.
func (c *Client) Verify(ctx context.Context, accessToken string) (*VerifyResponse, error) {
	if len(accessToken) == 0 {
		return nil, errors.New("access_token is empty")
	}
	u, err := c.buildEndpoint(verifyEndpoint)
	if err != nil {
		return nil, err
	}
	q := u.Query()
	q.Set("access_token", accessToken)
	u.RawQuery = q.Encode()
	resp, err := c.client.Get(ctx, u.String(), make(http.Header))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.decodeErr(resp)
	}
	var vresp VerifyResponse
	if err := safe.DecodeJSON(resp.Body, &vresp); err != nil {
		return nil, err
	}
	return &vresp, nil
}

type Profile struct {
	DisplayName   string `json:"displayName,omitempty"`
	UserID        string `json:"userId,omitempty"`
	PictureURL    string `json:"pictureUrl,omitempty"`
	StatusMessage string `json:"statusMessage,omitempty"`
}

// GetUserProfile get user information from access token.
func (c *Client) GetUserProfile(ctx context.Context, accessToken string) (*Profile, error) {
	u, err := c.buildEndpoint(userProfileEndpoint)
	if err != nil {
		return nil, err
	}
	h := make(http.Header)
	h.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))
	resp, err := c.client.Get(ctx, u.String(), h)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, c.decodeErr(resp)
	}
	var prof Profile
	if err := safe.DecodeJSON(resp.Body, &prof); err != nil {
		return nil, err
	}
	return &prof, nil
}

type RevokeRequest struct {
	ClientID     string
	ClientSecret string
	AccessToken  string
}

func (c *Client) Revoke(ctx context.Context, request RevokeRequest) error {
	if len(request.AccessToken) == 0 {
		return errors.New("access_token is empty")
	}
	u, err := c.buildEndpoint(revokeEndpoint)
	if err != nil {
		return err
	}

	data := url.Values{}
	data.Set("client_id", request.ClientID)
	data.Set("client_secret", request.ClientSecret)
	data.Set("access_token", request.AccessToken)

	r, err := http.NewRequest(http.MethodPost, u.String(), strings.NewReader(data.Encode()))
	if err != nil {
		return err
	}

	r.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	resp, err := c.client.Do(ctx, r)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return c.decodeErr(resp)
	}
	return nil
}

func (c *Client) IssueStatelessChannelAccessToken(ctx context.Context, channelID string, channelSecret string) (IssueStatelessChannelAccessTokenResponse, error) {
	genErrLog := func(srcLog *logx.LogEvent, srcCtx context.Context, srcErr error) *logx.LogEvent {
		return srcLog.Context(srcCtx).Err(srcErr).
			Str(logutil.Module, c.ModuleName()).
			Str(logutil.Method, "IssueStatelessChannelAccessToken")
	}

	var funcResp IssueStatelessChannelAccessTokenResponse

	u, err := c.buildEndpoint(issueStatelessChannelAccessTokenEndpoint)
	if err != nil {
		genErrLog(logx.Error(), ctx, err).
			Msgf("unable to build endpoint for sending a request")
		return funcResp, err
	}

	data := url.Values{}
	data.Set("grant_type", "client_credentials")
	data.Set("client_id", channelID)
	data.Set("client_secret", channelSecret)

	r, err := http.NewRequest(http.MethodPost, u.String(), strings.NewReader(data.Encode()))
	if err != nil {
		genErrLog(logx.Error(), ctx, err).
			Msgf("unable to create a new POST request")
		return funcResp, err
	}

	r.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	resp, err := c.client.Do(ctx, r)
	if err != nil {
		genErrLog(logx.Error(), ctx, err).
			Msgf("unable to send a POST request")
		return funcResp, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errResp IssueStatelessChannelAccessTokenErrorResponse
		if err := safe.DecodeJSON(resp.Body, &errResp); err != nil {
			genErrLog(logx.Error(), ctx, err).
				Msgf("unable to decode error response")
			return funcResp, err
		}

		compactErr := fmt.Errorf("%s: %s", errResp.Error, errResp.ErrorDescription)
		genErrLog(logx.Error(), ctx, compactErr).
			Str("error", errResp.Error).
			Str("error_description", errResp.ErrorDescription).
			Msg(compactErr.Error())
		return funcResp, compactErr
	}

	if err := safe.DecodeJSON(resp.Body, &funcResp); err != nil {
		genErrLog(logx.Error(), ctx, err).
			Msgf("unable to decode response")
		return funcResp, err
	}

	return funcResp, nil
}

func (c *Client) ModuleName() string {
	return "LINEPublicClient"
}

// @@wire-set-name@@ name:"IntegrationTest"
// @@no-locator-generation@@
func ProvideClientStub(cfg LINEHTTPClientConfig) *Client {
	httpClient := &http.Client{}
	httpmock.ActivateNonDefault(httpClient)

	return &Client{
		endpoint: cfg.endpoint,
		client: &httpclient.Client{
			HTTPClient: httpClient,
		},
	}
}
