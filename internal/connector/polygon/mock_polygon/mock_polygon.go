// Code generated by MockGen. DO NOT EDIT.
// Source: polygon.go

// Package mock_polygon is a generated GoMock package.
package mock_polygon

import (
	context "context"
	reflect "reflect"

	polygon "git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	gomock "github.com/golang/mock/gomock"
)

// MockPolygon is a mock of Polygon interface.
type MockPolygon struct {
	ctrl     *gomock.Controller
	recorder *MockPolygonMockRecorder
}

// MockPolygonMockRecorder is the mock recorder for MockPolygon.
type MockPolygonMockRecorder struct {
	mock *MockPolygon
}

// NewMockPolygon creates a new mock instance.
func NewMockPolygon(ctrl *gomock.Controller) *MockPolygon {
	mock := &MockPolygon{ctrl: ctrl}
	mock.recorder = &MockPolygonMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPolygon) EXPECT() *MockPolygonMockRecorder {
	return m.recorder
}

// GetRawRegion mocks base method.
func (m *MockPolygon) GetRawRegion(ctx context.Context, regionCode string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRawRegion", ctx, regionCode)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRawRegion indicates an expected call of GetRawRegion.
func (mr *MockPolygonMockRecorder) GetRawRegion(ctx, regionCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRawRegion", reflect.TypeOf((*MockPolygon)(nil).GetRawRegion), ctx, regionCode)
}

// GetRegion mocks base method.
func (m *MockPolygon) GetRegion(ctx context.Context, regionCode string) (polygon.GetRawRegionRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRegion", ctx, regionCode)
	ret0, _ := ret[0].(polygon.GetRawRegionRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRegion indicates an expected call of GetRegion.
func (mr *MockPolygonMockRecorder) GetRegion(ctx, regionCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRegion", reflect.TypeOf((*MockPolygon)(nil).GetRegion), ctx, regionCode)
}

// GetRegionByLocation mocks base method.
func (m *MockPolygon) GetRegionByLocation(ctx context.Context, location polygon.Location) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRegionByLocation", ctx, location)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRegionByLocation indicates an expected call of GetRegionByLocation.
func (mr *MockPolygonMockRecorder) GetRegionByLocation(ctx, location interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRegionByLocation", reflect.TypeOf((*MockPolygon)(nil).GetRegionByLocation), ctx, location)
}

// ListRegionByService mocks base method.
func (m *MockPolygon) ListRegionByService(ctx context.Context, service string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRegionByService", ctx, service)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRegionByService indicates an expected call of ListRegionByService.
func (mr *MockPolygonMockRecorder) ListRegionByService(ctx, service interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRegionByService", reflect.TypeOf((*MockPolygon)(nil).ListRegionByService), ctx, service)
}
