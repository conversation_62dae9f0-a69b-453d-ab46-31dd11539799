package httptransport

import (
	"net/http"
	"net/http/httputil"
	"time"

	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/absinthe/httpclient"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

type RequestResponseDumpTransport struct {
	base   http.RoundTripper
	logger httpclient.Logger
}

func NewRequestResponseDumpTransport(base http.RoundTripper) *RequestResponseDumpTransport {
	opt := httpclient.WithHTTPCallDumpLogger(logrus.New())
	o := httpclient.Options{}
	opt(&o)
	return &RequestResponseDumpTransport{
		base:   base,
		logger: o.Logger,
	}
}

func (t RequestResponseDumpTransport) RoundTrip(r *http.Request) (*http.Response, error) {
	startTime := timeutils.Now()
	reqb, _ := httputil.DumpRequestOut(r, true)

	resp, err := t.base.RoundTrip(r)
	if err != nil {
		return resp, err
	}

	respb, _ := httputil.DumpResponse(resp, true)

	t.logger.Log(&httpclient.Entry{
		Latency:  time.Since(startTime),
		Response: readResponse(respb, reqb),
	})

	return resp, nil
}
