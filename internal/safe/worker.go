package safe

import (
	"errors"
	"fmt"

	"github.com/panjf2000/ants/v2"
	"github.com/sirupsen/logrus"
)

type Worker struct {
	pool *ants.PoolWithFunc
}

func process(payload interface{}) {
	p := payload.(func())
	p()
}

func NewWorker(poolSize int) (*Worker, func()) {
	p, _ := ants.NewPoolWithFunc(poolSize, process, ants.WithPanicHandler(func(i interface{}) {
		logrus.Error("Recovering from panic: ", i)
		alertErr(errors.New(fmt.Sprintf("error from panic %v", i)))
	}))

	wk := &Worker{pool: p}

	return wk, func() {
		wk.pool.Release()
	}
}

func (w *Worker) GoFuncWithPool(fn func()) {
	err := w.pool.Invoke(fn)
	if err != nil {
		logrus.Errorf("Invoke GoFuncWithPool error: %s", err)
		return
	}
}

func (w *Worker) GoFuncWithPoolErr(fn func()) error {
	err := w.pool.Invoke(fn)
	if err != nil {
		return err
	}
	return nil
}

func (w *Worker) Free() int {
	return w.pool.Free()
}

func (w *Worker) Running() int {
	return w.pool.Running()
}

func (w *Worker) Cap() int {
	return w.pool.Cap()
}
