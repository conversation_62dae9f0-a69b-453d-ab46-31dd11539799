package config

import "github.com/kelseyhightower/envconfig"

type FormConfig struct {
	IsEnableRiderCompensationTimeAndDistanceLogicFeature bool `envconfig:"IS_ENABLE_RIDER_COMPENSATION_TIME_AND_DISTANCE_LOGIC_FEATURE" default:"false"`

	// The number of days within which the rider can claim after order created
	FormClaimQRPaymentAllowToClaimWithinDay int `envconfig:"FORM_CLAIM_QR_PAYMENT_ALLOW_TO_CLAIM_WITHIN_DAY" default:"7"`
}

func ProvideFormConfig() (cfg FormConfig) {
	envconfig.MustProcess("", &cfg)
	return
}
