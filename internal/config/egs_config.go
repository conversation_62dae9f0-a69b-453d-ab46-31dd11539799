package config

import (
	"github.com/kelseyhightower/envconfig"
)

type EGSConfig struct {
	// Unacceptable ban within x days
	FormEGSUnacceptableBanWithin int `envconfig:"FORM_EGS_UNACCEPTABLE_BAN_WITHIN" default:"7"`

	// Minimum x completed order each day
	FormEGSMinimunCompletedOrderEachDay int `envconfig:"FORM_EGS_MIN_COMPLETED_ORDER_EACH_DAY" default:"1"`

	// The number of days that the order completed
	FormEGSCompletedOrderDayAmount int `envconfig:"FORM_EGS_AMOUNT_OF_COMPLETED_ORDER_DAY" default:"4"`

	// Range to count the completed order
	FormEGSCompletedOrderWithin int `envconfig:"FORM_EGS_COMPLETED_ORDER_WITHIN" default:"7"`

	// Maximum Days Past Due (DPD) allowed for each installment
	FormEGSMaximumDPDAllowed int `envconfig:"FORM_EGS_MAX_DPD_ALLOW" default:"4"`

	// the longest month duration that we allow to compare for auto approval
	FormInsuranceAutoApproveMonthDuration int  `envconfig:"FORM_INSURANCE_AUTO_APPROVE_MONTH_DURATION" default:"3"`
	EnableEgsOms                          bool `envconfig:"ENABLE_EGS_OMS" default:"false"`

	// Using to validate max exposure and max tenor that was set by admin
	IsEGSFinancialRiskValidatorEnabled bool `envconfig:"IS_EGS_FINANCIAL_RISK_VALIDATOR_ENABLED" default:"false"`

	// time when installment scheduler deduct money
	EgsInstallmentCutOffTime string `envconfig:"EGS_INSTALLMENT_CUTOFF_TIME" default:"20:30"`
}

func ProvideEGSConfig() (cfg EGSConfig) {
	envconfig.MustProcess("", &cfg)

	return
}

// EGSDBConfig
type EGSDBConfig struct {
	EgsPositiveCreditDelayDeductionDay int `envconfig:"EGS_POSITIVE_CREDIT_DELAY_DEDUCTION_DAY" default:"5"`
}

func ProvideAtomicEGSDBConfig(configUpdater *DBConfigUpdater) *AtomicEGSDBConfig {
	var cfg AtomicEGSDBConfig
	configUpdater.Register(&cfg)
	return &cfg
}

type AtomicEGSDBConfig = AtomicWrapper[EGSDBConfig]

func NewAtomicEGSDBConfig(cfg EGSDBConfig) *AtomicEGSDBConfig {
	return NewAtomicWrapper(cfg)
}
