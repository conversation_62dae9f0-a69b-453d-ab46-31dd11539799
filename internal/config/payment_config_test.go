package config

import (
	"math"
	"os"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestProvidePaymentConfig(t *testing.T) {
	var testcases = []struct {
		a      string
		result int
	}{
		{"-1", math.MaxInt8},
		{"", math.MaxInt8},
		{"1", 1},
	}

	for _, tc := range testcases {
		if tc.a != "" {
			os.Setenv("PAYMENT_WALLET_WITHDRAW_MAXIMUM_REQUEST", tc.a)
		} else {
			os.Unsetenv("PAYMENT_WALLET_WITHDRAW_MAXIMUM_REQUEST")
		}

		config := ProvidePaymentConfig()
		require.Equal(t, tc.result, config.WalletWithdrawMaximumReq)
	}
}
