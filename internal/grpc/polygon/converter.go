package polygon

import (
	"fmt"

	"github.com/twpayne/go-geom"

	polygonPb "git.wndv.co/go/proto/lineman/polygon/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

func ConvertToPoint(poi *polygonPb.POI, pointId string) *model.Point {
	if poi == nil || poi.IsPoi == false {
		return nil
	}

	var point *polygonPb.Point
	for _, poiPoint := range poi.Points {
		if poiPoint.PointId == pointId {
			point = poiPoint
			break
		}
	}

	if point == nil {
		safe.SentryErrorMessage(fmt.Sprintf("unable to find point %v in poi %v", pointId, poi.PoiId))
		return nil
	}

	var location model.Location
	if point.Location != nil {
		location = model.Location{
			Lat: point.Location.Lat,
			Lng: point.Location.Lng,
		}
	}

	var driverSteps []model.DriverStep
	for _, driverStep := range point.DriverSteps {
		if driverStep != nil {
			driverSteps = append(driverSteps, model.DriverStep{
				Text:     driverStep.Text,
				ImageURL: driverStep.ImageUrl,
			})
		}
	}

	var geometry model.PointOfInterestV2Geometry
	if poi.GetGeometry() != nil {
		geometry.Type = poi.GetGeometry().Type
		for _, subPolygon := range poi.GetGeometry().GetSubPolygons() {
			subPolygonCoordinates := []geom.Coord{}
			for _, coordinate := range subPolygon.GetCoordinates() {
				subPolygonCoordinates = append(subPolygonCoordinates, geom.Coord{coordinate.Lat, coordinate.Lng})
			}
			geometry.Coordinates = append(geometry.Coordinates, subPolygonCoordinates)
		}
	}

	return &model.Point{
		ID:                 point.PointId,
		Name:               point.Name,
		Location:           location,
		HasParkingZone:     point.HasParkingZone,
		Remark:             point.Remark,
		DriverSteps:        driverSteps,
		DriverViewImageURL: point.DriverViewImageUrl,
		GooglePlaceID:      point.GooglePlaceId,
		Poi: model.PointOfInterestV2{
			ID:       poi.PoiId,
			Name:     poi.Name,
			Address:  poi.Address,
			Geometry: geometry,
		},
	}
}
