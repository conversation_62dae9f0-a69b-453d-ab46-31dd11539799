package inventoryservice

import (
	"context"

	"google.golang.org/grpc"

	inventoryPb "git.wndv.co/go/proto/lineman/inventory/v1"
)

var _ inventoryPb.ProductServiceClient = &StubGRPCProductService{}

type StubGRPCProductService struct {
	testcaseToPPGResponse map[string]*inventoryPb.GetProductWithPriorityGroupResponse
}

type ContextKey string

const (
	TestContextValue ContextKey = "PRODUCT_GRPC_TESTCONTEXTVAL"
)

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideStubGRPCProductService(cfg Config) *StubGRPCProductService {
	return &StubGRPCProductService{
		testcaseToPPGResponse: make(map[string]*inventoryPb.GetProductWithPriorityGroupResponse),
	}
}

func (ps *StubGRPCProductService) SetGetProductWithPriorityGroupResponse(testcase string, src *inventoryPb.GetProductWithPriorityGroupResponse) {
	ps.testcaseToPPGResponse[testcase] = src
}

func (ps *StubGRPCProductService) GetProduct(ctx context.Context, in *inventoryPb.GetProductRequest, opts ...grpc.CallOption) (*inventoryPb.GetProductResponse, error) {
	return nil, nil
}

func (ps *StubGRPCProductService) ListProduct(ctx context.Context, in *inventoryPb.ListProductRequest, opts ...grpc.CallOption) (*inventoryPb.ListProductResponse, error) {
	return nil, nil
}

func (ps *StubGRPCProductService) CheckoutProduct(ctx context.Context, in *inventoryPb.CheckoutProductRequest, opts ...grpc.CallOption) (*inventoryPb.CheckoutProductResponse, error) {
	return nil, nil
}

func (ps *StubGRPCProductService) UpdateProductQuantity(ctx context.Context, in *inventoryPb.UpdateProductQuantityRequest, opts ...grpc.CallOption) (*inventoryPb.UpdateProductQuantityResponse, error) {
	return nil, nil
}

func (ps *StubGRPCProductService) UpdateProduct(ctx context.Context, in *inventoryPb.UpdateProductRequest, opts ...grpc.CallOption) (*inventoryPb.UpdateProductResponse, error) {
	return nil, nil
}

func (ps *StubGRPCProductService) GetAllDriverAvailableProduct(ctx context.Context, in *inventoryPb.GetAllDriverAvailableProductRequest, opts ...grpc.CallOption) (*inventoryPb.GetAllDriverAvailableProductResponse, error) {
	return nil, nil
}

func (ps *StubGRPCProductService) GetProductWithPriorityGroup(ctx context.Context, in *inventoryPb.GetProductWithPriorityGroupRequest, opts ...grpc.CallOption) (*inventoryPb.GetProductWithPriorityGroupResponse, error) {
	var targetCase string
	if str, ok := ctx.Value(TestContextValue).(string); ok {
		targetCase = str
	}
	if res, existed := ps.testcaseToPPGResponse[targetCase]; existed {
		return res, nil
	}
	return nil, nil
}

func (ps *StubGRPCProductService) ReturnProduct(ctx context.Context, in *inventoryPb.ReturnProductRequest, opts ...grpc.CallOption) (*inventoryPb.ReturnProductResponse, error) {
	return nil, nil
}

func (ps *StubGRPCProductService) GetProductBySKU(ctx context.Context, in *inventoryPb.GetProductBySKURequest, opts ...grpc.CallOption) (*inventoryPb.GetProductBySKUResponse, error) {
	return nil, nil
}

func (ps *StubGRPCProductService) GetProductPurchaseLimitBySKU(ctx context.Context, in *inventoryPb.GetProductPurchaseLimitBySKURequest, opts ...grpc.CallOption) (*inventoryPb.GetProductPurchaseLimitBySKUResponse, error) {
	return nil, nil
}
