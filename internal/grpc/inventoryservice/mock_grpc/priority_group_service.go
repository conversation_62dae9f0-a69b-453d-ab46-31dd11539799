// Code generated by MockGen. DO NOT EDIT.
// Source: git.wndv.co/go/proto/lineman/inventory/v1 (interfaces: PriorityGroupServiceClient)

// Package mock_grpc is a generated GoMock package.
package mock_grpc

import (
	context "context"
	reflect "reflect"

	inventoryv1 "git.wndv.co/go/proto/lineman/inventory/v1"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPriorityGroupServiceClient is a mock of PriorityGroupServiceClient interface.
type MockPriorityGroupServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockPriorityGroupServiceClientMockRecorder
}

// MockPriorityGroupServiceClientMockRecorder is the mock recorder for MockPriorityGroupServiceClient.
type MockPriorityGroupServiceClientMockRecorder struct {
	mock *MockPriorityGroupServiceClient
}

// NewMockPriorityGroupServiceClient creates a new mock instance.
func NewMockPriorityGroupServiceClient(ctrl *gomock.Controller) *MockPriorityGroupServiceClient {
	mock := &MockPriorityGroupServiceClient{ctrl: ctrl}
	mock.recorder = &MockPriorityGroupServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPriorityGroupServiceClient) EXPECT() *MockPriorityGroupServiceClientMockRecorder {
	return m.recorder
}

// CreatePriorityGroup mocks base method.
func (m *MockPriorityGroupServiceClient) CreatePriorityGroup(arg0 context.Context, arg1 *inventoryv1.CreatePriorityGroupRequest, arg2 ...grpc.CallOption) (*inventoryv1.CreatePriorityGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePriorityGroup", varargs...)
	ret0, _ := ret[0].(*inventoryv1.CreatePriorityGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePriorityGroup indicates an expected call of CreatePriorityGroup.
func (mr *MockPriorityGroupServiceClientMockRecorder) CreatePriorityGroup(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePriorityGroup", reflect.TypeOf((*MockPriorityGroupServiceClient)(nil).CreatePriorityGroup), varargs...)
}

// GetPriorityGroup mocks base method.
func (m *MockPriorityGroupServiceClient) GetPriorityGroup(arg0 context.Context, arg1 *inventoryv1.GetPriorityGroupRequest, arg2 ...grpc.CallOption) (*inventoryv1.GetPriorityGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPriorityGroup", varargs...)
	ret0, _ := ret[0].(*inventoryv1.GetPriorityGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPriorityGroup indicates an expected call of GetPriorityGroup.
func (mr *MockPriorityGroupServiceClientMockRecorder) GetPriorityGroup(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPriorityGroup", reflect.TypeOf((*MockPriorityGroupServiceClient)(nil).GetPriorityGroup), varargs...)
}

// ListPriorityGroups mocks base method.
func (m *MockPriorityGroupServiceClient) ListPriorityGroups(arg0 context.Context, arg1 *inventoryv1.ListPriorityGroupsRequest, arg2 ...grpc.CallOption) (*inventoryv1.ListPriorityGroupsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListPriorityGroups", varargs...)
	ret0, _ := ret[0].(*inventoryv1.ListPriorityGroupsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPriorityGroups indicates an expected call of ListPriorityGroups.
func (mr *MockPriorityGroupServiceClientMockRecorder) ListPriorityGroups(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPriorityGroups", reflect.TypeOf((*MockPriorityGroupServiceClient)(nil).ListPriorityGroups), varargs...)
}

// UpdatePriorityGroup mocks base method.
func (m *MockPriorityGroupServiceClient) UpdatePriorityGroup(arg0 context.Context, arg1 *inventoryv1.UpdatePriorityGroupRequest, arg2 ...grpc.CallOption) (*inventoryv1.UpdatePriorityGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePriorityGroup", varargs...)
	ret0, _ := ret[0].(*inventoryv1.UpdatePriorityGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePriorityGroup indicates an expected call of UpdatePriorityGroup.
func (mr *MockPriorityGroupServiceClientMockRecorder) UpdatePriorityGroup(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePriorityGroup", reflect.TypeOf((*MockPriorityGroupServiceClient)(nil).UpdatePriorityGroup), varargs...)
}
