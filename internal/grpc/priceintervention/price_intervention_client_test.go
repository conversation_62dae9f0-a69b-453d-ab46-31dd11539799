package priceintervention_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	price_interventionv1 "git.wndv.co/go/proto/lineman/price_intervention/v1"
	transportationV1 "git.wndv.co/go/proto/lineman/transportation_price_intervention/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/priceintervention"
)

func TestDisabledPriceInterventionClient(t *testing.T) {
	cfg := priceintervention.Config{
		IsEnabled: false,
		Endpoint:  "",
	}
	client, close := priceintervention.ProvidePriceInterventionClient(cfg)

	ctx := context.TODO()
	resp, err := client.GetDeliveryFeeOntopByRegion(ctx, &price_interventionv1.GetDeliveryFeeOntopByRegionRequest{})
	require.Nil(t, resp)
	require.Nil(t, err)
	close()
}

func Test_DisabledPriceInterventionPIPClient(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	cfg := priceintervention.Config{
		IsTransportationPIPEnabled: false,
		Endpoint:                   "",
	}
	client, close := priceintervention.ProvidePriceInterventionClient(cfg)
	defer close()
	ctx := context.TODO()
	resp, err := client.GetEstimatedUserFareOnTopRequest(ctx, &transportationV1.GetEstimatedUserFareOntopRequest{})
	require.Nil(t, resp)
	require.Nil(t, err)
}
