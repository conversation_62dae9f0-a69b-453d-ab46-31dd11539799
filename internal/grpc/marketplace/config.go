package marketplace

import (
	"github.com/kelseyhightower/envconfig"
)

type Config struct {
	Endpoint  string `envconfig:"EGS_ENDPOINT"  default:""`
	ClientID  string `envconfig:"EGS_CLIENT_ID" default:"LM-DRIVER"`
	KeyID     string `envconfig:"EGS_KEY_ID" default:""`
	SecretKey string `envconfig:"EGS_SECRET_KEY" default:""`
}

func ProvideConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}
