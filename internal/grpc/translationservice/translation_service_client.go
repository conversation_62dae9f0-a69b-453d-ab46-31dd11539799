package translation

import (
	"context"

	"google.golang.org/grpc"

	grpcTranslation "git.wndv.co/go/proto/lineman/translation/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type MonitoredTranslationServiceClient struct {
	grpcTranslation.TranslationServiceClient
	successCounter metric.Counter
	errorCounter   metric.Counter
}

func NewTranslationServiceClient(client grpcTranslation.TranslationServiceClient, meter metric.Meter) *MonitoredTranslationServiceClient {
	errorCounter := meter.GetCounter("lm_driver_translation_error_total", "number of translation service client call success", "method")
	successCounter := meter.GetCounter("lm_driver_translation_success_total", "number of translation service client call error", "method")

	return &MonitoredTranslationServiceClient{
		TranslationServiceClient: client,
		successCounter:           successCounter,
		errorCounter:             errorCounter,
	}
}

func (m *MonitoredTranslationServiceClient) Translate(ctx context.Context, in *grpcTranslation.TranslateRequest, opts ...grpc.CallOption) (*grpcTranslation.TranslateResponse, error) {
	resp, err := m.TranslationServiceClient.Translate(ctx, in, opts...)
	if err != nil {
		m.errorCounter.Inc("Translate")
	} else {
		m.successCounter.Inc("Translate")
	}
	return resp, err
}
