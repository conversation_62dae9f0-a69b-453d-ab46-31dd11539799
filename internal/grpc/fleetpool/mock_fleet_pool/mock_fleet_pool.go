// Code generated by MockGen. DO NOT EDIT.
// Source: git.wndv.co/go/proto/lineman/fleet/pool/v1 (interfaces: RiderSearchServiceClient)

// Package mock_fleet_pool is a generated GoMock package.
package mock_fleet_pool

import (
	context "context"
	reflect "reflect"

	poolv1 "git.wndv.co/go/proto/lineman/fleet/pool/v1"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRiderSearchServiceClient is a mock of RiderSearchServiceClient interface.
type MockRiderSearchServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockRiderSearchServiceClientMockRecorder
}

// MockRiderSearchServiceClientMockRecorder is the mock recorder for MockRiderSearchServiceClient.
type MockRiderSearchServiceClientMockRecorder struct {
	mock *MockRiderSearchServiceClient
}

// NewMockRiderSearchServiceClient creates a new mock instance.
func NewMockRiderSearchServiceClient(ctrl *gomock.Controller) *MockRiderSearchServiceClient {
	mock := &MockRiderSearchServiceClient{ctrl: ctrl}
	mock.recorder = &MockRiderSearchServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiderSearchServiceClient) EXPECT() *MockRiderSearchServiceClientMockRecorder {
	return m.recorder
}

// GetRidersInMultiPolygon mocks base method.
func (m *MockRiderSearchServiceClient) GetRidersInMultiPolygon(arg0 context.Context, arg1 *poolv1.GetRidersInMultiPolygonRequest, arg2 ...grpc.CallOption) (*poolv1.GetRidersInMultiPolygonResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRidersInMultiPolygon", varargs...)
	ret0, _ := ret[0].(*poolv1.GetRidersInMultiPolygonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRidersInMultiPolygon indicates an expected call of GetRidersInMultiPolygon.
func (mr *MockRiderSearchServiceClientMockRecorder) GetRidersInMultiPolygon(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRidersInMultiPolygon", reflect.TypeOf((*MockRiderSearchServiceClient)(nil).GetRidersInMultiPolygon), varargs...)
}

// GetRidersInRadius mocks base method.
func (m *MockRiderSearchServiceClient) GetRidersInRadius(arg0 context.Context, arg1 *poolv1.GetRidersInRadiusRequest, arg2 ...grpc.CallOption) (*poolv1.GetRidersInRadiusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRidersInRadius", varargs...)
	ret0, _ := ret[0].(*poolv1.GetRidersInRadiusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRidersInRadius indicates an expected call of GetRidersInRadius.
func (mr *MockRiderSearchServiceClientMockRecorder) GetRidersInRadius(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRidersInRadius", reflect.TypeOf((*MockRiderSearchServiceClient)(nil).GetRidersInRadius), varargs...)
}
