package handler

import (
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/serviceprovider"
)

const (
	Food      ServiceType = "food"
	Messenger ServiceType = "messenger"
	Mart      ServiceType = "mart"
)

type ServiceType string

func (s ServiceType) ToService() model.Service {
	return model.Service(s)
}

type ServiceProviderHandler struct {
	Selector *serviceprovider.ServiceSelector
	registry *serviceprovider.ProviderRegistry
}

func (ps *ServiceProviderHandler) GetProvider(svc ServiceType) serviceprovider.ServiceProvider {
	return ps.registry.Get(string(svc))
}

func (ps *ServiceProviderHandler) RegisterProvider(svc ServiceType, sp serviceprovider.ServiceProvider) {
	ps.registry.Register(string(svc), func() serviceprovider.ServiceProvider { return sp })
}
