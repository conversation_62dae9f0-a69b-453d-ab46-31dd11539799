package event

import (
	"encoding/json"

	"github.com/IBM/sarama"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type HeatMapObserver struct {
	payloads []model.OrderEventPayload
}

func (o *HeatMapObserver) pushMessages(messages []sarama.ConsumerMessage) {
	for _, message := range messages {
		var model DriverEventOrderModel
		err := json.Unmarshal(message.Value, &model)
		if err != nil {
			logrus.Error("Skip this message because the message is invalid type:", err)
			continue
		}

		if EventOrderCreate == EventMessageType(model.Event) {
			o.update(model)
		}
	}
}

func (o *HeatMapObserver) GetPayload() []model.OrderEventPayload {
	return o.payloads
}

func (o *HeatMapObserver) update(deo DriverEventOrderModel) {
	var oc model.OrderEventPayload
	err := json.Unmarshal(deo.Payload, &oc)
	if err != nil {
		logrus.Warn("Could not unmarshal payload:", err)
	}
	o.payloads = append(o.payloads, oc)
}
