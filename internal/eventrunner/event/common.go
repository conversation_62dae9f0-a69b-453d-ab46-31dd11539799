package event

import (
	"encoding/json"
	"time"

	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type EventMessageType string

const (
	EventOrderCancel       EventMessageType = "ORDER_CANCEL"
	EventOrderCreate       EventMessageType = "ORDER_CREATE"
	EventOrderComplete     EventMessageType = "ORDER_COMPLETE"
	EventOrderAccept       EventMessageType = "ORDER_ACCEPT"
	EventOrderAssign       EventMessageType = "ORDER_ASSIGN"
	EventOrderUnAssign     EventMessageType = "ORDER_UNASSIGN"
	EventStatsReset        EventMessageType = "COUNTS_RESET"
	EventVelocityOverLimit EventMessageType = "VELOCITY_OVER_LIMIT"

	// EventCountsDelete is used by internal API to delete daily counts or monthly counts
	EventCountsDelete EventMessageType = "COUNTS_DELETE"
	// EventCountsSet is used by internal API to set daily counts or monthly counts
	EventCountsSet EventMessageType = "COUNTS_SET"
)

type DriverEventOrderModel struct {
	Event     string          `json:"event"`
	Payload   json.RawMessage `json:"payload"`
	EventTime time.Time       `json:"event_time"`
}

type CancelOrderPayload struct {
	OrderPayload
	IsCancellationFree bool `json:"is_cancellation_free,omitempty"`
}

type OrderPayload struct {
	OrderID  string `json:"order_id,omitempty"`
	DriverID string `json:"driver_id,omitempty"`

	// Location source location
	Location Location `json:"location,omitempty"`

	DestinationLocation Location  `json:"destination_location,omitempty"`
	DriverLocation      Location  `json:"driver_location,omitempty"`
	CreatedAt           time.Time `json:"created_at,omitempty"`
	Region              string    `json:"region,omitempty"`
	ServiceType         string    `json:"service_type,omitempty"`
	SwitchFlow          bool      `json:"switch_flow,omitempty"`
	IsAutoAssign        bool      `json:"is_auto_assign,omitempty"`
	DeliveringRound     int       `json:"delivering_round,omitempty"`
	HasRained           bool      `json:"has_rained,omitempty"`
}

type CountsDeleteOrderPayload struct {
	DriverID            string `json:"driver_id,omitempty"`
	DeleteDailyCounts   bool   `json:"delete_daily_counts,omitempty"`
	DeleteMonthlyCounts bool   `json:"delete_monthly_counts,omitempty"`
}

type VelocityOverLimitPayload struct {
	DriverID  string    `json:"driver_id,omitempty"`
	CreatedAt time.Time `json:"created_at,omitempty"`
}

type CountsSetOrderPayload struct {
	DriverID      string               `json:"driver_id,omitempty"`
	DailyCounts   []model.DailyCount   `json:"daily_counts,omitempty"`
	MonthlyCounts []model.MonthlyCount `json:"monthly_counts,omitempty"`
}

type StatsResetEventPayload struct {
	DriverID                 string        `json:"driver_id,omitempty"`
	Date                     time.Time     `json:"date,omitempty"`
	Service                  model.Service `json:"service,omitempty"`
	AutoAssignedAccepted     *int          `json:"auto_assigned_accepted,omitempty"`
	AutoAssigned             *int          `json:"auto_assigned,omitempty"`
	CancelledNotFree         *int          `json:"cancelled_not_free,omitempty"`
	Accepted                 *int          `json:"accepted,omitempty"`
	RequestedBy              string        `json:"requested_by,omitempty"`
	AutoAssignedRain         *int          `json:"auto_assigned_rain,omitempty"`
	AutoAssignedAcceptedRain *int          `json:"auto_assigned_accepted_rain,omitempty"`
}

type DriverStatisticConfig struct {
	DriverCleanUpDailyCount            int           `envconfig:"DRIVER_CLEAN_UP_DAILY_COUNT" default:"62"`
	BackwardCompatibilityDeduplication bool          `envconfig:"BACKWARD_COMPATIBILITY_DEDUPLICATION" default:"true"`
	EventDeduplicateDuration           time.Duration `envconfig:"EVENT_DEDUPLICATE_DURATION" default:"30m"`
	IncentiveProgressBackFillDays      int           `envconfig:"INCENTIVE_PROGRESS_BACK_FILL_DAYS" default:"7"`
}

func ProvideDriverStatisticConfig() (cfg DriverStatisticConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type AtomicDriverStatisticConfig = config.AtomicWrapper[DriverStatisticConfig]

func NewAtomicDriverStatisticConfig(cfg DriverStatisticConfig) *AtomicDriverStatisticConfig {
	return config.NewAtomicWrapper(cfg)
}

func ProvideAtomicDriverStatisticConfig(configUpdater *config.DBConfigUpdater) *AtomicDriverStatisticConfig {
	var cfg AtomicDriverStatisticConfig
	configUpdater.Register(&cfg)
	return &cfg
}
