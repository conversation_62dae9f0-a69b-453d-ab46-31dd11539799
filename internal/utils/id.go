package utils

import (
	"math/rand"
	"strings"

	"github.com/google/uuid"
)

var _ = uuid.Must(uuid.NewRandom())

func GenerateUUID() string {
	id, _ := uuid.NewRandom()
	return strings.ReplaceAll(id.String(), "-", "")
}

const letterBytes = "1234567890"
const (
	letterIdxBits = 4                    // 6 bits to represent a letter index
	letterIdxMask = 1<<letterIdxBits - 1 // All 1-bits, as many as letterIdxBits
	letterIdxMax  = 63 / letterIdxBits   // # of letter indices fitting in 63 bits
)

func GenerateLetterNumber(size int32) string {
	b := make([]byte, size)
	// A rand.Int63() generates 63 random bits, enough for letterIdxMax letters!
	for i, cache, remain := size-1, rand.Int63(), letterIdxMax; i >= 0; {
		if remain == 0 {
			cache, remain = rand.Int63(), letterIdxMax
		}

		// first char should be "0"
		maxIdx := len(letterBytes)
		if i == 0 {
			maxIdx = maxIdx - 1
		}

		if idx := int(cache & letterIdxMask); idx < maxIdx {
			b[i] = letterBytes[idx]
			i--
		}
		cache >>= letterIdxBits
		remain--
	}

	return string(b)
}
