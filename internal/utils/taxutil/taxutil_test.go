package taxutil_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/taxutil"
)

func TestCalculateTax(t *testing.T) {
	type args struct {
		initialAmt float64
		taxRate    float64
	}
	tests := []struct {
		name      string
		args      args
		wantProd  float64
		wantVAT   float64
		wantTotal float64
	}{
		{
			name: "calculates tax correctly with positive initial amount and tax rate",
			args: args{
				initialAmt: 100.0,
				taxRate:    7.00,
			},
			wantProd:  93.46,
			wantVAT:   6.54,
			wantTotal: 100.0,
		},
		{
			name: "calculates tax correctly with zero initial amount and tax rate",
			args: args{
				initialAmt: 0.0,
				taxRate:    7.00,
			},
			wantProd:  0.0,
			wantVAT:   0.0,
			wantTotal: 0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotProd, gotVAT, gotTotal := taxutil.CalculateTax(tt.args.initialAmt, tt.args.taxRate)
			require.Equal(t, tt.wantProd, gotProd)
			require.Equal(t, tt.wantVAT, gotVAT)
			require.Equal(t, tt.wantTotal, gotTotal)
		})
	}
}
