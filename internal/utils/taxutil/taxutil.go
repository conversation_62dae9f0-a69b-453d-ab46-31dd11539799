package taxutil

import (
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

// CalculateTax calculates the product amount, VAT amount, and total amount
// based on the initial amount and the tax rate.
func CalculateTax(initialAmt, taxRate float64) (productAmt, vatAmt, totalAmt float64) {
	initialAmtMoney := types.NewMoney(initialAmt)
	taxRateMoney := types.NewMoney(taxRate)

	productAmtMoney := initialAmtMoney.Sub(initialAmtMoney.Mul(taxRateMoney).Div(taxRateMoney.Add(types.NewMoney(100))))
	vatAmtMoney := initialAmtMoney.Sub(productAmtMoney)

	return productAmtMoney.Float64(), vatAmtMoney.Float64(), initialAmtMoney.Float64()
}
