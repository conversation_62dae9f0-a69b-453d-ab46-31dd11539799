package logutil

// Domain Log Key
const (
	DriverID      = "driver_id"
	DriverIDs     = "driver_ids"
	OrderID       = "order_id"
	QuoteID       = "quote_id"
	BundleOrderID = "bundle_order_id"
	OrderIDs      = "order_ids"
	TripID        = "trip_id"
	AssignmentID  = "assignment_id"
	ZoneCode      = "zone_code"
	Tag           = "tag"
	TransactionID = "transaction_id"
	Type          = "type"
	Service       = "service"
	Region        = "region"
	Subtype       = "subtype"
	FormType      = "form_type"
	FormSubType   = "form_subtype"
	Sku           = "sku"
	EGSOrderID    = "egs_order_id"
	InstallmentID = "installment_id"
	OnTopScheme   = "on_top_scheme"
	ServiceType   = "service_type"
)

// Technical Log Key
const (
	SentryErrorCode = "sentry_error_code"
	Method          = "method"
	Event           = "event"
	Feature         = "feature"
	Module          = "module"
)
const (
	DriverIDKey = "driverID"
)

const (
	TagQRBike = "qr_bike"
)
