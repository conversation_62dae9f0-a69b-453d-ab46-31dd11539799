package income

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func getOrderIdsFromTransactions(ts []model.Transaction) []string {
	s := types.NewStringSet()
	for _, v := range ts {
		orderIds := getCompletedOrderIDsFromTransaction(v)
		s.Add(orderIds...)
	}

	return s.GetElements()
}

func getCompletedOrderIDsFromTransaction(t model.Transaction) []string {
	s := types.NewStringSet()
	for _, o := range t.Info.Orders {
		if o.OrderID != "" && o.Status == model.StatusCompleted {
			s.Add(o.OrderID)
		}
	}

	if t.Info.OrderID != "" {
		s.Add(t.Info.OrderID)
	}

	return s.GetElements()
}

type orders []model.Order

func (o orders) toMap() map[string]model.Order {
	m := make(map[string]model.Order, len(o))
	for _, v := range o {
		m[v.OrderID] = v
	}
	return m
}

func firstDateOfWeek(now time.Time) time.Time {
	offset := int(time.Monday - now.Weekday())
	if offset > 0 {
		offset = -6
	}

	return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).AddDate(0, 0, offset)
}

func firstDateOfMonth(now time.Time) time.Time {
	return timeutil.DateTruncate(timeutil.GetFirstDayOfMonthFromTime(now))
}

func DateBeginningAndEnd(granularity Granularity, date time.Time) (time.Time, time.Time, time.Time) {
	switch granularity {
	case DAILY:
		from := timeutil.DateTruncate(date)
		to := timeutil.DateCeiling(from)
		toExclusive := from.AddDate(0, 0, 1)
		return from, to, toExclusive
	case WEEKLY:
		from := firstDateOfWeek(date)
		to := timeutil.DateCeiling(from.AddDate(0, 0, 6))
		toExclusive := from.AddDate(0, 0, 7)
		return from, to, toExclusive
	case MONTHLY:
		from := firstDateOfMonth(date)
		to := timeutil.DateCeiling(from.AddDate(0, 1, -1))
		toExclusive := from.AddDate(0, 1, 0)
		return from, to, toExclusive
	}

	return date, date, date
}

func chooseStrategy(ctx context.Context, today time.Time, from time.Time, to time.Time) QueryStrategy {
	if today.After(from) && today.Before(to) {
		if from.Equal(timeutil.DateTruncate(today)) && to.Equal(timeutil.DateCeiling(today)) {
			return QueryOnlyTodayRealTime
		}
		return QueryBothTodayRealTimeAndAggregated
	}
	return QueryOnlyAggregated
}
