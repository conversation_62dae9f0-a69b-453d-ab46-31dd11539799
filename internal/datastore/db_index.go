package datastore

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	drivermigrator "git.wndv.co/lineman/mongo-migrator/pkg/driver"
)

func EnsureIndexForTest(conn *mongodb.Conn, collection ...string) error {
	opts := bson.D{}

	collections := types.NewStringSet(collection...)

	for col, indexes := range drivermigrator.GetAllIndexes() {
		if collections.Count() == 0 || collections.Has(col) {
			if _, err := conn.Database().Collection(col).Indexes().CreateMany(context.Background(), indexes); err != nil {
				panic(err)
			}
		} else {
			opts = append(opts, bson.E{Key: "create", Value: col})
		}
	}

	conn.Database().RunCommand(context.Background(), opts)
	return nil
}
