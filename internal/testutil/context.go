package testutil

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"net/textproto"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/icrowley/fake"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/auth/token"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

const TestHttpRecorderKey = "test_http_recorder"

type GinContextWithRecorder struct {
	gctx             *gin.Context
	ResponseRecorder *httptest.ResponseRecorder
	body             io.ReadCloser
}

func (c *GinContextWithRecorder) SetBody(b io.Reader) {
	if rc, ok := b.(io.ReadCloser); ok {
		c.body = rc
	} else {
		c.body = io.NopCloser(b)
	}

	c.gctx.Request.Body = c.body
}

func (c *GinContextWithRecorder) SetQuery(rawQuery string) {
	c.gctx.Request.URL.RawQuery = rawQuery
}

func (c *GinContextWithRecorder) Send(eng interface {
	ServeHTTP(w http.ResponseWriter, req *http.Request)
}) *GinContextWithRecorder {
	eng.ServeHTTP(c.ResponseRecorder, c.gctx.Request)
	return c
}

func (c *GinContextWithRecorder) GinCtx() *gin.Context {
	return c.gctx
}

func (c *GinContextWithRecorder) SetGET(path string, args ...interface{}) *GinContextWithRecorder {
	return c.SetRequestWithMethod(http.MethodGet, path, args...)
}

func (c *GinContextWithRecorder) SetPOST(path string, args ...interface{}) *GinContextWithRecorder {
	return c.SetRequestWithMethod(http.MethodPost, path, args...)
}

func (c *GinContextWithRecorder) SetPUT(path string, args ...interface{}) *GinContextWithRecorder {
	return c.SetRequestWithMethod(http.MethodPut, path, args...)
}

func (c *GinContextWithRecorder) SetPATCH(path string, args ...interface{}) *GinContextWithRecorder {
	return c.SetRequestWithMethod(http.MethodPatch, path, args...)
}

func (c *GinContextWithRecorder) SetDELETE(path string, args ...interface{}) *GinContextWithRecorder {
	return c.SetRequestWithMethod(http.MethodDelete, path, args...)
}

func (c *GinContextWithRecorder) SetGinParams(params gin.Params) *GinContextWithRecorder {
	c.GinCtx().Params = params
	return c
}

// Authorized registered request, must call after SetPOST, SetPUT, SetGET method.
func (c *GinContextWithRecorder) Authorized(tokenStore auth.TokenStorage, driverId string) *GinContextWithRecorder {
	generate, duration, err := token.Generate(driverId, token.TokenKindAccessToken)
	if err != nil {
		panic(err)
	}
	err = tokenStore.Store(context.Background(), auth.TokenProps{
		AccessToken: generate,
		DriverID:    driverId,
		Expire:      duration,
	})
	if err != nil {
		panic(err)
	}
	c.gctx.Request.Header.Set("X-DRIVER-ID", driverId)
	c.gctx.Request.Header.Set("Authorization", fmt.Sprintf("%s %s", "Bearer", generate))

	return c
}

func (c *GinContextWithRecorder) AdminAuthorized(email string, permissions ...string) *GinContextWithRecorder {
	claims := jwt.MapClaims{
		"email": email,
		"id":    email,
		"iat":   timeutils.Now().Unix(),
		"iss":   "SYSTEM",
		"exp":   timeutils.Now().Add(time.Hour * 24),
	}
	if permissions != nil {
		claims["permissions"] = strings.Join(permissions, ",")
	}
	tok := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	tokenString, _ := tok.SignedString([]byte(""))
	c.gctx.Request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", tokenString))

	return c
}

func (c *GinContextWithRecorder) FakeAdminCtxAuthorized(id, token, email string, permission ...types.StringSet) *GinContextWithRecorder {
	adminUser := auth.NewAdminUser(id, token, email, types.NewStringSet())
	auth.SetAdminUserToGctx(c.gctx, adminUser)

	return c
}

func (c *GinContextWithRecorder) SetRequestWithMethod(method string, path string, args ...interface{}) *GinContextWithRecorder {
	uri := fmt.Sprintf(path, args...)
	req, err := http.NewRequest(method, uri, c.body)
	if err != nil {
		panic(err)
	}
	c.gctx.Request = req
	c.SetRiderAppVersion(999, 999, 99)
	return c
}

func (c *GinContextWithRecorder) SetRiderAppVersion(major, minor, patch int) *GinContextWithRecorder {
	ua := fmt.Sprintf("LINEMANRIDER/%d.%d.%d (Android; Integration test)", major, minor, patch)
	c.gctx.Request.Header.Set("user-agent", ua)
	return c
}

func (c *GinContextWithRecorder) AssertResponseCode(t testing.TB, expectedCode int) *GinContextWithRecorder {
	recorder := c.ResponseRecorder
	assert.Equal(t, expectedCode, recorder.Code, "invalid response. body=%v", recorder.Body.String())
	if recorder.Code <= 299 {
		assert.Empty(t, c.gctx.Errors)
	}
	return c
}

func (c *GinContextWithRecorder) DecodeJSONResponse(v interface{}) {
	body := c.ResponseRecorder.Result().Body
	defer body.Close()
	err := json.NewDecoder(body).Decode(v)
	if err != nil {
		panic(err)
	}
}

func (c *GinContextWithRecorder) SetHeader(key, val string) *GinContextWithRecorder {
	c.gctx.Request.Header.Set(key, val)
	return c
}

func (c *GinContextWithRecorder) Body() *BodyBuilder {
	return &BodyBuilder{
		ctx: c,
	}
}

type BodyBuilder struct {
	ctx *GinContextWithRecorder

	contentType string
	jsonBody    interface{}
	buf         *bytes.Buffer
}

func (b *BodyBuilder) Build() *GinContextWithRecorder {
	reader := b.buildReader()
	b.ctx.SetBody(reader)
	b.ctx.gctx.Request.Header.Set("Content-Type", b.contentType)
	return b.ctx
}

func (b *BodyBuilder) buildReader() io.Reader {
	if b.contentType == "" {
		panic("invalid body builder state")
	}
	if b.jsonBody != nil {
		return JSON(b.jsonBody)
	} else if b.buf != nil {
		return b.buf
	} else {
		return &bytes.Buffer{}
	}
}

func (b *BodyBuilder) JSON(data interface{}) *BodyBuilder {
	b.reset()
	b.contentType = gin.MIMEJSON
	b.jsonBody = data
	return b
}

func (b *BodyBuilder) MultipartForm() *MultipartBuilder {
	b.reset()
	b.buf = &bytes.Buffer{}
	multipartWriter := multipart.NewWriter(b.buf)
	b.contentType = multipartWriter.FormDataContentType()
	return &MultipartBuilder{b: b, writer: multipartWriter}
}

func (b *BodyBuilder) reset() {
	b.jsonBody = nil
	b.contentType = ""
	b.buf = nil
}

type MultipartBuilder struct {
	b      *BodyBuilder
	writer *multipart.Writer
}

func (m *MultipartBuilder) JSON(name string, data interface{}) *MultipartBuilder {
	h := make(textproto.MIMEHeader)
	h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, name))
	h.Set("Content-Type", gin.MIMEJSON)
	part, err := m.writer.CreatePart(h)
	if err != nil {
		panic(err)
	}

	_, err = io.Copy(part, JSON(data))
	if err != nil {
		panic(err)
	}
	return m
}

func (b *BodyBuilder) RawJSON(str string) *BodyBuilder {
	return b.JSON(json.RawMessage(str))
}

func (m *MultipartBuilder) FakeImage(fieldName string, fileName string) *MultipartBuilder {
	return m.File(fieldName, fileName, fake.CharactersN(10))
}

func (m *MultipartBuilder) ImageDataIfNotZero(fieldName string, data interface{}) *MultipartBuilder {
	if data == nil {
		return m
	}
	if reflect.ValueOf(data).IsZero() {
		return m
	}
	return m.File(fieldName, fake.CharactersN(10), data)
}

func (m *MultipartBuilder) ImageData(fieldName string, data interface{}) *MultipartBuilder {
	return m.File(fieldName, fake.CharactersN(10), data)
}

func (m *MultipartBuilder) File(fieldName string, fileName string, data interface{}) *MultipartBuilder {
	f, err := m.writer.CreateFormFile(fieldName, fileName)
	if err != nil {
		panic(err)
	}

	if data == nil {
		data = ""
	}

	switch v := data.(type) {
	case []byte:
		_, err = io.Copy(f, bytes.NewReader(v))
	case string:
		_, err = io.Copy(f, bytes.NewReader([]byte(v)))
	case io.Reader:
		_, err = io.Copy(f, v)
	default:
		panic(fmt.Sprintf("Unsupport image data. %t %v", v, v))
	}
	if err != nil {
		panic(err)
	}
	return m
}

func (m *MultipartBuilder) String(fieldName string, data string) *MultipartBuilder {
	field, err := m.writer.CreateFormField(fieldName)
	if err != nil {
		panic(err)
	}
	_, err = field.Write([]byte(data))
	if err != nil {
		panic(err)
	}
	return m
}

func (m *MultipartBuilder) Build() *GinContextWithRecorder {
	if err := m.writer.Close(); err != nil {
		panic(err)
	}
	return m.b.Build()
}

// TestRequestContext create a new gin.Context and recorder uses for test.
func TestRequestContext(method, url string, body io.Reader) (*gin.Context, *httptest.ResponseRecorder) {
	r, _ := http.NewRequest(method, url, body)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	ctx.Request = r
	ctx.Set(TestHttpRecorderKey, w)
	return ctx, w
}

func NewContextWithRecorder() *GinContextWithRecorder {
	r, _ := http.NewRequest(http.MethodGet, "/", nil)
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	ctx.Request = r

	return &GinContextWithRecorder{
		gctx:             ctx,
		ResponseRecorder: w,
	}
}

// JSON create json payload reader from v.
func JSON(v interface{}) io.Reader {
	var buf bytes.Buffer
	if err := json.NewEncoder(&buf).Encode(v); err != nil {
		panic(fmt.Errorf("JSON encode error: %v", err))
	}
	return &buf
}

// DecodeJSON is utility for decoding json into v.
func DecodeJSON(t *testing.T, r io.Reader, v interface{}) {
	err := json.NewDecoder(r).Decode(v)
	require.NoError(t, err)
}

func MultipartFileHeader(filename string, content []byte) *multipart.FileHeader {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)

	fw, _ := w.CreateFormFile("file", filename)
	n, err := io.Copy(fw, bytes.NewReader(content))
	if err != nil {
		panic(err)
	}
	if n != int64(len(content)) {
		panic(errors.New("written size is not match"))
	}

	err = w.Close()
	if err != nil {
		panic(err)
	}

	r := multipart.NewReader(&b, w.Boundary())
	form, _ := r.ReadForm(int64(len(content)) + (1024 * 1024))
	file := form.File["file"][0]

	return file
}
