[{"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-B2B001", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 7.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-B2B001", "trip_id": "TRIP-B2B001", "status": "DRIVER_TO_DESTINATION", "driver": "B2B-001", "history": {"DRIVER_TO_DESTINATION": {"$date": "{{isodate_now}}"}, "DRIVER_ARRIVED_RESTAURANT": {"$date": "{{isodate_now}}"}, "DRIVER_TO_RESTAURANT": {"$date": "{{isodate_now}}"}, "DRIVER_MATCHED": {"$date": "{{isodate_now}}"}, "ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-B2B002", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "rest1", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-B2B002", "status": "ASSIGNING_DRIVER", "driver": "", "history": {"ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-B2B003", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "rest2", "name": "", "address": "", "phones": [""], "location": {"lat": 14.34, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-B2B003", "trip_id": "TRIP-B2B003", "status": "DRIVER_MATCHED", "driver": "B2B-001", "history": {"DRIVER_MATCHED": {"$date": "{{isodate_now}}"}, "ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-B2B004", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-B2B004", "status": "ASSIGNING_DRIVER", "driver": "", "history": {"ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-B2B005", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-B2B005", "status": "DRIVER_ARRIVED_RESTAURANT", "driver": "B2B-003", "history": {"DRIVER_ARRIVED_RESTAURANT": {"$date": "{{isodate_now}}"}, "DRIVER_TO_RESTAURANT": {"$date": "{{isodate_now}}"}, "DRIVER_MATCHED": {"$date": "{{isodate_now}}"}, "ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-B2B006", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-B2B006", "status": "ASSIGNING_DRIVER", "driver": "", "history": {"ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-MOB2B01", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "rest1", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "user1", "name": "", "address": "", "memo": "memo-LMF-MO01", "memo_th": "ข้อความไทย", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-MO01", "status": "DRIVER_MATCH", "driver": "DRIV-MO-001", "history": {"DRIVER_MATCHED": {"$date": "{{isodate_now}}"}, "ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-MOB2B02", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "rest1", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "user2", "name": "", "address": "", "memo": "memo-LMF-MO02", "memo_th": "ข้อความไทย", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-MO02", "status": "ASSIGNING_DRIVER", "driver": "", "history": {"ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-MOB2B02", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "rest1", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "user2", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-MO03", "status": "DRIVER_ARRIVED_RESTAURANT", "driver": "DRIV-MO-002", "history": {"DRIVER_ARRIVED_RESTAURANT": {"$date": "{{isodate_now}}"}, "DRIVER_TO_RESTAURANT": {"$date": "{{isodate_now}}"}, "DRIVER_MATCHED": {"$date": "{{isodate_now}}"}, "ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-MOB2B04", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "rest2", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "user2", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-MO04", "trip_id": "LMRT-04", "status": "RESTAURANT_ACCEPTED", "driver": "DRIV-MO-010", "history": {"RESTAURANT_ACCEPTED": {"$date": "{{isodate_now}}"}, "DRIVER_MATCHED": {"$date": "{{isodate_now}}"}, "ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-MOB2B04", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "rest2", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "user2", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-MO05", "trip_id": "LMRT-05", "status": "DRIVER_TO_DESTINATION", "driver": "DRIV-MO-001", "history": {"DRIVER_TO_DESTINATION": {"$date": "{{isodate_now}}"}, "DRIVER_ARRIVED_RESTAURANT": {"$date": "{{isodate_now}}"}, "DRIVER_TO_RESTAURANT": {"$date": "{{isodate_now}}"}, "RESTAURANT_ACCEPTED": {"$date": "{{isodate_now}}"}, "DRIVER_MATCHED": {"$date": "{{isodate_now}}"}, "ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-MOB2B02", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "rest1", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "user2", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-MO-SWITCH-FLOW-01", "status": "ASSIGNING_DRIVER", "driver": "", "history": {"ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}, "options": {"switch_flow": true}, "autostart": true}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-MOB2B02", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "rest2", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "user2", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-MO-S3-SWITCH-FLOW-01", "status": "ASSIGNING_DRIVER", "driver": "", "history": {"ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}, "options": {"switch_flow": true}, "autostart": true}, {"_id": {"$oid": "{{rand_objid}}"}, "quote_id": "LMFQ-MOB2B02", "user_id": "U1", "service_type": "food", "region": "CHIANG_MAI_B2B", "routes": [{"id": "rest2", "name": "", "address": "", "phones": [""], "location": {"lat": 14.343028, "lng": 100.560946}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 0.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "", "discount": 0.0, "max_deduction": 0.0}, "sub_total": 0.0, "total": 0.0}, "order_price": 0.0, "total": 0.0}, "info": {"service_type": "food", "estimated_cooking_time": 0, "restaurant_type": "RMS", "price_scheme": "FOOD_PURCHASE"}, "estimated_delivery_time": 0, "distance": 0.0}, {"id": "user2", "name": "", "address": "", "phones": ["0895959595"], "location": {"lat": 14.3532128, "lng": 100.5689599}, "memo": "", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": 0.0, "price_summary": {"delivery_fee": {"base_fee": 40.0, "road_fee": 0.0, "additional_fee": {}, "coupon": {"code": "GPR020", "discount": 20.0, "max_deduction": 20.0}, "sub_total": 40.0, "total": 20.0}, "order_price": 0.0, "total": 20.0}, "info": {"estimated_cooking_time": 0, "restaurant_type": ""}, "estimated_delivery_time": 424, "distance": 1689.7}], "payment_method": "CASH", "distance": 1689.7, "created_at": {"$date": "{{isodate_now}}"}, "updated_at": {"$date": "{{isodate_now}}"}, "order_id": "LMF-MO-REASSIGNED-01", "status": "ASSIGNING_DRIVER", "driver": "", "history": {"ASSIGNING_DRIVER": {"$date": "{{isodate_now}}"}}, "expire_at": {"$date": "{{`30m` | isodate_fromnow}}"}, "rating_score": 0, "distribution": "AUTO_ASSIGN", "distribution_regions": ["CHIANG_MAI_B2B"], "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": ""}, "captcha": {"choices": [{"id": 0}, {"id": 1}, {"id": 2}], "correct_id": 1}, "options": {}, "delivering_round": 1}]