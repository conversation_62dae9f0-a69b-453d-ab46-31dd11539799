package notification

import (
	"net/http"

	"git.wndv.co/lineman/fleet-distribution/internal/httpheader"
)

type NotificationHeaderTransport struct {
	base http.RoundTripper
}

func NewNotificationHeaderTransport(base http.RoundTripper) *NotificationHeaderTransport {
	return &NotificationHeaderTransport{
		base: base,
	}
}

func (t NotificationHeaderTransport) RoundTrip(r *http.Request) (*http.Response, error) {
	httpheader.AddNotificationHttpHeader(r.Context(), r.Header)
	return t.base.RoundTrip(r)
}
