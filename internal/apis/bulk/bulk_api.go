package bulk

import (
	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulk/bulk_incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/bulk"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type API struct {
	bulkGinHandlerFn bulk.GinHandlersFn
}

func ProvideBulkAPI(fn bulk.GinHandlersFn) *API {
	return &API{
		bulkGinHandlerFn: fn,
	}
}

func (api *API) BulkUpdateServiceTypesSilentBanned(gctx *gin.Context) {
	handler := api.bulkGinHandlerFn(string(model.BulkProcessDriverServiceTypeSilentBanned))
	handler(gctx)
}

func (api *API) BulkToggleOnRiderServicePreference(gctx *gin.Context) {
	handler := api.bulkGinHandlerFn(string(model.BulkProcessToggleOnRiderServicePreference))
	handler(gctx)
}

func (api *API) BulkUpdateIncentive(gctx *gin.Context) {
	handler := api.bulkGinHandlerFn(string(model.BulkIncentiveUpdate))
	handler(gctx)
}

func (api *API) BulkCreateIncentive(gctx *gin.Context) {
	handler := api.bulkGinHandlerFn(string(model.BulkIncentiveCreate))
	handler(gctx)
}

func (api *API) BulkDeleteIncentive(gctx *gin.Context) {
	handler := api.bulkGinHandlerFn(string(model.BulkIncentiveDelete))
	handler(gctx)
}

func (api *API) BulkExportIncentive(gctx *gin.Context) {
	handler := api.bulkGinHandlerFn(string(model.BulkIncentiveExport))
	handler(gctx)
}

func ProvideBulkOperations(bulkUpdateServiceTypeSilentBanned *BulkUpdateServiceTypeSilentBanned, bulkToggleOnRiderServicePreference *BulkToggleOnRiderServicePreference,
	bulkUpdateIncentive *bulk_incentive.UpdateBulkIncentive, bulkCreateIncentive *bulk_incentive.CreateBulkIncentive, bulkDeleteIncentive *bulk_incentive.DeleteBulkIncentive, bulkExportIncentive *bulk_incentive.ExportBulkIncentive) (*bulk.Operations, error) {
	return bulk.NewOperations([]bulk.SingleCollectionOperation{
		bulkUpdateServiceTypeSilentBanned,
		bulkToggleOnRiderServicePreference,
		bulkUpdateIncentive,
		bulkCreateIncentive,
		bulkDeleteIncentive,
		bulkExportIncentive,
	})
}
