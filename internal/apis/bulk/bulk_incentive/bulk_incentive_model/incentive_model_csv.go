package bulk_incentive_model

import (
	"context"
	"encoding/json"
	"fmt"
	"math"

	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/absinthe/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type IncentiveModelCSV struct {
	Id                       string                    `csv:"id"`
	Name                     string                    `csv:"name,omitempty"`
	Description              string                    `csv:"description"`
	Region                   string                    `csv:"region,omitempty"`
	Active                   BooleanColumn             `csv:"active"`
	DisplayName              string                    `csv:"display_name"`
	IncentiveSources         IncentiveSourcesColumn    `csv:"incentive_sources"`
	ServiceTypes             ServiceTypesColumn        `csv:"service_types,omitempty"`
	PaymentType              PaymentTypeColumn         `csv:"payment_type,omitempty"`
	OrderType                OrderTypeColumn           `csv:"order_type,omitempty"`
	IncentiveOrderTiers      IncentiveOrderTiersColumn `csv:"incentive_order_tiers,omitempty"`
	AcceptanceRate           Float64NullableColumn     `csv:"acceptance_rate"`
	CancellationRate         Float64NullableColumn     `csv:"cancellation_rate"`
	Rating                   Float64NullableColumn     `csv:"rating"`
	HaveBox                  BooleanNullableColumn     `csv:"have_box"`
	HaveJacket               BooleanNullableColumn     `csv:"have_jacket"`
	WhitelistIds             WhitelistColumn           `csv:"whitelist_ids"`
	Tiers                    DriverTierColumn          `csv:"tiers"`
	DateRange                DateRangeColumn           `csv:"date_range,omitempty"`
	TimeRanges               TimeRangeColumn           `csv:"time_ranges,omitempty"`
	LocationName             string                    `csv:"location_name,omitempty"`
	EffectiveAreaType        EffectiveAreaTypeColumn   `csv:"effective_area.type"`
	EffectiveAreaZone        string                    `csv:"effective_area.zone"`
	EffectiveAreaCoordinates CoordinatesColumn         `csv:"effective_area.coordinates"`
	Streaks                  StreakIncentiveColumn     `csv:"streak,omitempty"`
}

func (m *IncentiveModelCSV) toRequest(polygonService polygon.Polygon) incentive.Request {
	return incentive.Request{
		Name:           m.Name,
		DisplayName:    m.DisplayName,
		LocationName:   m.LocationName,
		Description:    m.Description,
		Region:         m.Region,
		PaymentType:    m.PaymentType.PaymentType,
		ServiceTypes:   m.ServiceTypes.Services,
		Coordinates:    m.EffectiveAreaCoordinates.Coordinates,
		OrderTier:      toOrderTierReqs(m.IncentiveOrderTiers.OrderTiers),
		DateRange:      incentive.DateRangeReq(m.DateRange.DateRange),
		Times:          toTimesReq(m.TimeRanges.TimeRanges),
		AR:             m.AcceptanceRate.Value,
		CR:             m.CancellationRate.Value,
		Active:         m.Active.Active,
		Email:          "",
		Sources:        m.IncentiveSources.IncentiveSources,
		Rating:         m.Rating.Value,
		Tiers:          m.Tiers.Tiers,
		Box:            m.HaveBox.Active,
		Jacket:         m.HaveJacket.Active,
		WhitelistIDs:   m.WhitelistIds.WhiteLists,
		OrderShiftType: string(m.OrderType.OrderType),
		ZoneCode:       m.EffectiveAreaZone,
		Streak:         m.Streaks.Streak,
	}
}

func (m *IncentiveModelCSV) ToMap(polygonService polygon.Polygon) (map[string]interface{}, error) {
	if m.EffectiveAreaType.EffectiveAreaType == EFFECTIVE_AREA_REGION {
		err := m.ValidateCoordinateEmpty(context.Background(), polygonService)
		if err != nil {
			return nil, err
		}
	}
	req := m.toRequest(polygonService)
	geo, err := req.GeometryToModel()
	if err != nil {
		return nil, err
	}
	inc := &incentive.Incentive{
		IncentiveID:  m.Id,
		Name:         req.Name,
		DisplayName:  req.DisplayName,
		LocationName: req.LocationName,
		Description:  req.Description,
		Region:       model.RegionCode(req.Region),
		PaymentType:  req.PaymentType,
		ServiceTypes: req.ServiceTypes,
		DateRange: incentive.DateRange{
			Start: req.DateRange.Start,
			End:   req.DateRange.End,
		},
		Times:          req.TimesToModel(),
		OrderTier:      req.OrderTierToModel(),
		Active:         req.Active,
		Sources:        req.Sources,
		Tiers:          req.Tiers,
		WhitelistIDs:   req.WhitelistIDs,
		BlacklistIDs:   req.BlacklistIDs,
		OrderShiftType: incentive.OrderShiftType(req.OrderShiftType),
		AR:             req.AR,
		CR:             req.CR,
		Rating:         req.Rating,
		Box:            req.Box,
		Jacket:         req.Jacket,
		ZoneCode:       req.ZoneCode,
	}

	if err := req.Streak.Validate(); err == nil {
		inc.Streak = req.Streak
	}

	inc.Geometry = geo

	incBytes, err := bson.Marshal(inc)
	if err != nil {
		return nil, err
	}

	var incMap map[string]interface{}
	err = bson.Unmarshal(incBytes, &incMap)
	if err != nil {
		return nil, err
	}

	return incMap, nil
}

func (m *IncentiveModelCSV) ValidateCoordinateEmpty(ctx context.Context, polygonService polygon.Polygon) error {
	if m.EffectiveAreaCoordinates.Coordinates == nil && m.EffectiveAreaZone == "" {
		rawRegion, err := polygonService.GetRawRegion(ctx, m.Region)
		if err != nil {
			return err
		}

		var region polygon.GetRawRegionRes
		err = json.Unmarshal(rawRegion, &region)
		if err != nil {
			return err
		}
		m.EffectiveAreaCoordinates.Coordinates = incentive.Coordinates{region.Geometry.Coordinates}
	}
	return nil
}

func (m *IncentiveModelCSV) Validate(ctx context.Context, zoneService service.ZoneService) ([]string, bool) {
	reasons := []string{}
	if m.Id == "" {
		reasons = append(reasons, "id is required")
	}

	if m.Name == "" {
		reasons = append(reasons, "name is required")
	}

	if m.Region == "" {
		reasons = append(reasons, "region is required")
	}

	if len(m.ServiceTypes.Services) == 0 {
		reasons = append(reasons, "service_types is required")
	}

	if m.PaymentType.PaymentType == "" {
		reasons = append(reasons, "payment_type is required")
	}

	if m.OrderType.OrderType == "" {
		reasons = append(reasons, "order_type is required")
	}

	if m.DateRange.DateRange.Start.IsZero() || m.DateRange.DateRange.End.IsZero() {
		reasons = append(reasons, "date_range is required")
	}

	if len(m.TimeRanges.TimeRanges) == 0 {
		reasons = append(reasons, "time_range is required")
	}

	if len(m.IncentiveOrderTiers.OrderTiers) == 0 {
		reasons = append(reasons, "incentive_order_tiers is required")
	}

	if len(m.LocationName) == 0 {
		reasons = append(reasons, "location_name is required")
	}

	if m.PaymentType.PaymentType == incentive.PeriodStreak {
		if err := m.Streaks.Streak.Validate(); err != nil {
			reasons = append(reasons, err.Error())
		}
		if m.IncentiveOrderTiers.OrderTiers != nil && (len(m.IncentiveOrderTiers.OrderTiers) != 1 || !m.IncentiveOrderTiers.OrderTiers[0].IsEmpty()) {
			reasons = append(reasons, "incentive_order_tiers is not allowed when payment_type is STREAK, please put 0|0|0.00")
		}

		if float64(m.Streaks.Streak.NoOfDays*24) > math.Round(m.DateRange.DateRange.End.Sub(m.DateRange.DateRange.Start).Hours()) {
			reasons = append(reasons, "date_range is not allowed to be more than streak no_of_days")
		}
	} else {
		if err := m.Streaks.Streak.Validate(); err == nil {
			reasons = append(reasons, "streak is not allowed when payment_type is not STREAK")
		}
	}

	switch m.EffectiveAreaType.EffectiveAreaType {
	case EFFECTIVE_AREA_COORDINATES:
		coordinates := m.EffectiveAreaCoordinates
		if len(coordinates.Coordinates) == 0 {
			reasons = append(reasons, "effective_area.coordinates is required when effective_area.type is COORDINATES")
		}
	case EFFECTIVE_AREA_ZONE:
		zoneCode := m.EffectiveAreaZone
		if zoneCode == "" {
			reasons = append(reasons, "effective_area.zone is required when effective_area.type is ZONE")
		} else {
			if zoneCode, region := m.EffectiveAreaZone, m.Region; zoneCode != "" {
				req := service.FindBriefZoneRequest{
					Region: &region,
					Active: types.NewBool(true),
				}
				resp, err := zoneService.FindBriefZones(ctx, req)
				if err != nil {
					reason := fmt.Sprintf("get zone list err: %v", err)
					reasons = append(reasons, reason)
				}
				zones := make([]string, 0, resp.TotalCount)
				for _, zone := range resp.Zones {
					zones = append(zones, zone.ZoneCode)
				}
				if !utils.StrContains(zoneCode, zones) {
					reason := fmt.Sprintf("effective_area.zone value %v is not valid", zoneCode)
					reasons = append(reasons, reason)
				}
			}
		}
	}

	return reasons, len(reasons) == 0
}

func toTimesReq(times []incentive.Times) []incentive.TimesReq {
	var rr []incentive.TimesReq
	for _, v := range times {
		rr = append(rr, incentive.TimesReq(v))
	}
	return rr
}

func toOrderTierReqs(orderTier []incentive.OrderTier) []incentive.OrderTierReq {
	var rr []incentive.OrderTierReq
	for _, v := range orderTier {
		rr = append(rr, incentive.OrderTierReq(v))
	}
	return rr
}
