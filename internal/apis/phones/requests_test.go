package phones

import (
	"bytes"
	"io"
	"mime/multipart"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestParsePhoneFromCSV(t *testing.T) {
	t.Run("behaves as expected", func(tt *testing.T) {
		content := "0800000001\n0800000002\n0800000003"
		b := newTestBulkAddRequest(content)

		actual, err := b.parsePhonesFromCSV()
		assert.NoError(tt, err)
		assert.Equal(tt, 3, len(actual))
		expectedPhones := []crypt.EncryptedString{"0800000001", "0800000002", "0800000003"}
		assert.ElementsMatch(tt, expectedPhones, actual)
	})

	t.Run("returns error if file is in wrong format", func(tt *testing.T) {
		content := "0812345678,0812345679"
		b := newTestBulkAddRequest(content)

		_, err := b.parsePhonesFromCSV()
		assert.Error(tt, err)
		assert.Equal(tt, errFileFormat, err)
	})

	t.Run("returns error if phone number is in wrong format", func(tt *testing.T) {
		content := "66812345678"
		b := newTestBulkAddRequest(content)

		_, err := b.parsePhonesFromCSV()
		assert.Error(tt, err)
		assert.Equal(tt, errFileFormat, err)
	})

}

func newTestBulkAddRequest(content string) *BulkAddRequest {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)

	var fw io.Writer
	fw, _ = w.CreateFormField("requestedBy")
	fw.Write([]byte("placeholder"))

	fw, _ = w.CreateFormFile("file", "file.csv")
	fw.Write([]byte(content))
	w.Close()

	gctx, _ := testutil.TestRequestContext("POST", "", &b)
	gctx.Request.Header.Set("Content-Type", w.FormDataContentType())

	var out BulkAddRequest
	gctx.ShouldBind(&out)

	return &out
}
