//go:build integration_test
// +build integration_test

package installment_test

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/installment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestAdvanceInstallment(t *testing.T) {
	type RequestBody struct {
		InstallmentID     string      `json:"installmentId"`
		DriverID          string      `json:"driverId"`
		AdvancementAmount types.Money `json:"advancementAmount"`
	}

	type DriverBalance struct {
		Wallet            types.Money
		Credit            types.Money
		InstallmentAmount types.Money
	}

	type ExpectInstallment struct {
		InstallmentAmount types.Money
		OverdueAmount     types.Money
		ActualAmounts     []types.Money
	}

	type BaseInstallmentSet struct {
		baseInstallments  []model.Installment
		baseProducts      []model.Product
		baseProductGroups []model.ProductGroup
	}
	type GetBaseInstallmentSet func() BaseInstallmentSet

	type TestData struct {
		Name                  string
		requestBody           []RequestBody
		baseDriverBalance     DriverBalance
		baseInstallmentSet    GetBaseInstallmentSet
		expectedDriverBalance DriverBalance
		expectInstallments    map[string]ExpectInstallment
		expectedFailedSize    int
	}

	container := ittest.NewContainer(t)

	productGroups, _ := generateProductGroup(t, container)
	products, _ := generateProduct(t, container)
	installments, _ := generateInstallment(t, container)

	makeReq := func(data any) *testutil.GinContextWithRecorder {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/advance-installments").
			AdminAuthorized("<EMAIL>")
		ctx.SetBody(testutil.JSON(&data))
		return ctx
	}

	targetTestDriverID := "DRV_PATTAYA_ONLINE"

	timeNow := time.Now()
	instGenerator := InstallmentGenerator{
		DailyAmount: 100,
		DayPeriod:   2,
		Timenow:     timeNow,
		SKU:         "PRODUCT_1",
	}
	productGnrtr := InstallmentProductGenerator{}
	productGGnrtr := InstallmentProductGroupGenerator{}

	getBaseInstallmentSet := func() BaseInstallmentSet {
		return BaseInstallmentSet{
			baseInstallments: []model.Installment{
				instGenerator.GetFreshInstallment(installments[0].ID, targetTestDriverID),
			},
			baseProducts: []model.Product{
				productGnrtr.UpdatePriority(products[0], 1),
			},
			baseProductGroups: []model.ProductGroup{
				productGGnrtr.UpdatePriority(productGroups[0], 1),
			},
		}
	}

	testSet := []TestData{
		{
			Name: "advance-1",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: getBaseInstallmentSet,
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 100,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100},
				},
			},
		},
		{
			Name: "rebalance-1",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -100,
				InstallmentAmount: -100,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPastDueInstallment(installments[0].ID, targetTestDriverID, 1),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 100,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100},
				},
			},
		},
		{
			Name: "rebalance-half-advance-1-log",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 150,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            150,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPaidPastDueInstallment(installments[0].ID, targetTestDriverID, 1, 50),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{50, 50, 100},
				},
			},
		},
		{
			Name: "rebalance-half",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPaidPastDueInstallment(installments[0].ID, targetTestDriverID, 1, 50),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            50,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 100,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{50, 50},
				},
			},
		},
		{
			Name: "rebalancing-clear-only-expense",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 50,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            50,
				Credit:            -150,
				InstallmentAmount: -100,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPastDueInstallment(installments[0].ID, targetTestDriverID, 1),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            -100,
				InstallmentAmount: -100,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     100,
					ActualAmounts:     []types.Money{},
				},
			},
		},
		{
			Name: "advance-2",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 200,
				},
				{
					InstallmentID:     installments[1].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            200,
				Credit:            200,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = append(result.baseInstallments,
					instGenerator.GetFreshInstallment(installments[1].ID, targetTestDriverID))
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            100,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100},
				},
				installments[1].ID.Hex(): {
					InstallmentAmount: 100,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100},
				},
			},
		},
		{
			Name: "rebalance-2-adv-1",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 400,
				},
				{
					InstallmentID:     installments[1].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            600,
				Credit:            -400,
				InstallmentAmount: -400,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.SetDayPeriod(3).GetPastDueInstallment(installments[0].ID, targetTestDriverID, 1),
					instGenerator.SetSKU("PRODUCT_2").SetDayPeriod(3).GetPastDueInstallment(installments[1].ID, targetTestDriverID, 3),
				}
				result.baseProducts = append(result.baseProducts,
					productGnrtr.UpdatePriority(products[1], 2))
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100},
				},
				installments[1].ID.Hex(): {
					InstallmentAmount: 300,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100, 100},
				},
			},
		},
		{
			Name: "driver-id-mismatch",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 200,
				},
				{
					InstallmentID:     installments[1].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            200,
				Credit:            200,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = append(result.baseInstallments,
					instGenerator.GetFreshInstallment(installments[1].ID, targetTestDriverID+"!"))
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            200,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100},
				},
				installments[1].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
				},
			},
			expectedFailedSize: 1,
		},
		{
			Name: "no-txn-to-service",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 1,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: getBaseInstallmentSet,
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
				},
			},
		},
	}

	for index := range testSet {
		testData := testSet[index]
		t.Run(testData.Name, func(tt *testing.T) {
			requestBody := testData.requestBody
			ctx := makeReq(requestBody)

			dt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
			require.NoError(tt, err)
			dt.PurchaseCreditBalance = testData.baseDriverBalance.Credit
			dt.WalletBalance = testData.baseDriverBalance.Wallet
			dt.InstallmentAmount = testData.baseDriverBalance.InstallmentAmount

			baseInstallmentSet := testData.baseInstallmentSet()
			updateDatabase(ctx.GinCtx(), tt, container,
				dt,
				baseInstallmentSet.baseInstallments,
				baseInstallmentSet.baseProducts,
				baseInstallmentSet.baseProductGroups,
			)

			container.GinEngineRouter.HandleContext(ctx.GinCtx())
			ctx.AssertResponseCode(tt, http.StatusOK)
			var actualResponse installment.AdvanceInstallmentResponse
			testutil.DecodeJSON(tt, ctx.ResponseRecorder.Body, &actualResponse)
			assert.Len(tt, actualResponse.Failures, testData.expectedFailedSize)

			dtResult, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
			require.NoError(tt, err)
			assert.Equal(tt, testData.expectedDriverBalance.Wallet, dtResult.WalletBalance)
			assert.Equal(tt, testData.expectedDriverBalance.Credit, dtResult.CreditBalance())
			assert.Equal(tt, testData.expectedDriverBalance.InstallmentAmount, dtResult.InstallmentAmount)

			installmentObjectIDs := make([]primitive.ObjectID, len(baseInstallmentSet.baseInstallments))
			for index := range baseInstallmentSet.baseInstallments {
				installmentObjectIDs[index] = baseInstallmentSet.baseInstallments[index].ID
			}
			var installmentResults []model.Installment
			err = container.InstallmentDataStore.Find(ctx.GinCtx(),
				primitive.M{"_id": primitive.M{"$in": installmentObjectIDs}},
				0, 0, &installmentResults)
			require.NoError(tt, err)

			assert.Len(tt, installmentResults, len(installmentObjectIDs))
			for _, item := range installmentResults {
				expectedInstallment, existed := testData.expectInstallments[item.ID.Hex()]
				assert.True(tt, existed)
				assert.Equal(tt, expectedInstallment.InstallmentAmount, item.InstallmentAmount)
				assert.Equal(tt, expectedInstallment.OverdueAmount, item.OverdueAmount)
				var indexer int
				for _, item := range item.InstallmentLogs {
					for _, item := range item.ActualAmountLogs {
						assert.Equal(tt, expectedInstallment.ActualAmounts[indexer], item.ActualAmount)
						indexer++
					}
				}
				assert.Equal(tt, len(expectedInstallment.ActualAmounts), indexer)
			}
		})
	}
}

func TestBulkUploadAdvanceInstallmentTriggerDryRun(t *testing.T) {
	type DriverBalance struct {
		Wallet            types.Money
		Credit            types.Money
		InstallmentAmount types.Money
	}

	type ExpectInstallment struct {
		InstallmentAmount types.Money
		OverdueAmount     types.Money
		ActualAmounts     []types.Money
	}

	type BaseInstallmentSet struct {
		baseInstallments  []model.Installment
		baseProducts      []model.Product
		baseProductGroups []model.ProductGroup
	}
	type GetBaseInstallmentSet func() BaseInstallmentSet

	type TestData struct {
		Name                  string
		requestBody           string
		baseDriverBalance     DriverBalance
		baseInstallmentSet    GetBaseInstallmentSet
		expectedDriverBalance DriverBalance
		expectInstallments    map[string]ExpectInstallment
		expectedFailedSize    int
		expectResponses       []installment.AdvanceInstallmentSuccessInfo
	}

	container := ittest.NewContainer(t)

	productGroups, _ := generateProductGroup(t, container)
	products, _ := generateProduct(t, container)
	installments, _ := generateInstallment(t, container)

	makeReq := func(content string) *testutil.GinContextWithRecorder {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/upload-advance-installments").
			AdminAuthorized("<EMAIL>")
		ctx.Body().MultipartForm().
			File("file", "file.csv", content).
			String("requestedBy", "anonymous").
			Build()
		return ctx
	}

	targetTestDriverID := "DRV_PATTAYA_ONLINE"

	timeNow := time.Now()
	instGenerator := InstallmentGenerator{
		DailyAmount: 100,
		DayPeriod:   2,
		Timenow:     timeNow,
		SKU:         "PRODUCT_1",
	}
	productGnrtr := InstallmentProductGenerator{}
	productGGnrtr := InstallmentProductGroupGenerator{}

	getBaseInstallmentSet := func() BaseInstallmentSet {
		return BaseInstallmentSet{
			baseInstallments: []model.Installment{
				instGenerator.GetFreshInstallment(installments[0].ID, targetTestDriverID),
			},
			baseProducts: []model.Product{
				productGnrtr.UpdatePriority(products[0], 1),
			},
			baseProductGroups: []model.ProductGroup{
				productGGnrtr.UpdatePriority(productGroups[0], 1),
			},
		}
	}

	testSet := []TestData{
		{
			Name: "advance-1",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "100",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: getBaseInstallmentSet,
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(100),
					InstallmentAmount:       types.NewMoney(100),
					OutstandingAmount:       types.NewMoney(-100),
					OverdueAmount:           types.NewMoney(0),
					ActualAdvancementAmount: types.NewMoney(100),
				},
			},
		},
		{
			Name: "rebalance-1",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "100",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -100,
				InstallmentAmount: -100,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPastDueInstallment(installments[0].ID, targetTestDriverID, 1),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -100,
				InstallmentAmount: -100,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     100,
					ActualAmounts:     []types.Money{},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(100),
					InstallmentAmount:       types.NewMoney(100),
					OutstandingAmount:       types.NewMoney(-100),
					OverdueAmount:           types.NewMoney(-100),
					ActualAdvancementAmount: types.NewMoney(0),
				},
			},
		},
		{
			Name: "rebalance-half-advance-1-log",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "150",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            150,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPaidPastDueInstallment(installments[0].ID, targetTestDriverID, 1, types.NewMoney(50)),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            150,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 50,
					OverdueAmount:     50,
					ActualAmounts:     []types.Money{50},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(150),
					InstallmentAmount:       types.NewMoney(150),
					OutstandingAmount:       types.NewMoney(-150),
					OverdueAmount:           types.NewMoney(-50),
					ActualAdvancementAmount: types.NewMoney(100),
				},
			},
		},
		{
			Name: "rebalance-half",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "100",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPaidPastDueInstallment(installments[0].ID, targetTestDriverID, 1, 50),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 50,
					OverdueAmount:     50,
					ActualAmounts: []types.Money{
						50,
					},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(100),
					InstallmentAmount:       types.NewMoney(50),
					OutstandingAmount:       types.NewMoney(-50),
					OverdueAmount:           types.NewMoney(-50),
					ActualAdvancementAmount: types.NewMoney(0),
				},
			},
		},
		{
			Name: "advance-more-than-balance",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "120",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: getBaseInstallmentSet,
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(120),
					InstallmentAmount:       types.NewMoney(100),
					OutstandingAmount:       types.NewMoney(-100),
					OverdueAmount:           types.NewMoney(0),
					ActualAdvancementAmount: types.NewMoney(100),
				},
			},
		},
		{
			Name: "advance-more-than-balance",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "120",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: getBaseInstallmentSet,
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(120),
					InstallmentAmount:       types.NewMoney(100),
					OutstandingAmount:       types.NewMoney(-100),
					OverdueAmount:           types.NewMoney(0),
					ActualAdvancementAmount: types.NewMoney(100),
				},
			},
		},
		{
			Name: "rebalance-2",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "400",
				installments[1].ID.Hex(), "DRV_PATTAYA_ONLINE", "100",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            600,
				Credit:            -400,
				InstallmentAmount: -400,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.SetDayPeriod(3).GetPastDueInstallment(installments[0].ID, targetTestDriverID, 1),
					instGenerator.SetDayPeriod(3).GetPastDueInstallment(installments[1].ID, targetTestDriverID, 3),
				}
				result.baseProducts = append(result.baseProducts,
					productGnrtr.UpdatePriority(products[1], 2))
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            600,
				Credit:            -400,
				InstallmentAmount: -400,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     100,
					ActualAmounts:     []types.Money{},
				},
				installments[1].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     300,
					ActualAmounts:     []types.Money{},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(400),
					InstallmentAmount:       types.NewMoney(200),
					OutstandingAmount:       types.NewMoney(-200),
					OverdueAmount:           types.NewMoney(-100),
					ActualAdvancementAmount: types.NewMoney(100),
				},
				{
					InstallmentID:           installments[1].ID.Hex(),
					AdvancementAmount:       types.NewMoney(100),
					InstallmentAmount:       types.NewMoney(300),
					OutstandingAmount:       types.NewMoney(-300),
					OverdueAmount:           types.NewMoney(-300),
					ActualAdvancementAmount: types.NewMoney(0),
				},
			},
		},
	}

	for index := range testSet {
		testData := testSet[index]
		t.Run(testData.Name, func(tt *testing.T) {
			requestBody := testData.requestBody
			ctx := makeReq(requestBody)

			dt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
			require.NoError(tt, err)
			dt.PurchaseCreditBalance = testData.baseDriverBalance.Credit
			dt.WalletBalance = testData.baseDriverBalance.Wallet
			dt.InstallmentAmount = testData.baseDriverBalance.InstallmentAmount

			baseInstallmentSet := testData.baseInstallmentSet()
			updateDatabase(ctx.GinCtx(), tt, container,
				dt,
				baseInstallmentSet.baseInstallments,
				baseInstallmentSet.baseProducts,
				baseInstallmentSet.baseProductGroups,
			)

			container.GinEngineRouter.HandleContext(ctx.GinCtx())
			ctx.AssertResponseCode(tt, http.StatusOK)
			var actualResponse installment.AdvanceInstallmentResponse
			testutil.DecodeJSON(tt, ctx.ResponseRecorder.Body, &actualResponse)
			assert.Len(tt, actualResponse.Failures, testData.expectedFailedSize)
			assert.Len(tt, actualResponse.Successes, len(testData.expectResponses))
			for index, expectResponse := range testData.expectResponses {
				actualSuccess := actualResponse.Successes[index]
				assert.Equal(tt, expectResponse.InstallmentID, actualSuccess.InstallmentID)
				assert.Equal(tt, expectResponse.AdvancementAmount, actualSuccess.AdvancementAmount)
				assert.Equal(tt, expectResponse.InstallmentAmount, actualSuccess.InstallmentAmount)
				assert.Equal(tt, expectResponse.OutstandingAmount, actualSuccess.OutstandingAmount)
				assert.Equal(tt, expectResponse.OverdueAmount, actualSuccess.OverdueAmount)
				assert.Equal(tt, expectResponse.ActualAdvancementAmount, actualSuccess.ActualAdvancementAmount)
			}

			dtResult, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
			require.NoError(tt, err)
			assert.Equal(tt, testData.expectedDriverBalance.Wallet, dtResult.WalletBalance)
			assert.Equal(tt, testData.expectedDriverBalance.Credit, dtResult.CreditBalance())
			assert.Equal(tt, testData.expectedDriverBalance.InstallmentAmount, dtResult.InstallmentAmount)

			installmentObjectIDs := make([]primitive.ObjectID, len(baseInstallmentSet.baseInstallments))
			for index := range baseInstallmentSet.baseInstallments {
				installmentObjectIDs[index] = baseInstallmentSet.baseInstallments[index].ID
			}
			var installmentResults []model.Installment
			err = container.InstallmentDataStore.Find(ctx.GinCtx(),
				primitive.M{"_id": primitive.M{"$in": installmentObjectIDs}},
				0, 0, &installmentResults)
			require.NoError(tt, err)

			assert.Len(tt, installmentResults, len(installmentObjectIDs))
			for _, item := range installmentResults {
				expectedInstallment, existed := testData.expectInstallments[item.ID.Hex()]
				assert.True(tt, existed)
				assert.Equal(tt, expectedInstallment.InstallmentAmount, item.InstallmentAmount)
				assert.Equal(tt, expectedInstallment.OverdueAmount, item.OverdueAmount)
				var indexer int
				for _, item := range item.InstallmentLogs {
					for _, item := range item.ActualAmountLogs {
						assert.Equal(tt, expectedInstallment.ActualAmounts[indexer], item.ActualAmount)
						indexer++
					}
				}
				assert.Equal(tt, len(expectedInstallment.ActualAmounts), indexer)
			}
		})
	}
}

func TestAdvanceInstallmentIMS(t *testing.T) {
	type RequestBody struct {
		InstallmentID     string      `json:"installmentId"`
		DriverID          string      `json:"driverId"`
		AdvancementAmount types.Money `json:"advancementAmount"`
	}

	type DriverBalance struct {
		Wallet            types.Money
		Credit            types.Money
		InstallmentAmount types.Money
	}

	type ExpectInstallment struct {
		InstallmentAmount types.Money
		OverdueAmount     types.Money
		ActualAmounts     []types.Money
	}

	type BaseInstallmentSet struct {
		baseInstallments  []model.Installment
		baseProducts      []model.Product
		baseProductGroups []model.ProductGroup
	}
	type GetBaseInstallmentSet func() BaseInstallmentSet

	type TestData struct {
		Name                  string
		requestBody           []RequestBody
		baseDriverBalance     DriverBalance
		baseInstallmentSet    GetBaseInstallmentSet
		expectedDriverBalance DriverBalance
		expectInstallments    map[string]ExpectInstallment
		expectedFailedSize    int
	}

	os.Setenv("EnableInventoryManagementService", "true")
	container := ittest.NewContainer(t)

	productGroups, _ := generateProductGroup(t, container)
	products, _ := generateProduct(t, container)
	installments, _ := generateInstallment(t, container)

	makeReq := func(data any) *testutil.GinContextWithRecorder {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/advance-installments").
			AdminAuthorized("<EMAIL>")
		ctx.SetBody(testutil.JSON(&data))
		return ctx
	}

	targetTestDriverID := "DRV_PATTAYA_ONLINE"

	timeNow := time.Now()
	instGenerator := InstallmentGenerator{
		DailyAmount: 100,
		DayPeriod:   2,
		Timenow:     timeNow,
		SKU:         "PRODUCT_1",
	}
	productGnrtr := InstallmentProductGenerator{}
	productGGnrtr := InstallmentProductGroupGenerator{}

	getBaseInstallmentSet := func() BaseInstallmentSet {
		return BaseInstallmentSet{
			baseInstallments: []model.Installment{
				instGenerator.GetFreshInstallment(installments[0].ID, targetTestDriverID),
			},
			baseProducts: []model.Product{
				productGnrtr.UpdatePriority(products[0], 1),
			},
			baseProductGroups: []model.ProductGroup{
				productGGnrtr.UpdatePriority(productGroups[0], 1),
			},
		}
	}

	testSet := []TestData{
		{
			Name: "advance-1",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: getBaseInstallmentSet,
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 100,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100},
				},
			},
		},
		{
			Name: "rebalance-1",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -100,
				InstallmentAmount: -100,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPastDueInstallment(installments[0].ID, targetTestDriverID, 1),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 100,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100},
				},
			},
		},
		{
			Name: "rebalance-half-advance-1-log",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 150,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            150,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPaidPastDueInstallment(installments[0].ID, targetTestDriverID, 1, 50),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{50, 50, 100},
				},
			},
		},
		{
			Name: "rebalance-half",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPaidPastDueInstallment(installments[0].ID, targetTestDriverID, 1, 50),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            50,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 100,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{50, 50},
				},
			},
		},
		{
			Name: "rebalancing-clear-only-expense",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 50,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            50,
				Credit:            -150,
				InstallmentAmount: -100,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPastDueInstallment(installments[0].ID, targetTestDriverID, 1),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            -100,
				InstallmentAmount: -100,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     100,
					ActualAmounts:     []types.Money{},
				},
			},
		},
		{
			Name: "advance-2",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 200,
				},
				{
					InstallmentID:     installments[1].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            200,
				Credit:            200,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = append(result.baseInstallments,
					instGenerator.GetFreshInstallment(installments[1].ID, targetTestDriverID))
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            100,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100},
				},
				installments[1].ID.Hex(): {
					InstallmentAmount: 100,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100},
				},
			},
		},
		{
			Name: "rebalance-2-adv-1",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 400,
				},
				{
					InstallmentID:     installments[1].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            600,
				Credit:            -400,
				InstallmentAmount: -400,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.SetDayPeriod(3).GetPastDueInstallment(installments[0].ID, targetTestDriverID, 1),
					instGenerator.SetSKU("PRODUCT_2").SetDayPeriod(3).GetPastDueInstallment(installments[1].ID, targetTestDriverID, 3),
				}
				result.baseProducts = append(result.baseProducts,
					productGnrtr.UpdatePriority(products[1], 2))
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100},
				},
				installments[1].ID.Hex(): {
					InstallmentAmount: 300,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100, 100},
				},
			},
		},
		{
			Name: "driver-id-mismatch",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 200,
				},
				{
					InstallmentID:     installments[1].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 100,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            200,
				Credit:            200,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = append(result.baseInstallments,
					instGenerator.GetFreshInstallment(installments[1].ID, targetTestDriverID+"!"))
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            200,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100},
				},
				installments[1].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
				},
			},
			expectedFailedSize: 1,
		},
		{
			Name: "no-txn-to-service",
			requestBody: []RequestBody{
				{
					InstallmentID:     installments[0].ID.Hex(),
					DriverID:          targetTestDriverID,
					AdvancementAmount: 1,
				},
			},
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: getBaseInstallmentSet,
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
				},
			},
		},
	}

	for index := range testSet {
		testData := testSet[index]
		t.Run(testData.Name, func(tt *testing.T) {
			requestBody := testData.requestBody
			ctx := makeReq(requestBody)

			dt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
			require.NoError(tt, err)
			dt.PurchaseCreditBalance = testData.baseDriverBalance.Credit
			dt.WalletBalance = testData.baseDriverBalance.Wallet
			dt.InstallmentAmount = testData.baseDriverBalance.InstallmentAmount

			baseInstallmentSet := testData.baseInstallmentSet()
			updateDatabase(ctx.GinCtx(), tt, container,
				dt,
				baseInstallmentSet.baseInstallments,
				[]model.Product{},
				[]model.ProductGroup{},
			)
			setProductServiceStub("", container.StubGRPCProductService, baseInstallmentSet.baseProducts, baseInstallmentSet.baseProductGroups)

			container.GinEngineRouter.HandleContext(ctx.GinCtx())
			ctx.AssertResponseCode(tt, http.StatusOK)
			var actualResponse installment.AdvanceInstallmentResponse
			testutil.DecodeJSON(tt, ctx.ResponseRecorder.Body, &actualResponse)
			assert.Len(tt, actualResponse.Failures, testData.expectedFailedSize)

			dtResult, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
			require.NoError(tt, err)
			assert.Equal(tt, testData.expectedDriverBalance.Wallet, dtResult.WalletBalance)
			assert.Equal(tt, testData.expectedDriverBalance.Credit, dtResult.CreditBalance())
			assert.Equal(tt, testData.expectedDriverBalance.InstallmentAmount, dtResult.InstallmentAmount)

			installmentObjectIDs := make([]primitive.ObjectID, len(baseInstallmentSet.baseInstallments))
			for index := range baseInstallmentSet.baseInstallments {
				installmentObjectIDs[index] = baseInstallmentSet.baseInstallments[index].ID
			}
			var installmentResults []model.Installment
			err = container.InstallmentDataStore.Find(ctx.GinCtx(),
				primitive.M{"_id": primitive.M{"$in": installmentObjectIDs}},
				0, 0, &installmentResults)
			require.NoError(tt, err)

			assert.Len(tt, installmentResults, len(installmentObjectIDs))
			for _, item := range installmentResults {
				expectedInstallment, existed := testData.expectInstallments[item.ID.Hex()]
				assert.True(tt, existed)
				assert.Equal(tt, expectedInstallment.InstallmentAmount, item.InstallmentAmount)
				assert.Equal(tt, expectedInstallment.OverdueAmount, item.OverdueAmount)
				var indexer int
				for _, item := range item.InstallmentLogs {
					for _, item := range item.ActualAmountLogs {
						assert.Equal(tt, expectedInstallment.ActualAmounts[indexer], item.ActualAmount)
						indexer++
					}
				}
				assert.Equal(tt, len(expectedInstallment.ActualAmounts), indexer)
			}
		})
	}
}

func TestBulkUploadAdvanceInstallmentTriggerDryRunIMS(t *testing.T) {
	type DriverBalance struct {
		Wallet            types.Money
		Credit            types.Money
		InstallmentAmount types.Money
	}

	type ExpectInstallment struct {
		InstallmentAmount types.Money
		OverdueAmount     types.Money
		ActualAmounts     []types.Money
	}

	type BaseInstallmentSet struct {
		baseInstallments  []model.Installment
		baseProducts      []model.Product
		baseProductGroups []model.ProductGroup
	}
	type GetBaseInstallmentSet func() BaseInstallmentSet

	type TestData struct {
		Name                  string
		requestBody           string
		baseDriverBalance     DriverBalance
		baseInstallmentSet    GetBaseInstallmentSet
		expectedDriverBalance DriverBalance
		expectInstallments    map[string]ExpectInstallment
		expectedFailedSize    int
		expectResponses       []installment.AdvanceInstallmentSuccessInfo
	}

	os.Setenv("EnableInventoryManagementService", "true")
	container := ittest.NewContainer(t)

	productGroups, _ := generateProductGroup(t, container)
	products, _ := generateProduct(t, container)
	installments, _ := generateInstallment(t, container)

	makeReq := func(content string) *testutil.GinContextWithRecorder {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/upload-advance-installments").
			AdminAuthorized("<EMAIL>")
		ctx.Body().MultipartForm().
			File("file", "file.csv", content).
			String("requestedBy", "anonymous").
			Build()
		return ctx
	}

	targetTestDriverID := "DRV_PATTAYA_ONLINE"

	timeNow := time.Now()
	instGenerator := InstallmentGenerator{
		DailyAmount: 100,
		DayPeriod:   2,
		Timenow:     timeNow,
		SKU:         "PRODUCT_1",
	}
	productGnrtr := InstallmentProductGenerator{}
	productGGnrtr := InstallmentProductGroupGenerator{}

	getBaseInstallmentSet := func() BaseInstallmentSet {
		return BaseInstallmentSet{
			baseInstallments: []model.Installment{
				instGenerator.GetFreshInstallment(installments[0].ID, targetTestDriverID),
			},
			baseProducts: []model.Product{
				productGnrtr.UpdatePriority(products[0], 1),
			},
			baseProductGroups: []model.ProductGroup{
				productGGnrtr.UpdatePriority(productGroups[0], 1),
			},
		}
	}

	testSet := []TestData{
		{
			Name: "advance-1",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "100",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: getBaseInstallmentSet,
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(100),
					InstallmentAmount:       types.NewMoney(100),
					OutstandingAmount:       types.NewMoney(-100),
					OverdueAmount:           types.NewMoney(0),
					ActualAdvancementAmount: types.NewMoney(100),
				},
			},
		},
		{
			Name: "rebalance-1",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "100",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -100,
				InstallmentAmount: -100,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPastDueInstallment(installments[0].ID, targetTestDriverID, 1),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -100,
				InstallmentAmount: -100,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     100,
					ActualAmounts:     []types.Money{},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(100),
					InstallmentAmount:       types.NewMoney(100),
					OutstandingAmount:       types.NewMoney(-100),
					OverdueAmount:           types.NewMoney(-100),
					ActualAdvancementAmount: types.NewMoney(0),
				},
			},
		},
		{
			Name: "rebalance-half-advance-1-log",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "150",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            150,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPaidPastDueInstallment(installments[0].ID, targetTestDriverID, 1, types.NewMoney(50)),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            150,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 50,
					OverdueAmount:     50,
					ActualAmounts:     []types.Money{50},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(150),
					InstallmentAmount:       types.NewMoney(150),
					OutstandingAmount:       types.NewMoney(-150),
					OverdueAmount:           types.NewMoney(-50),
					ActualAdvancementAmount: types.NewMoney(100),
				},
			},
		},
		{
			Name: "rebalance-half",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "100",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetPaidPastDueInstallment(installments[0].ID, targetTestDriverID, 1, 50),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -50,
				InstallmentAmount: -50,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 50,
					OverdueAmount:     50,
					ActualAmounts: []types.Money{
						50,
					},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(100),
					InstallmentAmount:       types.NewMoney(50),
					OutstandingAmount:       types.NewMoney(-50),
					OverdueAmount:           types.NewMoney(-50),
					ActualAdvancementAmount: types.NewMoney(0),
				},
			},
		},
		{
			Name: "advance-more-than-balance",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "120",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: getBaseInstallmentSet,
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(120),
					InstallmentAmount:       types.NewMoney(100),
					OutstandingAmount:       types.NewMoney(-100),
					OverdueAmount:           types.NewMoney(0),
					ActualAdvancementAmount: types.NewMoney(100),
				},
			},
		},
		{
			Name: "advance-more-than-balance",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "120",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: getBaseInstallmentSet,
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(120),
					InstallmentAmount:       types.NewMoney(100),
					OutstandingAmount:       types.NewMoney(-100),
					OverdueAmount:           types.NewMoney(0),
					ActualAdvancementAmount: types.NewMoney(100),
				},
			},
		},
		{
			Name: "rebalance-2",
			requestBody: fmt.Sprintf("%s\n%s,%s,%s\n%s,%s,%s",
				"installment id, driver id, advancement amount",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "400",
				installments[1].ID.Hex(), "DRV_PATTAYA_ONLINE", "100",
			),
			baseDriverBalance: DriverBalance{
				Wallet:            600,
				Credit:            -400,
				InstallmentAmount: -400,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.SetDayPeriod(3).GetPastDueInstallment(installments[0].ID, targetTestDriverID, 1),
					instGenerator.SetDayPeriod(3).GetPastDueInstallment(installments[1].ID, targetTestDriverID, 3),
				}
				result.baseProducts = append(result.baseProducts,
					productGnrtr.UpdatePriority(products[1], 2))
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            600,
				Credit:            -400,
				InstallmentAmount: -400,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     100,
					ActualAmounts:     []types.Money{},
				},
				installments[1].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     300,
					ActualAmounts:     []types.Money{},
				},
			},
			expectResponses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:           installments[0].ID.Hex(),
					AdvancementAmount:       types.NewMoney(400),
					InstallmentAmount:       types.NewMoney(200),
					OutstandingAmount:       types.NewMoney(-200),
					OverdueAmount:           types.NewMoney(-100),
					ActualAdvancementAmount: types.NewMoney(100),
				},
				{
					InstallmentID:           installments[1].ID.Hex(),
					AdvancementAmount:       types.NewMoney(100),
					InstallmentAmount:       types.NewMoney(300),
					OutstandingAmount:       types.NewMoney(-300),
					OverdueAmount:           types.NewMoney(-300),
					ActualAdvancementAmount: types.NewMoney(0),
				},
			},
		},
	}

	for index := range testSet {
		testData := testSet[index]
		t.Run(testData.Name, func(tt *testing.T) {
			requestBody := testData.requestBody
			ctx := makeReq(requestBody)

			dt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
			require.NoError(tt, err)
			dt.PurchaseCreditBalance = testData.baseDriverBalance.Credit
			dt.WalletBalance = testData.baseDriverBalance.Wallet
			dt.InstallmentAmount = testData.baseDriverBalance.InstallmentAmount

			baseInstallmentSet := testData.baseInstallmentSet()
			updateDatabase(ctx.GinCtx(), tt, container,
				dt,
				baseInstallmentSet.baseInstallments,
				[]model.Product{},
				[]model.ProductGroup{},
			)
			setProductServiceStub("", container.StubGRPCProductService, baseInstallmentSet.baseProducts, baseInstallmentSet.baseProductGroups)

			container.GinEngineRouter.HandleContext(ctx.GinCtx())
			ctx.AssertResponseCode(tt, http.StatusOK)
			var actualResponse installment.AdvanceInstallmentResponse
			testutil.DecodeJSON(tt, ctx.ResponseRecorder.Body, &actualResponse)
			assert.Len(tt, actualResponse.Failures, testData.expectedFailedSize)
			assert.Len(tt, actualResponse.Successes, len(testData.expectResponses))
			for index, expectResponse := range testData.expectResponses {
				actualSuccess := actualResponse.Successes[index]
				assert.Equal(tt, expectResponse.InstallmentID, actualSuccess.InstallmentID)
				assert.Equal(tt, expectResponse.AdvancementAmount, actualSuccess.AdvancementAmount)
				assert.Equal(tt, expectResponse.InstallmentAmount, actualSuccess.InstallmentAmount)
				assert.Equal(tt, expectResponse.OutstandingAmount, actualSuccess.OutstandingAmount)
				assert.Equal(tt, expectResponse.OverdueAmount, actualSuccess.OverdueAmount)
				assert.Equal(tt, expectResponse.ActualAdvancementAmount, actualSuccess.ActualAdvancementAmount)
			}

			dtResult, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
			require.NoError(tt, err)
			assert.Equal(tt, testData.expectedDriverBalance.Wallet, dtResult.WalletBalance)
			assert.Equal(tt, testData.expectedDriverBalance.Credit, dtResult.CreditBalance())
			assert.Equal(tt, testData.expectedDriverBalance.InstallmentAmount, dtResult.InstallmentAmount)

			installmentObjectIDs := make([]primitive.ObjectID, len(baseInstallmentSet.baseInstallments))
			for index := range baseInstallmentSet.baseInstallments {
				installmentObjectIDs[index] = baseInstallmentSet.baseInstallments[index].ID
			}
			var installmentResults []model.Installment
			err = container.InstallmentDataStore.Find(ctx.GinCtx(),
				primitive.M{"_id": primitive.M{"$in": installmentObjectIDs}},
				0, 0, &installmentResults)
			require.NoError(tt, err)

			assert.Len(tt, installmentResults, len(installmentObjectIDs))
			for _, item := range installmentResults {
				expectedInstallment, existed := testData.expectInstallments[item.ID.Hex()]
				assert.True(tt, existed)
				assert.Equal(tt, expectedInstallment.InstallmentAmount, item.InstallmentAmount)
				assert.Equal(tt, expectedInstallment.OverdueAmount, item.OverdueAmount)
				var indexer int
				for _, item := range item.InstallmentLogs {
					for _, item := range item.ActualAmountLogs {
						assert.Equal(tt, expectedInstallment.ActualAmounts[indexer], item.ActualAmount)
						indexer++
					}
				}
				assert.Equal(tt, len(expectedInstallment.ActualAmounts), indexer)
			}
		})
	}
}
