package installment

import (
	"context"
	"fmt"

	"github.com/gin-gonic/gin"

	"git.wndv.co/go/logx/v2"
	absintheapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func (ia *InstallmentAPI) BulkResetEtaxInstallments(ctx *gin.Context) {
	var req BulkResetInstallmentEtaxRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	admin := auth.GetAdminEmailFromGctx(ctx)

	bulkData, err := req.GetCSVData()
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(err))
		return
	}

	response := BulkResetEtaxInstallmentResponse{
		Successes: make([]BulkResetEtaxInstallmentInfo, 0),
		Failures:  make([]BulkResetEtaxInstallmentInfo, 0),
	}

	for _, data := range bulkData {
		if !data.ErrorReporter.IsEmpty() {
			reports := data.ErrorReporter.ConvertToReport()
			for _, r := range reports {
				response.Failures = append(response.Failures, BulkResetEtaxInstallmentInfo{
					Row:                  r.Row,
					InstallmentID:        data.InstallmentID,
					Strategy:             string(data.Strategy),
					ExpectedDeliveryDate: data.ExpectedDeliveryDate,
					Reason:               r.Error,
				})
			}
			continue
		}

		ia.doResetEtaxInstallment(ctx, admin, data, &response)
	}

	apiutil.OK(ctx, response)
}

func (ia *InstallmentAPI) doResetEtaxInstallment(ctx context.Context, requester string, data ResetInstallmentEtaxData, resp *BulkResetEtaxInstallmentResponse) {
	method := "ResetEtaxInstallment"

	bulkResp := BulkResetEtaxInstallmentInfo{
		Row:                  data.RowIndex + 1,
		InstallmentID:        data.InstallmentID,
		Strategy:             string(data.Strategy),
		ExpectedDeliveryDate: data.ExpectedDeliveryDate,
	}

	cascadeRemark := model.Remark{
		Message:   fmt.Sprintf("Delete cascade from reset e-tax installment id: %s", data.InstallmentID),
		CreatedBy: requester,
		CreatedAt: timeutil.BangkokNow(),
	}

	if data.InstallmentID == "" {
		bulkResp.Reason = "installment id cannot empty"
		logx.Warn().Context(ctx).Str("method", method).Msg(bulkResp.String())
		resp.AddFailure(bulkResp)
		return
	}

	_, err := ia.txnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		installment, err := ia.installmentRepo.FindById(ctx, data.InstallmentID, repository.WithReadPrimary)
		if err != nil {
			return false, fmt.Errorf("unable find installment by id: %v", err)
		}

		if data.Strategy == model.ResetEtaxInstallmentStrategyCascadeEtaxAndDocument {
			if installment.FirstEtaxInvoiceIssued && installment.CreditNoteNo != "" {
				return false, fmt.Errorf("installment [%s]: latest e-tax is type credit note", data.InstallmentID)
			}
		} else if data.Strategy == model.ResetEtaxInstallmentStrategyNoCascade {
			if installment.CreditNoteNo == "" {
				return false, fmt.Errorf("installment [%s]: latest e-tax is not type credit note", data.InstallmentID)
			}
		}

		err = ia.resetEtaxInstallment(sessCtx, data, installment, requester)
		if err != nil {
			return false, err
		}
		logx.Info().Context(sessCtx).Str("method", method).Msgf("installment [%s]: e-tax installment reset", data.InstallmentID)

		if data.Strategy == model.ResetEtaxInstallmentStrategyNoCascade {
			return true, nil
		}

		invoiceNos, err := ia.softDeleteEtaxInvoices(sessCtx, data, cascadeRemark)
		if err != nil {
			return false, err
		}
		logx.Info().Context(sessCtx).Str("method", method).Msgf("installment [%s]: e-tax invoice(s) deleted %s", data.InstallmentID, stringutil.JoinNonEmpty(invoiceNos, ","))

		if len(invoiceNos) == 0 {
			return true, nil
		}

		err = ia.softDeleteDriverDocuments(sessCtx, invoiceNos, cascadeRemark)
		if err != nil {
			return false, err
		}
		logx.Info().Context(sessCtx).Str("method", method).Msgf("installment [%s]: driver document(s) deleted %s", data.InstallmentID, stringutil.JoinNonEmpty(invoiceNos, ","))

		return true, nil
	}, transaction.WithLabel("InstallmentAPI.doResetEtaxInstallment"))

	if err != nil {
		bulkResp.Reason = err.Error()
		logx.Info().Context(ctx).Str("method", method).Msgf("installment [%s]: txn rollback", data.InstallmentID)
		logx.Error().Context(ctx).Str("method", method).Err(err).Msg(bulkResp.String())
		resp.AddFailure(bulkResp)
		return
	}

	bulkResp.Reason = "success"
	logx.Info().Context(ctx).Str("method", method).Msgf("installment [%s]: txn commit", data.InstallmentID)
	logx.Info().Context(ctx).Str("method", method).Msg(bulkResp.String())
	resp.AddSuccess(bulkResp)
}

func (ia *InstallmentAPI) resetEtaxInstallment(ctx context.Context, data ResetInstallmentEtaxData, installment *model.Installment, requester string) error {
	installmentRemark := model.InstallmentRemark{
		Remark:    "Reset e-tax",
		CreatedBy: requester,
		CreatedAt: timeutil.BangkokNow(),
	}

	if err := ia.installmentRepo.ResetEtaxInvoice(ctx, installment.ID, data.ExpectedDeliveryDate, installmentRemark); err != nil {
		return fmt.Errorf("unable to reset e-tax installment: %v", err)
	}

	return nil
}

func (ia *InstallmentAPI) softDeleteEtaxInvoices(ctx context.Context, data ResetInstallmentEtaxData, cascadeRemark model.Remark) ([]string, error) {
	eTaxInvoicesQuery := persistence.BuildEtaxInvoiceQuery().WithInstallmentID(data.InstallmentID)
	eTaxInvoices, err := ia.eTaxInvoiceRepo.FindWithQueryAndSort(ctx, eTaxInvoicesQuery, 0, 0, repository.WithReadPrimary)
	if err != nil {
		return nil, fmt.Errorf("unable to list e-tax invoices: %v", err)
	}

	if len(eTaxInvoices) == 0 {
		return []string{}, nil
	}

	invoiceNos := make([]string, len(eTaxInvoices))
	for i, e := range eTaxInvoices {
		invoiceNos[i] = e.InvoiceNo
	}

	if err := ia.eTaxInvoiceRepo.SoftDeleteMany(ctx, eTaxInvoicesQuery, cascadeRemark); err != nil {
		return nil, fmt.Errorf("unable to soft delete e-tax invoices: %v", err)
	}

	return invoiceNos, nil
}

func (ia *InstallmentAPI) softDeleteDriverDocuments(ctx context.Context, invoiceNos []string, cascadeRemark model.Remark) error {
	documentsQB := persistence.DriverDocumentQuery{Names: invoiceNos, FileType: string(model.DriverDocumentTaxInvoiceEGSType)}
	documents, err := ia.documentRepo.GetByQuery(ctx, documentsQB.ToQuery(), repository.WithReadPrimary)
	if err != nil {
		return fmt.Errorf("unable to list documents: %v", err)
	}

	if len(documents) == 0 {
		return nil
	}

	documentNames := make([]string, len(documents))
	for i, d := range documents {
		documentNames[i] = d.Name
	}

	if err = ia.documentRepo.SoftDeleteMany(ctx, documentsQB.ToQuery(), cascadeRemark); err != nil {
		return fmt.Errorf("unable to soft delete documents: %v", err)
	}

	return nil
}
