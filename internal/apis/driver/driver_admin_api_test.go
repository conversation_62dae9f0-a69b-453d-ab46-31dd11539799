package driver

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/golang/mock/gomock"
	"github.com/icrowley/fake"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"gopkg.in/guregu/null.v4"

	unleashtest "git.wndv.co/go/unleash/test"
	"git.wndv.co/lineman/absinthe/crypt"
	apiValidator "git.wndv.co/lineman/fleet-distribution/internal/apis/validator"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep/mock_rep"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/riderlevel/mock_riderlevel"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore/mock_redis"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_unleash"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	"git.wndv.co/lineman/xgo/util"
)

func setUserIdToGctx(gctx *gin.Context, token, id, email string) {
	adminUser := auth.NewAdminUser(id, token, email, types.NewStringSet())
	auth.SetAdminUserToGctx(gctx, adminUser)
}

func generateDriver(id string) *model.Driver {
	now := time.Now()
	citiRefID := crypt.NewLazyEncryptedString(fake.DigitsN(10))
	return &model.Driver{
		DriverID: id,
		BaseDriver: model.BaseDriver{
			Title:                 crypt.NewLazyEncryptedString(fake.CharactersN(10)),
			Firstname:             crypt.NewLazyEncryptedString(fake.FirstName()),
			Lastname:              crypt.NewLazyEncryptedString(fake.LastName()),
			Phone:                 crypt.NewLazyEncryptedString("**********"),
			AvatarURL:             fake.CharactersN(20),
			CitizenID:             crypt.NewLazyEncryptedString(fake.DigitsN(13)),
			CitizenIDCardPhotoURL: fake.CharactersN(20),
			Birthday:              &now,
			CanExceedAgeLimit:     false,
			DriverType:            crypt.NewLazyEncryptedString(fake.CharactersN(10)),
			DriverTier:            model.DriverTierMember,

			Banking: model.BankingInfo{
				Account:   crypt.NewLazyEncryptedString(fake.DigitsN(10)),
				RefID:     citiRefID,
				CitiRefID: citiRefID.String(),
				UOBRefID:  fake.DigitsN(10),
				PhotoURL:  fmt.Sprintf("https://test1/%s", fake.CharactersN(20)),
			},

			CreatedAt: now.UTC(),
			UpdatedAt: now.UTC(),
		},
		RegistrationIDs: []primitive.ObjectID{primitive.NewObjectID(), primitive.NewObjectID()},
	}
}

func TestDriverAdminAPI_UpdateDriver(t *testing.T) {
	adminId := fake.CharactersN(10)
	email := "<EMAIL>"
	token := fake.CharactersN(20)

	makeReq := func(id string, req UpdateDriverReq) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/admin/driver/%s", id), testutil.JSON(&req))
		gctx.Params = gin.Params{
			gin.Param{Key: "driver_id", Value: id},
		}
		return gctx, recorder
	}

	createValidReq := func() UpdateDriverReq {
		return UpdateDriverReq{
			Title:      null.StringFrom("นาย"),
			Firstname:  null.StringFrom("ทดลอง"),
			Lastname:   null.StringFrom("ภาษาไทย"),
			Phone:      null.StringFrom("0812345678"),
			CitizenID:  null.StringFrom("2725142588851"),
			Birthday:   null.StringFrom("1989-03-09"),
			DriverType: null.StringFrom(string(model.DriverTypeTester)),
			DriverTier: null.StringFrom(string(model.DriverTierMember)),
			Trained:    null.BoolFrom(true),
			Region:     null.StringFrom("AYUTTHAYA"),
			HaveBox:    null.BoolFrom(true),
			HaveJacket: null.BoolFrom(true),
			Address: &AddressReq{
				HouseNumber: null.StringFrom(fake.Digits()),
				Moo:         null.StringFrom(fake.Digits()),
				Subdistrict: null.StringFrom(fake.CharactersN(10)),
				District:    null.StringFrom(fake.City()),
				Province:    null.StringFrom(fake.State()),
				Zipcode:     null.StringFrom(fake.DigitsN(5)),
			},
			BankingInfo: &BankingInfoReq{
				Account:       null.StringFrom("*********11"),
				BankName:      null.StringFrom("KBANK"),
				AccountHolder: null.StringFrom(fake.FullName()),
			},
			DriverLicenseInfo: &DriverLicenseInfoReq{
				ID:             null.StringFrom("*********"),
				ExpirationDate: null.StringFrom(time.Now().UTC().Add(time.Hour * 24 * 30).Format(dateFormat)),
			},
			VehicleInfo: &VehicleInfoReq{
				RegistrationDate:      null.StringFrom(time.Now().UTC().Add(-1 * time.Hour * 24 * 30).Format(dateFormat)),
				PlateNumber:           null.StringFrom("xxx-111111"),
				LegislationExpireDate: null.StringFrom("3000-01-01"),
			},
			DriverRole: null.StringFrom(string(model.DriverRoleNormal)),
		}
	}

	t.Run("should return 200 when update success", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		updateReq.ServiceTypes = []model.Service{model.ServiceMessenger, model.ServiceBike}
		updateReq.ServiceTypesSilentBanned = []model.Service{model.ServiceFood, model.ServiceBike}
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(generateDriver(id), nil)

		deps.srvareaRepo.EXPECT().
			GetByRegion(ctx, updateReq.Region.ValueOrZero()).
			Return(model.NewServiceArea("area-1", model.RegionCode(updateReq.Region.ValueOrZero())), nil).Times(2)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, driver *model.Driver) error {
				require.Equal(tt, updateReq.ServiceTypes, driver.ServiceTypes, "should be able to update service types")
				require.Equal(tt, updateReq.ServiceTypesSilentBanned, driver.ServiceTypesSilentBanned, "should be able to silent ban")
				return nil
			})

		deps.driverSvc.EXPECT().
			UnAssignUobRefFromDriver(ctx, gomock.Any()).
			Return(nil)

		deps.svAreaSv.EXPECT().RemoveDriverFromShiftWhitelist(ctx, id, "").Return(nil)
		deps.shiftRepo.EXPECT().Find(ctx, gomock.Any(), 0, 0).Return([]model.Shift{}, 0, nil)

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, auditLog *model.AuditLog) error {
			driverBeforeUpdate := auditLog.Before.(*model.Driver)
			driverAfterUpdate := auditLog.After.(*model.Driver)

			require.Equal(t, email, auditLog.Actor.ID)
			require.Equal(t, model.DriverObject, auditLog.Object.ObjectType)
			require.Equal(t, updateReq.Firstname.ValueOrZero(), driverAfterUpdate.Firstname.String())
			require.Equal(t, updateReq.Phone.ValueOrZero(), driverAfterUpdate.Phone.String())
			require.Equal(t, "**********", driverBeforeUpdate.Phone.String())

			return nil
		})

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual model.Driver
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, updateReq.Firstname.ValueOrZero(), actual.Firstname.String())
		require.Equal(tt, updateReq.Lastname.ValueOrZero(), actual.Lastname.String())
		require.Equal(tt, updateReq.Phone.ValueOrZero(), actual.Phone.String())
		require.Equal(tt, updateReq.CitizenID.ValueOrZero(), actual.CitizenID.String())
		require.Equal(tt, updateReq.DriverRole.ValueOrZero(), string(actual.DriverRole))
		require.Equal(t, updateReq.VehicleInfo.LegislationExpireDate.ValueOrZero(), actual.Vehicle.LegislationExpiredDate.Format(time.DateOnly))
	})

	t.Run("should return 200 when deactivate success", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(generateDriver(id), nil)

		deps.srvareaRepo.EXPECT().
			GetByRegion(ctx, updateReq.Region.ValueOrZero()).
			Return(model.NewServiceArea("area-1", model.RegionCode(updateReq.Region.ValueOrZero())), nil).Times(2)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.driverSvc.EXPECT().
			UnAssignUobRefFromDriver(ctx, gomock.Any()).
			Return(nil)

		deps.svAreaSv.EXPECT().RemoveDriverFromShiftWhitelist(ctx, id, "").Return(nil)
		deps.shiftRepo.EXPECT().Find(ctx, gomock.Any(), 0, 0).Return([]model.Shift{}, 0, nil)

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, auditLog *model.AuditLog) error {
			driverBeforeUpdate := auditLog.Before.(*model.Driver)
			driverAfterUpdate := auditLog.After.(*model.Driver)

			require.Equal(t, email, auditLog.Actor.ID)
			require.Equal(t, model.DriverObject, auditLog.Object.ObjectType)
			require.Equal(t, updateReq.Firstname.ValueOrZero(), driverAfterUpdate.Firstname.String())
			require.Equal(t, updateReq.Phone.ValueOrZero(), driverAfterUpdate.Phone.String())
			require.Equal(t, "**********", driverBeforeUpdate.Phone.String())

			return nil
		})

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual model.Driver
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, updateReq.Firstname.ValueOrZero(), actual.Firstname.String())
		require.Equal(tt, updateReq.Lastname.ValueOrZero(), actual.Lastname.String())
		require.Equal(tt, updateReq.Phone.ValueOrZero(), actual.Phone.String())
		require.Equal(tt, updateReq.CitizenID.ValueOrZero(), actual.CitizenID.String())
		require.Equal(tt, updateReq.DriverRole.ValueOrZero(), string(actual.DriverRole))
	})

	t.Run("should return 400 when update with invalid req (wrong driver tier)", func(tt *testing.T) {
		driverAPI, _, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		updateReq.DriverTier = null.StringFrom("WRONG_TIER")
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)

		driverAPI.UpdateDriver(gctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should not update DriverRole when DriverRole from request is empty string", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		updateReq.DriverRole = null.StringFromPtr(nil)
		gctx, recorder := makeReq(id, updateReq)
		ctx := gctx.Request.Context()

		driverRes := generateDriver(id)
		driverRes.DriverRole = model.DriverRoleNormal
		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(driverRes, nil)

		deps.srvareaRepo.EXPECT().
			GetByRegion(ctx, updateReq.Region.ValueOrZero()).
			Return(model.NewServiceArea("area-1", model.RegionCode(updateReq.Region.ValueOrZero())), nil).Times(2)

		deps.driverSvc.EXPECT().
			UnAssignUobRefFromDriver(ctx, gomock.Any()).
			Return(nil)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.svAreaSv.EXPECT().RemoveDriverFromShiftWhitelist(ctx, id, "").Return(nil)
		deps.shiftRepo.EXPECT().Find(ctx, gomock.Any(), 0, 0).Return([]model.Shift{}, 0, nil)

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual model.Driver
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, model.DriverRoleNormal, actual.DriverRole)
	})

	t.Run("should return 400 if invalid request", func(tt *testing.T) {
		driverAPI, _, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), UpdateDriverReq{
			DriverType: null.StringFrom("NON_EXISTS_TYPE"),
		}
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should return 404 if not found driver", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()

		deps.srvareaRepo.EXPECT().
			GetByRegion(ctx, updateReq.Region.ValueOrZero()).
			Return(model.NewServiceArea("area-1", model.RegionCode(updateReq.Region.ValueOrZero())), nil)

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(nil, repository.ErrNotFound)

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("should return 400 if region is not exists", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		updateReq.Region = null.StringFrom("NOT EXISTS")
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()

		deps.srvareaRepo.EXPECT().
			GetByRegion(ctx, updateReq.Region.ValueOrZero()).
			Return(nil, repository.ErrNotFound)

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should return 500 if update error", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(generateDriver(id), nil)

		deps.srvareaRepo.EXPECT().
			GetByRegion(ctx, updateReq.Region.ValueOrZero()).
			Return(model.NewServiceArea("area-1", model.RegionCode(updateReq.Region.ValueOrZero())), nil)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(errors.New("Mock error"))

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return 200 when update success and generate uobRefID", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()
		svcArea := model.NewServiceArea("area-1", model.RegionCode(updateReq.Region.ValueOrZero()))
		topUpConfig := model.NewServiceAreaTopUpConfig()
		topUpConfig.GenerateUobRefID = true
		svcArea.SetTopUpConfig(topUpConfig)

		driver := generateDriver(id)
		driver.Banking.UOBRefID = ""

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(driver, nil)

		deps.srvareaRepo.EXPECT().
			GetByRegion(ctx, updateReq.Region.ValueOrZero()).
			Return(svcArea, nil).Times(2)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.driverSvc.EXPECT().
			AssignUobRefToDriver(ctx, gomock.Any()).
			Return(nil)

		deps.svAreaSv.EXPECT().RemoveDriverFromShiftWhitelist(ctx, id, "").Return(nil)
		deps.shiftRepo.EXPECT().Find(ctx, gomock.Any(), 0, 0).Return([]model.Shift{}, 0, nil)

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, auditLog *model.AuditLog) error {
			driverBeforeUpdate := auditLog.Before.(*model.Driver)
			driverAfterUpdate := auditLog.After.(*model.Driver)

			require.Equal(t, email, auditLog.Actor.ID)
			require.Equal(t, model.DriverObject, auditLog.Object.ObjectType)
			require.Equal(t, updateReq.Firstname.ValueOrZero(), driverAfterUpdate.Firstname.String())
			require.Equal(t, updateReq.Phone.ValueOrZero(), driverAfterUpdate.Phone.String())
			require.Equal(t, "**********", driverBeforeUpdate.Phone.String())

			return nil
		})

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual model.Driver
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, updateReq.Firstname.ValueOrZero(), actual.Firstname.String())
		require.Equal(tt, updateReq.Lastname.ValueOrZero(), actual.Lastname.String())
		require.Equal(tt, updateReq.Phone.ValueOrZero(), actual.Phone.String())
		require.Equal(tt, updateReq.CitizenID.ValueOrZero(), actual.CitizenID.String())
		require.Equal(tt, updateReq.DriverRole.ValueOrZero(), string(actual.DriverRole))
	})

	t.Run("should remove shift data when change region", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()
		svcArea := model.NewServiceArea("area-1", model.RegionCode(updateReq.Region.ValueOrZero()))
		topUpConfig := model.NewServiceAreaTopUpConfig()
		topUpConfig.GenerateUobRefID = true
		svcArea.SetTopUpConfig(topUpConfig)

		driver := generateDriver(id)
		driver.Banking.UOBRefID = ""
		driver.Region = "BKK"

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(driver, nil)

		deps.srvareaRepo.EXPECT().
			GetByRegion(ctx, updateReq.Region.ValueOrZero()).
			Return(svcArea, nil).Times(2)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.driverSvc.EXPECT().
			AssignUobRefToDriver(ctx, gomock.Any()).
			Return(nil)

		deps.svAreaSv.EXPECT().RemoveDriverFromShiftWhitelist(ctx, id, "BKK").Return(nil)
		id1 := primitive.ObjectID{1}
		deps.shiftRepo.EXPECT().Find(ctx, gomock.Any(), 0, 0).Return([]model.Shift{
			{
				ID: id1,
			},
		}, 0, nil)
		deps.shiftRepo.EXPECT().UnBookMultipleShift(ctx, id, []string{id1.Hex()}).Return(nil)
		deps.driverRepo.EXPECT().RemoveShifts(ctx, id, []string{id1.Hex()}).Return(nil)

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, auditLog *model.AuditLog) error {
			driverBeforeUpdate := auditLog.Before.(*model.Driver)
			driverAfterUpdate := auditLog.After.(*model.Driver)

			require.Equal(t, email, auditLog.Actor.ID)
			require.Equal(t, model.DriverObject, auditLog.Object.ObjectType)
			require.Equal(t, updateReq.Firstname.ValueOrZero(), driverAfterUpdate.Firstname.String())
			require.Equal(t, updateReq.Phone.ValueOrZero(), driverAfterUpdate.Phone.String())
			require.Equal(t, "**********", driverBeforeUpdate.Phone.String())

			return nil
		})

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual model.Driver
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, updateReq.Firstname.ValueOrZero(), actual.Firstname.String())
		require.Equal(tt, updateReq.Lastname.ValueOrZero(), actual.Lastname.String())
		require.Equal(tt, updateReq.Phone.ValueOrZero(), actual.Phone.String())
		require.Equal(tt, updateReq.CitizenID.ValueOrZero(), actual.CitizenID.String())
		require.Equal(tt, updateReq.DriverRole.ValueOrZero(), string(actual.DriverRole))
	})

	t.Run("should return 200 when update full-time driver success and generate uobRefID", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		updateReq.DriverType.String = string(model.DriverTypeFullTime)
		updateReq.DriverVendor = null.StringFrom("MOCKED_VENDOR")
		updateReq.ServiceTypes = []model.Service{model.ServiceFood, model.ServiceMart}
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()
		svcArea := model.NewServiceArea("area-1", model.RegionCode(updateReq.Region.ValueOrZero()))
		topUpConfig := model.NewServiceAreaTopUpConfig()
		topUpConfig.GenerateUobRefID = true
		svcArea.SetTopUpConfig(topUpConfig)

		driver := generateDriver(id)
		driver.Banking.UOBRefID = ""

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(driver, nil)

		deps.srvareaRepo.EXPECT().
			GetByRegion(ctx, updateReq.Region.ValueOrZero()).
			Return(svcArea, nil).Times(2)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.driverSvc.EXPECT().
			AssignUobRefToDriver(ctx, gomock.Any()).
			Return(nil)

		deps.svAreaSv.EXPECT().RemoveDriverFromShiftWhitelist(ctx, id, "").Return(nil)
		deps.shiftRepo.EXPECT().Find(ctx, gomock.Any(), 0, 0).Return([]model.Shift{}, 0, nil)

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, auditLog *model.AuditLog) error {
			driverBeforeUpdate := auditLog.Before.(*model.Driver)
			driverAfterUpdate := auditLog.After.(*model.Driver)

			require.Equal(t, email, auditLog.Actor.ID)
			require.Equal(t, model.DriverObject, auditLog.Object.ObjectType)
			require.Equal(t, updateReq.Firstname.ValueOrZero(), driverAfterUpdate.Firstname.String())
			require.Equal(t, updateReq.Phone.ValueOrZero(), driverAfterUpdate.Phone.String())
			require.Equal(t, "**********", driverBeforeUpdate.Phone.String())

			return nil
		})

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual model.Driver
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, updateReq.Firstname.ValueOrZero(), actual.Firstname.String())
		require.Equal(tt, updateReq.Lastname.ValueOrZero(), actual.Lastname.String())
		require.Equal(tt, updateReq.Phone.ValueOrZero(), actual.Phone.String())
		require.Equal(tt, updateReq.CitizenID.ValueOrZero(), actual.CitizenID.String())
		require.Equal(tt, updateReq.DriverRole.ValueOrZero(), string(actual.DriverRole))
	})

	t.Run("should return 400 when update a full-time driver with no vendor", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		updateReq.DriverType.String = string(model.DriverTypeFullTime)
		updateReq.DriverVendor = null.StringFrom("")
		updateReq.ServiceTypes = []model.Service{model.ServiceFood, model.ServiceMart}
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)

		svcArea := model.NewServiceArea("area-1", model.RegionCode(updateReq.Region.ValueOrZero()))
		topUpConfig := model.NewServiceAreaTopUpConfig()
		topUpConfig.GenerateUobRefID = true
		svcArea.SetTopUpConfig(topUpConfig)
		driver := generateDriver(id)

		deps.srvareaRepo.EXPECT().
			GetByRegion(gomock.Any(), updateReq.Region.ValueOrZero()).
			Return(svcArea, nil).Times(1)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), id).
			Return(driver, nil)

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should return 400 when update a full-time driver with invalid service type", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		updateReq.DriverType.String = string(model.DriverTypeFullTime)
		updateReq.DriverVendor = null.StringFrom("MOCKED_VENDOR")
		updateReq.ServiceTypes = []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger}
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)

		svcArea := model.NewServiceArea("area-1", model.RegionCode(updateReq.Region.ValueOrZero()))
		topUpConfig := model.NewServiceAreaTopUpConfig()
		topUpConfig.GenerateUobRefID = true
		svcArea.SetTopUpConfig(topUpConfig)
		driver := generateDriver(id)

		deps.srvareaRepo.EXPECT().
			GetByRegion(gomock.Any(), updateReq.Region.ValueOrZero()).
			Return(svcArea, nil).Times(1)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), id).
			Return(driver, nil)

		driverAPI.UpdateDriver(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})
}

func TestDriverAdminAPI_GetDriver(t *testing.T) {
	makeReq := func(id string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("/v1/admin/drivers/%s", id), nil)
		gctx.Params = gin.Params{
			gin.Param{Key: "driver_id", Value: id},
		}
		return gctx, recorder
	}

	generateBanHistory := func(driverID string) model.BanHistory {
		now := time.Now().UTC()
		next := now.AddDate(0, 0, 7)
		return model.BanHistory{
			DriverID:        driverID,
			Type:            fake.CharactersN(10),
			Action:          fake.CharactersN(10),
			Value:           fake.CharactersN(10),
			Reason:          fake.CharactersN(10),
			Category:        fake.CharactersN(10),
			MessageToDriver: fake.CharactersN(10),
			CreatedBy:       fake.CharactersN(10),
			CreatedAt:       now,
			BannedUntil:     next,
		}
	}

	dummyAttendance := service.AttendanceRate{}

	t.Run("success should return 200", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id := "driver-1"
		profile := generateDriver(id)

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id, gomock.Any()).
			Return(profile, nil)

		deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverRegistration{}, nil)

		deps.banHistorySvc.EXPECT().
			FindHistory(ctx, id, gomock.Any()).
			Return([]model.BanHistory{
				generateBanHistory(id),
				generateBanHistory(id),
			}, nil)

		deps.attendance.EXPECT().
			GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(dummyAttendance, nil)

		deps.riderLevel.EXPECT().
			Get(ctx, id).
			Return(prediction.RiderLevelDefault, nil)

		driverAPI.GetDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual struct {
			Driver struct {
				ID              string   `json:"driverId"`
				RegistrationIDs []string `json:"registrationIds"`
				Goodness        Goodness `json:"goodness"`
			} `json:"driver"`
			Histories []interface{} `json:"histories"`
		}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, id, actual.Driver.ID)
		require.Len(tt, actual.Driver.RegistrationIDs, 2)
		require.Equal(tt, actual.Driver.RegistrationIDs[0], profile.RegistrationIDs[0].Hex())
		require.Len(tt, actual.Histories, 2)
		require.Equal(tt, actual.Driver.Goodness.LevelDefault, prediction.RiderLevelDefault.String())
	})

	t.Run("success should return 200 when migrate on the fly", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id := "driver-1"
		profile := generateDriver(id)

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id, gomock.Any()).
			Return(profile, nil)

		deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverRegistration{{
				ReferrerCode: "REF3",
			}, {
				ReferrerCode: "REF2",
			}, {
				ReferrerCode: "REF1",
			}}, nil)

		deps.driverRepo.EXPECT().SetRefCode(gomock.Any(), gomock.Any(), "REF1").Return(nil)

		deps.banHistorySvc.EXPECT().
			FindHistory(ctx, id, gomock.Any()).
			Return([]model.BanHistory{
				generateBanHistory(id),
				generateBanHistory(id),
			}, nil)

		deps.attendance.EXPECT().
			GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(dummyAttendance, nil)

		deps.riderLevel.EXPECT().
			Get(ctx, id).
			Return(prediction.RiderLevelDefault, nil)

		driverAPI.GetDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("set `NO_TIER` as a default value for a driver tier field", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id := "driver-1"

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id, gomock.Any()).
			Return(generateDriver(id), nil)

		deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverRegistration{}, nil)

		deps.banHistorySvc.EXPECT().
			FindHistory(ctx, id, gomock.Any()).
			Return([]model.BanHistory{
				generateBanHistory(id),
				generateBanHistory(id),
			}, nil)

		deps.attendance.EXPECT().
			GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(dummyAttendance, nil)

		deps.riderLevel.EXPECT().
			Get(ctx, id).
			Return(prediction.RiderLevelDefault, nil)

		driverAPI.GetDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual struct {
			Driver    AdminDriverResponse `json:"driver"`
			Histories []interface{}       `json:"histories"`
		}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, model.DriverTierMember, actual.Driver.DriverTier)
	})

	t.Run("not found should return 404", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id := "driver-1"

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id, gomock.Any()).
			Return(nil, repository.ErrNotFound)

		driverAPI.GetDriver(gctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("backward compatibility for legacy criminal_check_status field", func(t *testing.T) {
		testcases := []struct {
			name                            string
			criminalCheckStatus             model.CriminalStatus
			encryptedCriminalCheckStatusVal model.CriminalStatus
			expected                        model.CriminalStatus
		}{
			{"encryptedCriminalCheckStatus is empty", model.CriminalStatusInReview, "", model.CriminalStatusInReview},
			{"value of both fields are not empty", model.CriminalStatusInReview, model.CriminalStatusNotPass, model.CriminalStatusNotPass},
			{"not error if criminalCheckStatus is removed", "", model.CriminalStatusNotPass, model.CriminalStatusNotPass},
			{"both fields are empty", "", "", model.CriminalStatusPending},
		}

		for _, ts := range testcases {
			t.Run(ts.name, func(t *testing.T) {
				driverAPI, deps, finish := newDriverAdminAPI(t)
				defer finish()

				id := "driver-1"

				gctx, recorder := makeReq(id)
				ctx := gctx.Request.Context()

				mockDriverVal := generateDriver(id)
				mockDriverVal.BaseDriver.EncryptedCriminalCheckStatus = crypt.NewLazyEncryptedString(string(ts.encryptedCriminalCheckStatusVal))
				mockDriverVal.BaseDriver.CriminalCheckStatus = ts.criminalCheckStatus

				deps.driverRepo.EXPECT().
					FindDriverID(ctx, id, gomock.Any()).
					Return(mockDriverVal, nil)

				deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]model.DriverRegistration{}, nil)

				deps.banHistorySvc.EXPECT().
					FindHistory(ctx, id, gomock.Any()).
					Return([]model.BanHistory{
						generateBanHistory(id),
						generateBanHistory(id),
					}, nil)

				deps.attendance.EXPECT().
					GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(dummyAttendance, nil)

				deps.riderLevel.EXPECT().
					Get(ctx, id).
					Return(prediction.RiderLevelDefault, nil)

				driverAPI.GetDriver(gctx)

				var actual struct {
					Driver    AdminDriverResponse `json:"driver"`
					Histories []interface{}       `json:"histories"`
				}
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, ts.expected, actual.Driver.CriminalCheckStatus)
			})
		}
	})

	t.Run("defaultGoodnessLevel", func(tt *testing.T) {
		t.Run("happy case", func(ttt *testing.T) {
			for _, level := range []prediction.RiderLevel{
				prediction.RiderLevelDefault,
				prediction.RiderLevelL1,
				prediction.RiderLevelL2,
				prediction.RiderLevelL3,
				prediction.RiderLevelL4,
				prediction.RiderLevelL5,
				prediction.RiderLevelL6,
				prediction.RiderLevelL7,
				prediction.RiderLevelL8,
				prediction.RiderLevelL9,
				prediction.RiderLevelL10,
			} {
				driverAPI, deps, finish := newDriverAdminAPI(ttt)
				defer finish()

				id := "driver-1"
				profile := generateDriver(id)

				gctx, recorder := makeReq(id)
				ctx := gctx.Request.Context()

				deps.driverRepo.EXPECT().
					FindDriverID(ctx, id, gomock.Any()).
					Return(profile, nil)

				deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]model.DriverRegistration{}, nil)

				deps.banHistorySvc.EXPECT().
					FindHistory(ctx, id, gomock.Any()).
					Return([]model.BanHistory{}, nil)

				deps.attendance.EXPECT().
					GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(dummyAttendance, nil)

				deps.riderLevel.EXPECT().
					Get(ctx, id).
					Return(level, nil)

				driverAPI.GetDriver(gctx)

				require.Equal(ttt, http.StatusOK, recorder.Code)

				var actual struct {
					Driver struct {
						ID              string   `json:"driverId"`
						RegistrationIDs []string `json:"registrationIds"`
						Goodness        Goodness `json:"goodness"`
					} `json:"driver"`
					Histories []interface{} `json:"histories"`
				}

				testutil.DecodeJSON(ttt, recorder.Body, &actual)
				require.Equal(ttt, id, actual.Driver.ID)
				require.Equal(ttt, actual.Driver.Goodness.LevelDefault, level.String())
			}
		})

		t.Run("failure case should use default value", func(ttt *testing.T) {
			driverAPI, deps, finish := newDriverAdminAPI(ttt)
			defer finish()

			id := "driver-1"
			profile := generateDriver(id)

			gctx, recorder := makeReq(id)
			ctx := gctx.Request.Context()

			deps.driverRepo.EXPECT().
				FindDriverID(ctx, id, gomock.Any()).
				Return(profile, nil)

			deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]model.DriverRegistration{}, nil)

			deps.banHistorySvc.EXPECT().
				FindHistory(ctx, id, gomock.Any()).
				Return([]model.BanHistory{}, nil)

			deps.attendance.EXPECT().
				GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(dummyAttendance, nil)

			deps.riderLevel.EXPECT().
				Get(ctx, id).
				Return(prediction.RiderLevel("someBadValue"), errors.New("some error"))

			driverAPI.GetDriver(gctx)

			require.Equal(ttt, http.StatusOK, recorder.Code)

			var actual struct {
				Driver struct {
					ID                string   `json:"driverId"`
					RegistrationIDs   []string `json:"registrationIds"`
					DefaultRiderLevel string   `json:"defaultRiderLevel"`
				} `json:"driver"`
				Histories []interface{} `json:"histories"`
			}
			testutil.DecodeJSON(ttt, recorder.Body, &actual)
			require.Equal(ttt, id, actual.Driver.ID)
			require.Equal(ttt, actual.Driver.DefaultRiderLevel, prediction.RiderLevelDefault.String())
		})
	})

	t.Run("rider level cases", func(t *testing.T) {
		type resp struct {
			Driver struct {
				ID       string `json:"driverId"`
				Goodness struct {
					Level          string `json:"level"`
					LevelExpiredAt string `json:"levelExpiredAt"`
				} `json:"goodness"`
			} `json:"driver"`
		}

		type testFixture struct {
			name             string
			stubGoodness     model.Goodness
			expectRiderLevel func(t *testing.T, recorder *httptest.ResponseRecorder)
		}

		possibleRiderLevels := []prediction.RiderLevel{
			prediction.RiderLevelDefault,
			prediction.RiderLevelL1,
			prediction.RiderLevelL2,
			prediction.RiderLevelL3,
			prediction.RiderLevelL4,
			prediction.RiderLevelL5,
			prediction.RiderLevelL6,
			prediction.RiderLevelL7,
			prediction.RiderLevelL8,
			prediction.RiderLevelL9,
			prediction.RiderLevelL10,
		}

		riderLevelActiveTestcases := []testFixture{}
		for _, v := range possibleRiderLevels {
			v := v
			riderLevelActiveTestcases = append(riderLevelActiveTestcases, testFixture{
				name: fmt.Sprintf("success case when manual set goodness rider_level to %s and active expired_at", string(v)),
				stubGoodness: model.Goodness{
					Level:          v,
					LevelExpiredAt: types.Ptr(time.Date(2024, 2, 25, 16, 59, 59, 999, time.UTC)),
				},
				expectRiderLevel: func(t *testing.T, recorder *httptest.ResponseRecorder) {
					require.Equal(t, http.StatusOK, recorder.Code)
					var actual resp
					testutil.DecodeJSON(t, recorder.Body, &actual)
					require.Equal(t, "driver-1", actual.Driver.ID)
					require.Equal(t, string(v), actual.Driver.Goodness.Level)
					require.Equal(t, "2024-02-25 23:59:59", actual.Driver.Goodness.LevelExpiredAt)
				},
			})
		}

		riderLevelInactiveTestcases := []testFixture{}
		for _, v := range possibleRiderLevels {
			v := v
			riderLevelActiveTestcases = append(riderLevelActiveTestcases, testFixture{
				name: fmt.Sprintf("success case when manual set goodness rider_level to %s and inactive expired_at", string(v)),
				stubGoodness: model.Goodness{
					Level:          v,
					LevelExpiredAt: types.Ptr(time.Date(2024, 2, 12, 16, 59, 59, 999, time.UTC)),
				},
				expectRiderLevel: func(t *testing.T, recorder *httptest.ResponseRecorder) {
					require.Equal(t, http.StatusOK, recorder.Code)
					var actual resp
					testutil.DecodeJSON(t, recorder.Body, &actual)
					require.Equal(t, "driver-1", actual.Driver.ID)
					require.Equal(t, "", actual.Driver.Goodness.Level)
					require.Equal(t, "", actual.Driver.Goodness.LevelExpiredAt)
				},
			})
		}

		exceptionFixtures := []testFixture{
			{
				name:         "success case rider has empty goodness",
				stubGoodness: model.Goodness{},
				expectRiderLevel: func(t *testing.T, recorder *httptest.ResponseRecorder) {
					require.Equal(t, http.StatusOK, recorder.Code)
					var actual resp
					testutil.DecodeJSON(t, recorder.Body, &actual)
					require.Equal(t, "driver-1", actual.Driver.ID)
					require.Equal(t, "", actual.Driver.Goodness.Level)
					require.Equal(t, "", actual.Driver.Goodness.LevelExpiredAt)
				},
			},
			{
				name: "success case rider has a rider level but nil expired_at",
				stubGoodness: model.Goodness{
					Level:          prediction.RiderLevelL10,
					LevelExpiredAt: nil,
				},
				expectRiderLevel: func(t *testing.T, recorder *httptest.ResponseRecorder) {
					require.Equal(t, http.StatusOK, recorder.Code)
					var actual resp
					testutil.DecodeJSON(t, recorder.Body, &actual)
					require.Equal(t, "driver-1", actual.Driver.ID)
					require.Equal(t, "", actual.Driver.Goodness.Level)
					require.Equal(t, "", actual.Driver.Goodness.LevelExpiredAt)
				},
			},
			{
				name: "success case rider has a rider level but active expired_at in UTC and cross midnight",
				stubGoodness: model.Goodness{
					Level:          prediction.RiderLevelL10,
					LevelExpiredAt: types.Ptr(time.Date(2024, 2, 25, 19, 59, 59, 999, time.UTC)),
				},
				expectRiderLevel: func(t *testing.T, recorder *httptest.ResponseRecorder) {
					require.Equal(t, http.StatusOK, recorder.Code)
					var actual resp
					testutil.DecodeJSON(t, recorder.Body, &actual)
					require.Equal(t, "driver-1", actual.Driver.ID)
					require.Equal(t, "Rider_L10", actual.Driver.Goodness.Level)
					require.Equal(t, "2024-02-26 02:59:59", actual.Driver.Goodness.LevelExpiredAt)
				},
			},
		}

		testFixtures := []testFixture{}
		testFixtures = append(testFixtures, riderLevelActiveTestcases...)
		testFixtures = append(testFixtures, riderLevelInactiveTestcases...)
		testFixtures = append(testFixtures, exceptionFixtures...)

		for _, tc := range testFixtures {
			tc := tc
			t.Run(tc.name, func(t *testing.T) {
				driverAPI, deps, finish := newDriverAdminAPI(t)
				defer finish()

				id := "driver-1"
				profile := generateDriver(id)

				gctx, recorder := makeReq(id)
				ctx := gctx.Request.Context()
				ctx = timeutil.NewContextWithTime(ctx, time.Date(2024, 2, 13, 0, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Request = gctx.Request.WithContext(ctx)

				deps.driverRepo.EXPECT().
					FindDriverID(ctx, id, gomock.Any()).
					DoAndReturn(func(ctx context.Context, driverID string, opts ...repository.Option) (*model.Driver, error) {
						newDriver := *profile
						newDriver.Goodness = tc.stubGoodness
						return &newDriver, nil
					}).AnyTimes()

				deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return([]model.DriverRegistration{}, nil)

				deps.banHistorySvc.EXPECT().
					FindHistory(ctx, id, gomock.Any()).
					Return([]model.BanHistory{
						generateBanHistory(id),
						generateBanHistory(id),
					}, nil)

				deps.attendance.EXPECT().
					GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(dummyAttendance, nil)

				deps.riderLevel.EXPECT().
					Get(ctx, id).
					Return(prediction.RiderLevelDefault, nil)

				driverAPI.GetDriver(gctx)

				tc.expectRiderLevel(t, recorder)
			})
		}
	})

	t.Run("success should return 200 with on-top quotas", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id := "driver-1"
		endDate := time.Date(2029, 12, 12, 10, 10, 10, 0, timeutil.BangkokLocation())
		profile := generateDriver(id)
		profile.OnTopQuotas = []model.OnTopQuota{
			{
				OnTopFareID:    "installment-1",
				RemainingQuota: 12,
				MaximumQuota:   15,
				QuotaType:      model.InstallmentOnTopQuotaType,
				EndDate:        endDate,
			},
		}

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id, gomock.Any()).
			Return(profile, nil)

		deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverRegistration{}, nil)

		deps.banHistorySvc.EXPECT().
			FindHistory(ctx, id, gomock.Any()).
			Return([]model.BanHistory{
				generateBanHistory(id),
				generateBanHistory(id),
			}, nil)

		deps.attendance.EXPECT().
			GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(dummyAttendance, nil)

		deps.riderLevel.EXPECT().
			Get(ctx, id).
			Return(prediction.RiderLevelDefault, nil)

		deps.onTopFareSvc.EXPECT().
			GetOnTopByID(gomock.Any(), "installment-1").Return(&model.OnTopFare{
			ID:          "installment-1",
			Title:       "installment-1-title",
			Description: "installment-1-description",
			Name:        "installment-1-name",
			Label:       "installment-1-label",
			Scheme:      model.InstallmentOnTopScheme,
			Status:      model.StatusActive,
			Conditions: []model.OntopCondition{{
				Status: "ACTIVE",
				InstallmentPrice: model.OntopInstallmentPrice{
					MaxOrders: 15,
					Amount:    100,
				},
				ServiceTypes: []model.Service{model.ServiceFood, model.ServiceBike},
			}},
			EndTime:       endDate,
			IsWholeRegion: true,
		}, nil)

		driverAPI.GetDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual struct {
			Driver struct {
				ID              string                          `json:"driverId"`
				RegistrationIDs []string                        `json:"registrationIds"`
				Goodness        Goodness                        `json:"goodness"`
				OnTopQuotas     []AdminDriverOnTopQuotaResponse `json:"onTopQuotas"`
			} `json:"driver"`
			Histories []interface{} `json:"histories"`
		}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, id, actual.Driver.ID)
		require.Equal(tt, "installment-1", actual.Driver.OnTopQuotas[0].OnTopFareID)
		require.Equal(tt, "installment-1-title", actual.Driver.OnTopQuotas[0].Title)
		require.Equal(tt, "installment-1-description", actual.Driver.OnTopQuotas[0].Description)
		require.Equal(tt, 12, actual.Driver.OnTopQuotas[0].RemainingQuota)
		require.Equal(tt, 15, actual.Driver.OnTopQuotas[0].MaximumQuota)
		require.Equal(tt, types.Money(100.00), actual.Driver.OnTopQuotas[0].Amount)
		require.Equal(tt, []model.Service{model.ServiceFood, model.ServiceBike}, actual.Driver.OnTopQuotas[0].ServiceType)
	})

	t.Run("success should return 200 without on-top quota if condition is not set", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id := "driver-1"
		endDate := time.Date(2029, 12, 12, 10, 10, 10, 0, timeutil.BangkokLocation())
		profile := generateDriver(id)
		profile.OnTopQuotas = []model.OnTopQuota{
			{
				OnTopFareID:    "installment-1",
				RemainingQuota: 12,
				MaximumQuota:   15,
				QuotaType:      model.InstallmentOnTopQuotaType,
				EndDate:        endDate,
			},
		}

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id, gomock.Any()).
			Return(profile, nil)

		deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverRegistration{}, nil)

		deps.banHistorySvc.EXPECT().
			FindHistory(ctx, id, gomock.Any()).
			Return([]model.BanHistory{
				generateBanHistory(id),
				generateBanHistory(id),
			}, nil)

		deps.attendance.EXPECT().
			GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(dummyAttendance, nil)

		deps.riderLevel.EXPECT().
			Get(ctx, id).
			Return(prediction.RiderLevelDefault, nil)

		deps.onTopFareSvc.EXPECT().
			GetOnTopByID(gomock.Any(), "installment-1").Return(&model.OnTopFare{
			ID:            "installment-1",
			Title:         "installment-1-title",
			Description:   "installment-1-description",
			Name:          "installment-1-name",
			Label:         "installment-1-label",
			Scheme:        model.InstallmentOnTopScheme,
			Status:        model.StatusActive,
			Conditions:    []model.OntopCondition{},
			EndTime:       endDate,
			IsWholeRegion: true,
		}, nil)

		driverAPI.GetDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual struct {
			Driver struct {
				ID              string                          `json:"driverId"`
				RegistrationIDs []string                        `json:"registrationIds"`
				Goodness        Goodness                        `json:"goodness"`
				OnTopQuotas     []AdminDriverOnTopQuotaResponse `json:"onTopQuotas"`
			} `json:"driver"`
			Histories []interface{} `json:"histories"`
		}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, id, actual.Driver.ID)
		require.Equal(tt, 0, len(actual.Driver.OnTopQuotas))
	})
}

func TestDriverAdminAPI_UpdateDriverRemark(t *testing.T) {
	makeReq := func(id string, update UpdateDriverRemarkReq) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("POST", fmt.Sprintf("/v1/admin/drivers/%s", id), testutil.JSON(&update))
		gctx.Params = gin.Params{
			gin.Param{Key: "driver_id", Value: id},
		}
		return gctx, recorder
	}

	t.Run("success should return 200", func(tt *testing.T) {
		drierAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		expectID, expectRemark := "driver-1", UpdateDriverRemarkReq{
			Remark:    "remark",
			CreatedBy: "createBy",
		}

		gctx, recorder := makeReq(expectID, expectRemark)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, expectID).
			Return(generateDriver(expectID), nil)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, driver *model.Driver) error {
				require.Equal(tt, expectID, driver.DriverID)
				require.Len(tt, driver.Remarks, 1)

				remark := driver.Remarks[0]
				require.Equal(tt, expectRemark.Remark, remark.Remark)
				require.Equal(tt, expectRemark.CreatedBy, remark.CreatedBy)
				return nil
			})

		drierAPI.UpdateDriverRemark(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("not found should return 404", func(tt *testing.T) {
		drierAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		expectID, expectRemark := "driver-1", UpdateDriverRemarkReq{
			Remark:    "remark",
			CreatedBy: "createBy",
		}

		gctx, recorder := makeReq(expectID, expectRemark)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, expectID).
			Return(nil, repository.ErrNotFound)

		drierAPI.UpdateDriverRemark(gctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("invalid request should return 400", func(tt *testing.T) {
		drierAPI, _, finish := newDriverAdminAPI(tt)
		defer finish()

		expectID, expectRemark := "driver-1", UpdateDriverRemarkReq{
			CreatedBy: "createBy",
		}

		gctx, recorder := makeReq(expectID, expectRemark)

		drierAPI.UpdateDriverRemark(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("error no update should return 500", func(tt *testing.T) {
		drierAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		expectID, expectRemark := "driver-1", UpdateDriverRemarkReq{
			Remark:    "remark",
			CreatedBy: "createBy",
		}

		gctx, recorder := makeReq(expectID, expectRemark)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, expectID).
			Return(generateDriver(expectID), nil)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(errors.New("errors"))

		drierAPI.UpdateDriverRemark(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestDriverAdminAPI_UpdateCriminalCheckStatus(t *testing.T) {
	adminId := fake.CharactersN(10)
	email := "<EMAIL>"
	token := fake.CharactersN(20)

	makeReq := func(id string, criminalStatusCheck string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/admin/drivers/%s/criminal_check_status/%s", id, criminalStatusCheck), nil)
		gctx.Params = gin.Params{
			gin.Param{Key: "driver_id", Value: id},
		}
		return gctx, recorder
	}

	t.Run("should return 200 when update PASS status success", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id := fake.CharactersN(8)
		gctx, recorder := makeReq(id, "pass")
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(generateDriver(id), nil)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, auditLog *model.AuditLog) error {
			driverBeforeUpdate := auditLog.Before.(*model.Driver)
			driverAfterUpdate := auditLog.After.(*model.Driver)

			require.Equal(t, email, auditLog.Actor.ID)
			require.Equal(t, model.CriminalStatusPending, driverBeforeUpdate.GetCriminalCheckStatus())
			require.Equal(t, model.CriminalStatusPass, driverAfterUpdate.GetCriminalCheckStatus())

			return nil
		})

		driverAPI.CriminalCheckStatusPass(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should return 200 when update NOT_PASS status success", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id := fake.CharactersN(8)
		gctx, recorder := makeReq(id, "not_pass")
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(generateDriver(id), nil)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, auditLog *model.AuditLog) error {
			driverBeforeUpdate := auditLog.Before.(*model.Driver)
			driverAfterUpdate := auditLog.After.(*model.Driver)

			require.Equal(t, model.CriminalStatusPending, driverBeforeUpdate.GetCriminalCheckStatus())
			require.Equal(t, model.CriminalStatusNotPass, driverAfterUpdate.GetCriminalCheckStatus())

			return nil
		})

		driverAPI.CriminalCheckStatusNotPass(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should return 200 when update WHITELIST status success", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id := fake.CharactersN(8)
		gctx, recorder := makeReq(id, "whitelist")
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(generateDriver(id), nil)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, auditLog *model.AuditLog) error {
			driverBeforeUpdate := auditLog.Before.(*model.Driver)
			driverAfterUpdate := auditLog.After.(*model.Driver)

			require.Equal(t, email, auditLog.Actor.ID)
			require.Equal(t, model.CriminalStatusPending, driverBeforeUpdate.GetCriminalCheckStatus())
			require.Equal(t, model.CriminalStatusWhitelist, driverAfterUpdate.GetCriminalCheckStatus())

			return nil
		})

		driverAPI.CriminalCheckStatusWhiteList(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestDriverAdminAPI_BulkUpdateDriverCriminalStatus(t *testing.T) {
	makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/bulk/drivers/criminal_check_status")
		gctx.Body().MultipartForm().File("file", "file.csv", content).Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	expectFindDriverIDs := func(repo *mock_repository.MockDriverRepository, idToStatus map[string]model.CriminalStatus) {
		drivers := make([]model.Driver, 0)
		for id, status := range idToStatus {
			d := model.Driver{
				DriverID: id,
				BaseDriver: model.BaseDriver{
					CriminalCheckStatus: status,
				},
			}
			drivers = append(drivers, d)
		}
		repo.EXPECT().
			FindDriverIDs(gomock.Any(), gomock.Any()).
			Return(drivers, nil)
	}

	expectUpdateCriminal := func(repo *mock_repository.MockDriverRepository) {
		repo.EXPECT().
			UpdateCriminalStatus(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil).
			AnyTimes()
	}

	t.Run("all success", func(tt *testing.T) {
		api, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		content := `Driver ID,Name,Family Name,National ID,Criminal Result
test-1,tester1,line man,*********012,PASS
test-2,tester2,line man,*********012,PASS
test-3,tester3,line man,*********012,PASS
`
		gctx, recorder := makeReq(content)
		expectFindDriverIDs(deps.driverRepo, map[string]model.CriminalStatus{
			"test-1": model.CriminalStatusPass,
			"test-2": model.CriminalStatusPass,
			"test-3": model.CriminalStatusPass,
		})

		expectUpdateCriminal(deps.driverRepo)

		api.BulkUpdateDriverCriminalStatus(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
		var res BulkUpdateResp
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.Len(tt, res.Successes, 3)
		require.Len(tt, res.Failures, 0)
	})

	t.Run("partial pass because invalid transition", func(tt *testing.T) {
		api, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		content := `Driver ID,Name,Family Name,National ID,Criminal Result
test-1,tester1,line man,*********012,PASS
test-2,tester2,line man,*********012,NOT_PASS
test-3,tester3,line man,*********012,IN_REVIEW
`
		gctx, recorder := makeReq(content)
		expectFindDriverIDs(deps.driverRepo, map[string]model.CriminalStatus{
			"test-1": model.CriminalStatusPass,
			"test-2": model.CriminalStatusPass,
			"test-3": model.CriminalStatusPass,
		})

		expectUpdateCriminal(deps.driverRepo)

		api.BulkUpdateDriverCriminalStatus(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
		var res BulkUpdateResp
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.Len(tt, res.Successes, 2)
		require.Len(tt, res.Failures, 1)
	})

	t.Run("partial pass becase driver not found", func(tt *testing.T) {
		api, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		content := `Driver ID,Name,Family Name,National ID,Criminal Result
test-1,tester1,line man,*********012,PASS
test-2,tester2,line man,*********012,NOT_PASS
test-3,tester3,line man,*********012,PASS
`
		gctx, recorder := makeReq(content)
		expectFindDriverIDs(deps.driverRepo, map[string]model.CriminalStatus{
			"test-1": model.CriminalStatusPending,
			"test-3": model.CriminalStatusPending,
		})

		expectUpdateCriminal(deps.driverRepo)

		api.BulkUpdateDriverCriminalStatus(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
		var res BulkUpdateResp
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.Len(tt, res.Successes, 2)
		require.Len(tt, res.Failures, 1)
	})

	t.Run("all fail", func(tt *testing.T) {
		api, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		content := `Driver ID,Name,Family Name,National ID,Criminal Result
test-1,tester1,line man,*********012,WHITELIST
test-2,tester2,line man,*********012,WHITELIST
test-3,tester3,line man,*********012,WHITELIST
`
		gctx, recorder := makeReq(content)
		expectFindDriverIDs(deps.driverRepo, map[string]model.CriminalStatus{
			"test-1": model.CriminalStatusPass,
			"test-2": model.CriminalStatusPass,
			"test-3": model.CriminalStatusPass,
		})

		api.BulkUpdateDriverCriminalStatus(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
		var res BulkUpdateResp
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.Len(tt, res.Successes, 0)
		require.Len(tt, res.Failures, 3)
	})

	t.Run("invalid csv return 400", func(tt *testing.T) {
		api, _, finish := newDriverAdminAPI(tt)
		defer finish()

		content := `Driver ID,Name,Family Name,Criminal Result,Driver Status
test-1,tester1,line man,PASS,ONLINE
test-2,tester2,line man,NOT_PASS,ONLINE
test-3,tester3,line man,IN_REVIEW,ONLINE
`
		gctx, recorder := makeReq(content)
		api.BulkUpdateDriverCriminalStatus(gctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})
}

func TestDriverAdminAPI_CompleteProfileStatus(t *testing.T) {
	adminId := fake.CharactersN(10)
	email := "<EMAIL>"
	token := fake.CharactersN(20)

	makeReq := func(id string, req UpdateProfileStatusRequest) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("POST", fmt.Sprintf("/v1/admin/drivers/%s/profile/completed", id), testutil.JSON(&req))
		gctx.Params = gin.Params{
			gin.Param{Key: "driver_id", Value: id},
		}
		return gctx, recorder
	}

	createValidReq := func() UpdateProfileStatusRequest {
		return UpdateProfileStatusRequest{
			Reason: "Complete Profile Status",
		}
	}

	t.Run("should return 200 when complete profile status success", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		id, updateReq := fake.CharactersN(8), createValidReq()
		gctx, recorder := makeReq(id, updateReq)
		setUserIdToGctx(gctx, token, adminId, email)
		ctx := gctx.Request.Context()

		d := generateDriver(id)
		d.Banking.AccountHolder = crypt.NewLazyEncryptedString("นาย ขยัน ร่ำรวย")
		d.Banking.BankName = crypt.NewLazyEncryptedString("SCB")
		d.Banking.Account = crypt.NewLazyEncryptedString("*********")
		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id).
			Return(d, nil)

		deps.driverRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		driverAPI.CompleteProfileStatus(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestDriverAdminAPI_BulkUpdateDriverRegion(t *testing.T) {
	makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/bulk/region-drivers")
		gctx.Body().MultipartForm().File("file", "file.csv", content).Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	t.Run("all success", func(tt *testing.T) {
		api, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		koratServiceArea := model.NewServiceArea("TEST_ID_1", "KORAT")
		phuketServiceArea := model.NewServiceArea("TEST_ID_2", "PHUKET")

		topUpConfig := model.NewServiceAreaTopUpConfig()
		topUpConfig.GenerateUobRefID = true
		phuketServiceArea.SetTopUpConfig(topUpConfig)

		expectDriver4 := &model.Driver{
			DriverID: "test-4",
		}
		expectDriver4.Banking.UOBRefID = "UOB_REF_ID"

		expectDriver5 := &model.Driver{
			DriverID: "test-5",
		}

		content := `Driver ID, Region
test-1, BKK
test-2, NON
test-3, NAKORN
test-4, KORAT
test-5, PHUKET
`
		gctx, recorder := makeReq(content)
		deps.srvareaRepo.EXPECT().
			GetByRegion(gomock.Any(), "BKK").
			Return(&model.ServiceArea{}, nil)
		deps.srvareaRepo.EXPECT().
			GetByRegion(gomock.Any(), "NON").
			Return(&model.ServiceArea{}, nil)
		deps.srvareaRepo.EXPECT().
			GetByRegion(gomock.Any(), "NAKORN").
			Return(&model.ServiceArea{}, nil)
		deps.srvareaRepo.EXPECT().
			GetByRegion(gomock.Any(), "KORAT").
			Return(koratServiceArea, nil)
		deps.srvareaRepo.EXPECT().
			GetByRegion(gomock.Any(), "PHUKET").
			Return(phuketServiceArea, nil)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), "test-1").
			Return(&model.Driver{DriverID: "test-1"}, nil)
		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), "test-2").
			Return(&model.Driver{DriverID: "test-2"}, nil)
		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), "test-3").
			Return(&model.Driver{DriverID: "test-3"}, nil)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), "test-4").
			Return(expectDriver4, nil)
		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), "test-5").
			Return(expectDriver5, nil)

		deps.driverRepo.EXPECT().
			Update(gomock.Any(), gomock.Any()).
			Return(nil).Times(5)

		deps.driverSvc.EXPECT().
			UnAssignUobRefFromDriver(gomock.Any(), expectDriver4).
			DoAndReturn(func(ctx context.Context, driver *model.Driver) error {
				require.Equal(tt, "test-4", driver.DriverID)
				require.Equal(tt, "UOB_REF_ID", driver.DriverUOBRefID())
				return nil
			})

		deps.driverSvc.EXPECT().
			AssignUobRefToDriver(gomock.Any(), expectDriver5).
			DoAndReturn(func(ctx context.Context, driver *model.Driver) error {
				require.Equal(tt, "test-5", driver.DriverID)
				require.Equal(tt, "", driver.DriverUOBRefID())
				return nil
			})

		api.BulkUpdateDriverRegion(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
		var res BulkUpdateDriverRegionResp
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.Len(tt, res.Successes, 5)
		require.Len(tt, res.Failures, 0)
	})

	t.Run("Continue process with some driver failure", func(tt *testing.T) {
		api, deps, finish := newDriverAdminAPI(tt)
		defer finish()

		content := `Driver ID, Region
test-1,bkk
test-2,non
test-3,nakorn
`
		gctx, recorder := makeReq(content)
		deps.srvareaRepo.EXPECT().
			GetByRegion(gomock.Any(), gomock.Any()).
			Return(&model.ServiceArea{}, nil).
			Times(3)
		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, driverId string, opts ...repository.Option) (*model.Driver, error) {
				return &model.Driver{
					DriverID: driverId,
				}, nil
			}).
			Times(2)
		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{}, errors.New("not found"))
		deps.driverRepo.EXPECT().
			Update(gomock.Any(), gomock.Any()).
			Return(nil).Times(2)

		api.BulkUpdateDriverRegion(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
		var res BulkUpdateDriverRegionResp
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.Len(tt, res.Successes, 2)
		require.Len(tt, res.Failures, 1)
	})

	t.Run("invalid csv return 400", func(tt *testing.T) {
		api, _, finish := newDriverAdminAPI(tt)
		defer finish()

		content := `Driver ID, Region
driver-1, bkk, invalid field 
driver-2, non
`
		gctx, recorder := makeReq(content)
		api.BulkUpdateDriverRegion(gctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})
}

func TestDriverAdminAPI_UpdateDriverStatus(t *testing.T) {
	makeReq := func(id string, status model.DriverStatus) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/admin/drivers/%s/status/%s", id, status), nil)
		gctx.Params = gin.Params{
			gin.Param{Key: "driver_status", Value: string(status)},
		}
		return gctx, recorder
	}

	t.Run("should set driver last attempt when restore driver status to ONLINE", func(tt *testing.T) {
		api, deps, finish := newDriverAdminAPI(tt)
		api.OrderConfig.SetDriverLastAttemptWhenUpdateStatusOnlineEnabled = true
		defer finish()

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), gomock.Any()).Return(&model.Driver{}, nil)
		deps.driverRepo.EXPECT().SetCurrentStatus(gomock.Any(), gomock.Any(), model.StatusOnline, gomock.Any()).Return(nil)
		deps.assigner.EXPECT().UnAssignTrip(gomock.Any(), gomock.Any()).Return(&model.Driver{}, nil)
		deps.rep.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(nil)
		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.driverSvc.EXPECT().UpdateDriverLastAttempt(gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepo.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		gctx, recorder := makeReq("driverID", model.StatusOnline)
		safedGctx := safe.CreateWaitGroupOnGctx(gctx)
		api.UpdateDriverStatus(gctx)
		safedGctx.Wait()

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})

	t.Run("should return 204 when restore driver status set driver last attempt error", func(tt *testing.T) {
		api, deps, finish := newDriverAdminAPI(tt)
		api.OrderConfig.SetDriverLastAttemptWhenUpdateStatusOnlineEnabled = true
		defer finish()

		deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), gomock.Any()).Return(&model.Driver{}, nil)
		deps.driverRepo.EXPECT().SetCurrentStatus(gomock.Any(), gomock.Any(), model.StatusOnline, gomock.Any()).Return(nil)
		deps.assigner.EXPECT().UnAssignTrip(gomock.Any(), gomock.Any()).Return(&model.Driver{}, nil)
		deps.rep.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(nil)
		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.driverSvc.EXPECT().UpdateDriverLastAttempt(gomock.Any(), gomock.Any()).Return(errors.New("error"))
		deps.driverRepo.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		gctx, recorder := makeReq("driverID", model.StatusOnline)
		safedGctx := safe.CreateWaitGroupOnGctx(gctx)
		api.UpdateDriverStatus(gctx)
		safedGctx.Wait()

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})
}

func TestDriverAdminAPI_BulkUpdateDriverFinancialRiskControl(t *testing.T) {
	makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/admin/bulk/drivers/financial-risk-control")
		gctx.Body().MultipartForm().
			File("file", "file.csv", content).
			Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	nextMonth := timeutil.ToYYYYMMDD(timeutil.BangkokNow().AddDate(0, 1, 0))

	t.Run("should update driver dscr, dscr effective date, exposure and tenor", func(tt *testing.T) {
		api, deps, finish := newDriverAdminAPI(tt)
		api.OrderConfig.SetDriverLastAttemptWhenUpdateStatusOnlineEnabled = true
		defer finish()

		deps.driverRepo.EXPECT().
			FindOneAndSetField(gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, query repository.DriverQuery, updator repository.DriverUpdator) error {
				driverQuery, ok := query.(*persistence.MongoDriverQuery)
				if !ok {
					t.Fail()
				}

				driverUpdator, ok := updator.(*persistence.MongoDriverUpdator)
				if !ok {
					t.Fail()
				}

				payload := bson.M(*driverUpdator)
				setPayload := payload["$set"].(bson.M)
				pushPayload := payload["$push"].(bson.M)

				require.Equal(t, "LMD000000", util.Must(driverQuery.Query())["driver_id"])
				require.Equal(t, 15.00, setPayload["dscr"])
				require.Equal(t, 15, setPayload["max_tenor"])
				require.Equal(t, 100.00, setPayload["max_exposure"])
				require.Equal(t, fmt.Sprintf("Update driver DSCR to 15, DSCR Effective Date to (%s, %s), Max tenor to 15, Max exposure to 100", nextMonth, nextMonth), pushPayload["remarks"].(model.DriverRemark).Remark)

				return nil
			})

		gctx, recorder := makeReq(fmt.Sprintf("Driver ID,DSCR,DSCR Effective Date Start At,DSCR Effective Date End At,Max Tenor,Max Exposure\r\nLMD000000,15.00,%s,%s,15,100.00", nextMonth, nextMonth))

		safedGctx := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkUpdateDriverFinancialRiskControl(gctx)
		safedGctx.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

type driverAPIDeps struct {
	driverRepo                   *mock_repository.MockDriverRepository
	driverSvc                    *mock_service.MockDriverServiceInterface
	assigner                     *mock_service.MockAssigner
	srvareaRepo                  *mock_repository.MockServiceAreaRepository
	banHistorySvc                *mock_repository.MockBanHistoryRepository
	auditLogRepo                 *mock_repository.MockAuditLogRepository
	rep                          *mock_rep.MockREPService
	cache                        *mock_redis.MockUniversalClient
	driverRegisRepo              *mock_repository.MockDriverRegistrationRepository
	noti                         *mock_service.MockNotifier
	attendance                   *mock_service.MockAttendances
	svAreaSv                     *mock_service.MockServiceAreaService
	shiftRepo                    *mock_repository.MockShiftRepository
	orderRepo                    *mock_repository.MockOrderRepository
	dedicatedZoneRepo            *mock_repository.MockDedicatedZoneRepository
	driverUpdateLocationEventSvc *mock_service.MockDriverUpdateLocationEventService
	riderLevel                   *mock_riderlevel.MockRiderLevelConnector
	simpleUnleasher              *unleashtest.SimpleUnleasher
	unleashAdmin                 *mock_unleash.MockAdmin
	onTopFareSvc                 *mock_service.MockOnTopFareService
}

func newDriverAdminAPI(r gomock.TestReporter) (*DriverAdminAPI, driverAPIDeps, func()) {
	ctrl := gomock.NewController(r)

	deps := driverAPIDeps{
		driverRepo:                   mock_repository.NewMockDriverRepository(ctrl),
		driverSvc:                    mock_service.NewMockDriverServiceInterface(ctrl),
		assigner:                     mock_service.NewMockAssigner(ctrl),
		srvareaRepo:                  mock_repository.NewMockServiceAreaRepository(ctrl),
		banHistorySvc:                mock_repository.NewMockBanHistoryRepository(ctrl),
		auditLogRepo:                 mock_repository.NewMockAuditLogRepository(ctrl),
		rep:                          mock_rep.NewMockREPService(ctrl),
		cache:                        mock_redis.NewMockUniversalClient(ctrl),
		driverRegisRepo:              mock_repository.NewMockDriverRegistrationRepository(ctrl),
		noti:                         mock_service.NewMockNotifier(ctrl),
		attendance:                   mock_service.NewMockAttendances(ctrl),
		svAreaSv:                     mock_service.NewMockServiceAreaService(ctrl),
		shiftRepo:                    mock_repository.NewMockShiftRepository(ctrl),
		orderRepo:                    mock_repository.NewMockOrderRepository(ctrl),
		dedicatedZoneRepo:            mock_repository.NewMockDedicatedZoneRepository(ctrl),
		driverUpdateLocationEventSvc: mock_service.NewMockDriverUpdateLocationEventService(ctrl),
		riderLevel:                   mock_riderlevel.NewMockRiderLevelConnector(ctrl),
		simpleUnleasher:              unleashtest.ProvideSimpleUnleasher(),
		unleashAdmin:                 mock_unleash.NewMockAdmin(ctrl),
		onTopFareSvc:                 mock_service.NewMockOnTopFareService(ctrl),
	}

	financialRiskConfig := FinancialRiskConfig{
		FinancialRiskMinExposure: 0,
		FinancialRiskMaxExposure: 100000,
		FinancialRiskMinTenor:    15,
		FinancialRiskMaxTenor:    300,
	}

	return ProvideDriverAdminAPI(
		deps.driverRepo,
		deps.driverSvc,
		deps.assigner,
		deps.srvareaRepo,
		deps.banHistorySvc,
		deps.auditLogRepo,
		deps.rep,
		deps.cache,
		deps.driverRegisRepo,
		deps.noti,
		deps.attendance,
		deps.svAreaSv,
		deps.shiftRepo,
		deps.orderRepo,
		deps.dedicatedZoneRepo,
		OrderConfig{},
		deps.driverUpdateLocationEventSvc,
		financialRiskConfig,
		deps.riderLevel,
		featureflag.NewFeatureFlagService(deps.simpleUnleasher, deps.unleashAdmin),
		deps.onTopFareSvc,
	), deps, func() { ctrl.Finish() }
}

func init() {
	binding.Validator = apiValidator.NewDefaultValidator()
}
