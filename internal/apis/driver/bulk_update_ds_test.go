package driver

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBulkUpdateDS_EveryFuncsWorksOnZeroValue(t *testing.T) {
	t.Run("Add", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.NotPanics(t, func() {
			ds.Add("", "")
		})
	})
	t.Run("LenDrivers", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.NotPanics(t, func() {
			ds.LenDriverIDs()
		})
	})
	t.Run("Remove", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.NotPanics(t, func() {
			ds.Remove("1")
		})
	})
	t.Run("DriverIDs", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.NotPanics(t, func() {
			ds.DriverIDs()
		})
	})
	t.Run("Values", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.NotPanics(t, func() {
			ds.Values()
		})
	})
	t.Run("DriverIDsWithValue", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.NotPanics(t, func() {
			ds.DriverIDsWithValue("")
		})
	})
	t.Run("ValueOfDriverID", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.NotPanics(t, func() {
			ds.ValueOfDriverID("")
		})
	})
}

func TestBulkUpdateDS_Add(t *testing.T) {
	t.Run("Add increases len by 1 if and only if the id is new", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		assert.Equal(t, 1, ds.LenDriverIDs())
		ds.Add("1", "B")
		assert.Equal(t, 1, ds.LenDriverIDs())
		ds.Add("2", "A")
		assert.Equal(t, 2, ds.LenDriverIDs())
	})
}

func TestBulkUpdateDS_LenDriverIDs(t *testing.T) {
	t.Run("returns zero if zero value", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.Equal(t, 0, ds.LenDriverIDs())
	})

	t.Run("returns zero after adding one entry and removing that entry", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("", "")
		ds.Remove("")
		assert.Equal(t, 0, ds.LenDriverIDs())
	})

	t.Run("doesn't change when removing non existent id", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "")
		ds.Remove("2")
		assert.Equal(t, 1, ds.LenDriverIDs())
	})
}

func TestBulkUpdateDS_Remove(t *testing.T) {
	t.Run("no op if value doesn't exist", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		ds.Add("2", "B")
		ds.Remove("3")

		expect := BulkUpdateDS{}
		expect.Add("1", "A")
		expect.Add("2", "B")
		assert.Equal(t, expect, ds)
	})
}

func TestBulkUpdateDS_DriverIDs(t *testing.T) {
	t.Run("returns empty string slice when len is 0", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.Equal(t, []string{}, ds.DriverIDs())

		ds.Add("", "")
		ds.Remove("")
		assert.Equal(t, []string{}, ds.DriverIDs())
	})

	t.Run("returns single driverID correctly", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("", "")
		assert.ElementsMatch(t, ds.DriverIDs(), []string{""})
	})

	t.Run("returns all ids across statuses", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		ds.Add("2", "B")
		assert.ElementsMatch(t, ds.DriverIDs(), []string{"1", "2"})
	})

	t.Run("doesn't include id of removed entry", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		ds.Add("2", "B")
		ds.Remove("1")
		assert.ElementsMatch(t, ds.DriverIDs(), []string{"2"})
	})

	t.Run("adding duplicated id doesn't change the return value", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		ds.Add("1", "B")
		assert.ElementsMatch(t, ds.DriverIDs(), []string{"1"})
	})
}

func TestBulkUpdateDS_Statuses(t *testing.T) {
	t.Run("returns empty slice on zero value", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.Equal(t, []interface{}{}, ds.Values())
	})

	t.Run("returns empty slice when remove the last entry with the last status", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("some id", "A")
		ds.Remove("some id")
		assert.Equal(t, []interface{}{}, ds.Values())
	})

	t.Run("if there is only one id with status A, adding the id again with status B also removes status A", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("some id", "A")
		ds.Add("some id", "B")
		assert.ElementsMatch(t, ds.Values(), []interface{}{"B"})
	})

	t.Run("returns unique statuses", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		ds.Add("2", "A")
		ds.Add("3", "B")
		assert.ElementsMatch(t, ds.Values(), []interface{}{"A", "B"})
	})
}

func TestBulkUpdateDS_DriverIDsOfStatus(t *testing.T) {
	t.Run("returns empty slice on zero value", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.Equal(t, []string{}, ds.DriverIDsWithValue(""))
	})

	t.Run("returns empty slice if there's no id with such status", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		assert.Equal(t, []string{}, ds.DriverIDsWithValue("B"))
	})

	t.Run("correctly returns ids with the status", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		ds.Add("2", "B")
		ds.Add("3", "A")
		assert.ElementsMatch(t, ds.DriverIDsWithValue("A"), []string{"1", "3"})
	})

	t.Run("doesn't return removed id", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		ds.Add("2", "A")
		ds.Remove("1")
		assert.ElementsMatch(t, ds.DriverIDsWithValue("A"), []string{"2"})
		ds.Remove("2")
		assert.ElementsMatch(t, ds.DriverIDsWithValue("A"), []string{})
	})

	t.Run("still returns correctly after removing the last id with the status and adding again", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		ds.Remove("1")
		ds.Add("1", "A")
		assert.ElementsMatch(t, ds.DriverIDsWithValue("A"), []string{"1"})
	})

	t.Run("doesn't return an id that is added with different status", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		ds.Add("1", "B")
		assert.ElementsMatch(t, ds.DriverIDsWithValue("A"), []string{})
	})
}

func TestBulkUpdateDS_StatusOfDriverID(t *testing.T) {
	t.Run("returns zero value on non-existent id", func(t *testing.T) {
		ds := BulkUpdateDS{}
		assert.Equal(t, nil, ds.ValueOfDriverID(""))
	})

	t.Run("returns the latest status of the id", func(t *testing.T) {
		ds := BulkUpdateDS{}
		ds.Add("1", "A")
		ds.Add("1", "B")
		assert.Equal(t, "B", ds.ValueOfDriverID("1"))
	})
}
