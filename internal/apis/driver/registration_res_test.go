package driver

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func TestNewRegistrationRes(t *testing.T) {
	now := time.Now().UTC()
	vehicle := model.VehicleInfo{
		RegistrationDate:       &now,
		RegistrationPhotoURL:   "fleet_private/registration/5eec5f0b2999c800016d2d59/vehicle_registration__a1643710e57448b181d42c50614d9277",
		PlateNumber:            crypt.NewLazyEncryptedString("1234"),
		PhotoURL:               "fleet_private/registration/5eec5f0b2999c800016d2d59/vehicle__4b2876cce023452db76c6b65e5a605c1",
		LegislationPhotoURL:    "legislation_photo",
		LendingVehiclePhotoURL: "lending_vehicle_photo",
	}
	address := model.Address{
		HouseNumber: crypt.NewLazyEncryptedString("123"),
		Moo:         crypt.NewLazyEncryptedString("12"),
		Subdistrict: crypt.NewLazyEncryptedString("subdistrict_foo"),
		District:    crypt.NewLazyEncryptedString("district_bar"),
		Province:    crypt.NewLazyEncryptedString("หยุดยา"),
		Zipcode:     crypt.NewLazyEncryptedString("12345"),
	}

	citiRefID := crypt.NewLazyEncryptedString("12345")
	banking := model.BankingInfo{
		Account:       crypt.NewLazyEncryptedString("1234567"),
		AccountHolder: crypt.NewLazyEncryptedString("foo"),
		BankName:      crypt.NewLazyEncryptedString("abc"),
		RefID:         citiRefID,
		CitiRefID:     citiRefID.String(),
		PhotoURL:      "fleet_private/registration/5eec5f0b2999c800016d2d59/book_bank__ea6fbd9c54f14ce4bc1d9aee9df4e697",
		BranchCode:    crypt.NewLazyEncryptedString("7890"),
	}

	driverlicense := model.DriverLicenseInfo{
		ID:             crypt.NewLazyEncryptedString("*********"),
		ExpirationDate: &now,
		PhotoURL:       "fleet_private/registration/5eec5f0b2999c800016d2d59/driver_license__9763916a417a4e92acb0c118bbf0fc3d",
	}

	baseDriver := model.BaseDriver{
		Title:                  crypt.NewLazyEncryptedString("นาย"),
		Firstname:              crypt.NewLazyEncryptedString("รัก"),
		Lastname:               crypt.NewLazyEncryptedString("ดี"),
		Phone:                  crypt.NewLazyEncryptedString("**********"),
		EmergencyPhone:         crypt.NewLazyEncryptedString("**********"),
		StrongEmergencyPhone:   crypt.NewStrongEncryptedString("**********"),
		AvatarURL:              "https://line-objects-dev.com/lineman/fleet/registration/5eec5f0b2999c800016d2d59/avatar__520fd61bbd0e4380846b0f69b09cfcbc",
		CitizenID:              crypt.NewLazyEncryptedString("*************"),
		CitizenIDCardPhotoURL:  "fleet_private/registration/5eec5f0b2999c800016d2d59/citizen_id__3bce0c3f5f0b48f89c2e603488962529",
		CitizenIDExpiredDate:   &now,
		Birthday:               &now,
		CanExceedAgeLimit:      false,
		DriverType:             crypt.NewLazyEncryptedString("foo"),
		LineUID:                crypt.NewLazyEncryptedString("test123"),
		Address:                address,
		DriverLicense:          driverlicense,
		Vehicle:                vehicle,
		Banking:                banking,
		Trained:                false,
		InterestingProvince:    "หยุดยา",
		AcceptedConsentVersion: 1,
		CreatedAt:              now,
		UpdatedAt:              now,
	}
	m := model.DriverRegistration{
		BaseDriver: baseDriver,
	}

	r := NewRegistrationRes(m)

	require.Equal(t, r.Title, m.BaseDriver.Title.String())
	require.Equal(t, r.Firstname, m.BaseDriver.Firstname.String())
	require.Equal(t, r.Lastname, m.BaseDriver.Lastname.String())
	require.Equal(t, r.Phone, m.BaseDriver.Phone.String())
	require.Equal(t, r.EmergencyPhone, m.BaseDriver.EmergencyPhone.String())
	require.Equal(t, r.StrongEmergencyPhone, m.BaseDriver.StrongEmergencyPhone.String())
	require.Equal(t, r.AvatarURL, m.BaseDriver.AvatarURL)
	require.Equal(t, r.CitizenID, m.BaseDriver.CitizenID.String())
	require.Equal(t, r.CitizenIDCardPhotoURL, m.BaseDriver.CitizenIDCardPhotoURL)
	require.Equal(t, r.CitizenIDExpiredDate, m.BaseDriver.CitizenIDExpiredDate)
	require.Equal(t, r.IsDuplicatedCitizenID, m.IsDuplicatedCitizenID)
	require.Equal(t, r.IsDriverDuplicatedCitizenID, m.IsDriverDuplicatedCitizenID)

	require.Equal(t, r.Address.HouseNumber, m.BaseDriver.Address.HouseNumber.String())
	require.Equal(t, r.Address.Moo, m.BaseDriver.Address.Moo.String())
	require.Equal(t, r.Address.Subdistrict, m.BaseDriver.Address.Subdistrict.String())
	require.Equal(t, r.Address.District, m.BaseDriver.Address.District.String())
	require.Equal(t, r.Address.Province, m.BaseDriver.Address.Province.String())
	require.Equal(t, r.Address.Zipcode, m.BaseDriver.Address.Zipcode.String())

	require.Equal(t, r.DriverLicense.ID, m.BaseDriver.DriverLicense.ID.String())
	require.Equal(t, r.DriverLicense.PhotoURL, m.BaseDriver.DriverLicense.PhotoURL)
	require.NotEmpty(t, r.DriverLicense.ExpirationDate)

	require.Equal(t, r.Vehicle.PhotoURL, m.Vehicle.PhotoURL)
	require.Equal(t, r.Vehicle.PlateNumber, m.Vehicle.PlateNumber.String())
	require.Equal(t, r.Vehicle.RegistrationPhotoURL, m.Vehicle.RegistrationPhotoURL)
	require.Equal(t, r.Vehicle.LegislationPhotoURL, m.Vehicle.LegislationPhotoURL)
	require.Equal(t, r.Vehicle.LendingVehiclePhotoURL, m.Vehicle.LendingVehiclePhotoURL)
	require.NotEmpty(t, r.Vehicle.RegistrationDate, m.Vehicle.RegistrationDate)

	require.Equal(t, r.Banking.Account, m.BaseDriver.Banking.Account.String())
	require.Equal(t, r.Banking.AccountHolder, m.BaseDriver.Banking.AccountHolder.String())
	require.Equal(t, r.Banking.PhotoURL, m.BaseDriver.Banking.PhotoURL)
	require.Equal(t, r.Banking.BankName, m.BaseDriver.Banking.BankName.String())

	require.Equal(t, r.InterestingProvince, m.BaseDriver.InterestingProvince)
	require.Equal(t, r.AcceptedConsentVersion, m.BaseDriver.AcceptedConsentVersion)
	require.Equal(t, r.Status, string(m.Status))
	require.Equal(t, r.Reason, m.Reason)
	require.Equal(t, r.Trained, m.BaseDriver.Trained)

	require.NotEmpty(t, r.CreatedAt)
	require.NotEmpty(t, r.UpdatedAt)
}

func TestNewRegistrationReviseRes(t *testing.T) {
	now := time.Now().UTC()
	vehicle := model.VehicleInfo{
		PlateNumber: crypt.NewLazyEncryptedString("1234"),
	}

	driverlicense := model.DriverLicenseInfo{
		PhotoURL: "fleet_private/registration/5eec5f0b2999c800016d2d59/driver_license__9763916a417a4e92acb0c118bbf0fc3d",
	}

	baseDriver := model.BaseDriver{
		Firstname:              crypt.NewLazyEncryptedString("รัก"),
		Lastname:               crypt.NewLazyEncryptedString("ดี"),
		Phone:                  crypt.NewLazyEncryptedString("**********"),
		EmergencyPhone:         crypt.NewLazyEncryptedString("**********"),
		StrongEmergencyPhone:   crypt.NewStrongEncryptedString("**********"),
		AvatarURL:              "https://line-objects-dev.com/lineman/fleet/registration/5eec5f0b2999c800016d2d59/avatar__520fd61bbd0e4380846b0f69b09cfcbc",
		CitizenIDCardPhotoURL:  "fleet_private/registration/5eec5f0b2999c800016d2d59/citizen_id__3bce0c3f5f0b48f89c2e603488962529",
		LineUID:                crypt.NewLazyEncryptedString("test123"),
		DriverLicense:          driverlicense,
		Vehicle:                vehicle,
		InterestingProvince:    "หยุดยา",
		AcceptedConsentVersion: 1,
		CreatedAt:              now,
		UpdatedAt:              now,
	}
	m := model.DriverRegistration{
		BaseDriver: baseDriver,
	}

	r := NewRegistrationReviseRes(m, true)

	require.Equal(t, r.Title, m.BaseDriver.Title.String())
	require.Equal(t, r.Firstname, m.BaseDriver.Firstname.String())
	require.Equal(t, r.Lastname, m.BaseDriver.Lastname.String())
	require.Equal(t, r.Phone, m.BaseDriver.Phone.String())
	require.Equal(t, r.EmergencyPhone, m.BaseDriver.EmergencyPhone.String())
	require.Equal(t, r.StrongEmergencyPhone, m.BaseDriver.StrongEmergencyPhone.String())
	require.Equal(t, r.AvatarURL, m.BaseDriver.AvatarURL)
	require.Equal(t, r.CitizenIDCardPhotoURL, m.BaseDriver.CitizenIDCardPhotoURL)

	require.Equal(t, r.DriverLicense.PhotoURL, m.BaseDriver.DriverLicense.PhotoURL)

	require.Equal(t, r.Vehicle.PlateNumber, m.Vehicle.PlateNumber.String())

	require.Equal(t, r.InterestingProvince, m.BaseDriver.InterestingProvince)
	require.Equal(t, r.AcceptedConsentVersion, m.BaseDriver.AcceptedConsentVersion)
	require.Equal(t, r.Status, string(m.Status))
	require.Equal(t, r.Reason, m.Reason)
	require.Equal(t, r.IsApproved, true)

	require.NotEmpty(t, r.CreatedAt)
	require.NotEmpty(t, r.UpdatedAt)
}

func TestNewVehicleRes(t *testing.T) {

	t.Run("should return nil if all is zero value", func(t *testing.T) {
		r := model.VehicleInfo{}
		n := NewVehicleRes(r)
		require.Nil(t, n)
	})
}

func TestNewBankRes(t *testing.T) {
	t.Run("should return nil if all is zero value", func(t *testing.T) {
		r := model.BankingInfo{}
		n := NewBankRes(r)
		require.Nil(t, n)
	})
}

func TestNewDriverLicenseRes(t *testing.T) {
	t.Run("should return nil if all is zero value", func(t *testing.T) {
		r := model.DriverLicenseInfo{}
		n := NewDriverLicenseRes(r)
		require.Nil(t, n)
	})
}
