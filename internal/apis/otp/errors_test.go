package otp

import (
	"testing"
	"time"
)

func TestDurationRemainingInSec(t *testing.T) {
	expDuration := 500
	acceptableError := 5
	due := time.Now().Add(time.Duration(expDuration) * time.Second)
	realDuration := durationRemainingInSec(due)
	if realDuration > expDuration || realDuration < (expDuration-acceptableError) {
		t.<PERSON>rrorf("durationRemainingInSec doesn't work; expected: %v; actual: %v", expDuration, realDuration)
	}
}
