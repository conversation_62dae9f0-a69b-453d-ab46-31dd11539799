//go:build integration_test
// +build integration_test

package incentivesource_test

import (
	"net/http"
	"os"
	"testing"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentivesource"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"github.com/stretchr/testify/require"
)

func TestIncentiveSourceAPI(t *testing.T) {
	t.Run("GET /v1/admin/incentive-sources success (single source)", func(t *testing.T) {
		// Given
		os.Setenv("INCENTIVE_SOURCES", "A")
		defer os.Unsetenv("INCENTIVE_SOURCES")
		ctn := ittest.NewContainer(t)
		ctx := testutil.NewContextWithRecorder()

		//When
		ctx.SetGET("/v1/admin/incentive-sources")
		ctn.GinEngineRouter.HandleContext(ctx.GinCtx())

		//Then
		ctx.AssertResponseCode(t, http.StatusOK)

		var actual incentivesource.GetAllRes
		testutil.DecodeJSON(t, ctx.ResponseRecorder.Body, &actual)
		require.Len(t, actual.Data, 1)
		require.Equal(t, actual.Data[0].Name, "A")
	})

	t.Run("GET /v1/admin/incentive-sources success (multiple sources)", func(t *testing.T) {
		// Given
		os.Setenv("INCENTIVE_SOURCES", "A,B,C")
		defer os.Unsetenv("INCENTIVE_SOURCES")
		ctn := ittest.NewContainer(t)
		ctx := testutil.NewContextWithRecorder()

		//When
		ctx.SetGET("/v1/admin/incentive-sources")
		ctn.GinEngineRouter.HandleContext(ctx.GinCtx())

		//Then
		ctx.AssertResponseCode(t, http.StatusOK)
		var actual incentivesource.GetAllRes
		testutil.DecodeJSON(t, ctx.ResponseRecorder.Body, &actual)
		require.Len(t, actual.Data, 3)
		require.Equal(t, actual.Data[0].Name, "A")
		require.Equal(t, actual.Data[1].Name, "B")
		require.Equal(t, actual.Data[2].Name, "C")
	})
}
