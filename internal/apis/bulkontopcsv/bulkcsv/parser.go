package bulkcsv

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"

	"github.com/jszwec/csvutil"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkontopcsv/shared"
	internalError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
)

var (
	ErrSchemeNotImplemented = errors.New("scheme not implemented")
)

func parseCSVFile(reader io.Reader, schemeType shared.SchemeType) (BulkImportCSVData, error) {
	csvReader := csv.NewReader(reader)

	dec, err := csvutil.NewDecoder(csvReader)
	if err != nil {
		if errors.Is(err, io.EOF) {
			return BulkImportCSVData{}, ErrEmptyFileOrHeader()
		}
		err = apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, fmt.Errorf("create csv decoder error: %w", err))
		return BulkImportCSVData{}, err
	}

	dec.DisallowMissingColumns = true

	var (
		idx            int
		processingData BulkImportCSVData
		dataRow        []BulkImportCSVDataRow
		ids            []string
		merr           = NewCSVMultipleError()
	)

	groupIdToConditionMap := map[string][]interface{}{}
	uniqueIdMap := map[string]FixedColumns{}
	uniqueIDToOptionalColumn := map[string]OptionalColumns{}

	for {
		rowIndex := idx + 1

		i, err := decode(schemeType, dec)
		if err != nil {
			if err == io.EOF {
				break
			}

			if err == ErrSchemeNotImplemented {
				err = apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, fmt.Errorf("scheme %v not implemented", schemeType))
				return BulkImportCSVData{}, err
			}

			var missingColumnsErr *csvutil.MissingColumnsError
			if errors.As(err, &missingColumnsErr) {
				return BulkImportCSVData{}, ErrMissingColumns(missingColumnsErr)
			}

			var invalidDecodeErr *csvutil.InvalidDecodeError
			if errors.As(err, &invalidDecodeErr) {
				err = apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, fmt.Errorf("unable to decode on row %v: %v", rowIndex, err.Error()))
				return BulkImportCSVData{}, err
			}

			merr.AddError(internalError.NewCSVRowError(
				rowIndex,
				"-",
				err.Error(),
			))

			idx++
			continue
		}

		row := BulkImportCSVDataRow{}
		switch v := i.(type) {
		case BulkImportCSVFlatRateSchemeDataRow:
			row.FixedColumns = v.FixedColumns
			row.FlatRateCondition = &v.FlatRateCondition
		case BulkImportCSVBasketSchemeDataRow:
			row.FixedColumns = v.FixedColumns
			row.BasketCondition = &v.BasketCondition
		case BulkImportCSVDistanceSchemeDataRow:
			row.FixedColumns = v.FixedColumns
			row.DistanceCondition = &v.DistanceCondition
		case BulkImportCSVPickupDistanceSchemeDataRow:
			row.FixedColumns = v.FixedColumns
			row.PickupDistanceCondition = &v.PickupDistanceCondition
		case BulkImportCSVInstallmentSchemeDataRow:
			row.FixedColumns = v.FixedColumns
			row.OptionalColumns = &v.OptionalColumns
			row.InstallmentCondition = &v.InstallmentCondition
		}

		if _, exist := groupIdToConditionMap[row.Id]; exist {
			row.MetaData.IsGroupRow = true
			err := addCondition(schemeType, row, groupIdToConditionMap)
			if err != nil {
				err = apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, fmt.Errorf("unable to add condition on row %v: %v", rowIndex, err.Error()))
				return BulkImportCSVData{}, err
			}
		} else {
			err := addCondition(schemeType, row, groupIdToConditionMap)
			if err != nil {
				err = apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, fmt.Errorf("unable to add condition on row %v: %v", rowIndex, err.Error()))
				return BulkImportCSVData{}, err
			}
			uniqueIdMap[row.Id] = row.FixedColumns
			if row.OptionalColumns != nil {
				uniqueIDToOptionalColumn[row.Id] = *row.OptionalColumns
			}
			ids = append(ids, row.Id)
		}

		merr.AddSuccess(SuccessRow{
			Row: rowIndex,
			Id:  row.Id,
		})
		dataRow = append(dataRow, row)
		idx++
	}

	if merr.HasError() {
		return BulkImportCSVData{}, fmt.Errorf("row validation error: %w", merr)
	}

	processingData.RawRows = dataRow
	processingData.SchemeSize = len(groupIdToConditionMap)
	processingData.GroupIdToConditionMap = groupIdToConditionMap
	processingData.UniqueIDs = ids
	processingData.UniqueIDMap = uniqueIdMap
	processingData.UniqueIDToOptionalColumn = uniqueIDToOptionalColumn
	processingData.SchemeType = schemeType
	return processingData, nil
}

func parseBulkDeleteCSVFile(reader io.Reader) (BulkDeleteCSVData, error) {
	csvReader := csv.NewReader(reader)

	dec, err := csvutil.NewDecoder(csvReader)
	if err != nil {
		return BulkDeleteCSVData{}, fmt.Errorf("create csv decoder error: %w", err)
	}
	dec.DisallowMissingColumns = true

	var (
		processingData BulkDeleteCSVData
		idx            int
		merr           = NewCSVMultipleError()
	)

	for {
		rowIndex := idx + 1

		var r BulkDeleteSchemeDataRow
		err := dec.Decode(&r)
		if err != nil {
			if err == io.EOF {
				break
			}

			var missingColumnsErr *csvutil.MissingColumnsError
			if errors.As(err, &missingColumnsErr) {
				return BulkDeleteCSVData{}, fmt.Errorf("missing column: %w", err)
			}

			var invalidDecodeErr *csvutil.InvalidDecodeError
			if errors.As(err, &invalidDecodeErr) {
				return BulkDeleteCSVData{}, fmt.Errorf("unable to decode on row %v: %v", rowIndex, err.Error())
			}

			merr.AddError(internalError.NewCSVRowError(
				rowIndex,
				"-",
				err.Error(),
			))

			idx++
			continue
		}
		merr.AddSuccess(SuccessRow{
			Row: rowIndex,
			Id:  r.ID,
		})
		processingData.IDs = append(processingData.IDs, r.ID)
		idx++
	}
	if merr.HasError() {
		return BulkDeleteCSVData{}, merr
	}

	return processingData, nil
}

func decode(schemeType shared.SchemeType, dec *csvutil.Decoder) (interface{}, error) {
	var i interface{}
	switch schemeType {
	case shared.FLAT_RATE:
		var r BulkImportCSVFlatRateSchemeDataRow
		err := dec.Decode(&r)
		if err != nil {
			return nil, err
		}
		i = r

	case shared.BASKET_SIZE:
		var r BulkImportCSVBasketSchemeDataRow
		err := dec.Decode(&r)
		if err != nil {
			return nil, err
		}
		i = r
	case shared.DISTANCE:
		var r BulkImportCSVDistanceSchemeDataRow
		err := dec.Decode(&r)
		if err != nil {
			return nil, err
		}
		i = r
	case shared.PICKUP_DISTANCE:
		var r BulkImportCSVPickupDistanceSchemeDataRow
		err := dec.Decode(&r)
		if err != nil {
			return nil, err
		}
		i = r
	case shared.INSTALLMENT:
		var r BulkImportCSVInstallmentSchemeDataRow
		err := dec.Decode(&r)
		if err != nil {
			return nil, err
		}
		i = r
	default:
		return nil, ErrSchemeNotImplemented
	}
	return i, nil
}

func addCondition(schemeType shared.SchemeType, row BulkImportCSVDataRow, groupIdToConditionMap map[string][]interface{}) error {
	var conditions interface{}
	switch schemeType {
	case shared.FLAT_RATE:
		conditions = row.FlatRateCondition
	case shared.BASKET_SIZE:
		conditions = row.BasketCondition
	case shared.DISTANCE:
		conditions = row.DistanceCondition
	case shared.PICKUP_DISTANCE:
		conditions = row.PickupDistanceCondition
	case shared.INSTALLMENT:
		conditions = row.InstallmentCondition
	default:
		return errors.New("scheme not supported when add condition")
	}

	groupIdToConditionMap[row.Id] = append(groupIdToConditionMap[row.Id], conditions)

	return nil
}
