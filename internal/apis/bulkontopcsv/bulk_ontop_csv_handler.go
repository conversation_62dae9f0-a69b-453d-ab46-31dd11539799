package bulkontopcsv

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkontopcsv/bulkcsv"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkontopcsv/shared"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/ontopfare"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
)

func (of *BulkOntopCSVAPI) handlerImportCSVCreate(ctx context.Context, email string, req bulkImportCSVCreateReq) (bulkImportCSVCreateRes, error) {
	schemeType, err := shared.ToSchemeType(req.Type)
	if err != nil {
		return bulkImportCSVCreateRes{}, err
	}
	data, err := of.bulkOntopCSVService.ParseCSVFile(ctx, req.File, schemeType)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVCreateRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("parse csv file err: %w", err)
		return bulkImportCSVCreateRes{}, err
	}

	err = of.bulkOntopCSVService.ValidateBulkImportCSVData(ctx, data)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVCreateRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("validate csv file err: %w", err)
		return bulkImportCSVCreateRes{}, err
	}

	// converting to create on top req phase
	reqs := ontopfare.CreateOnTopFareReqs{}
	getRegionFunc := polygon.GetRegionFn(of.polygonService.GetRegion)
	err = reqs.FromBulkImportCSV(email, data, getRegionFunc)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVCreateRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("converting on top req err: %w", err)
		return bulkImportCSVCreateRes{}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}

	// converting model

	operations, err := createInsertOperationRequests(email, reqs, data.UniqueIDs)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVCreateRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("converting model err: %w", err)
		return bulkImportCSVCreateRes{}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}

	// bulk to db
	res := of.bulkInsertToDB(ctx, operations)

	return res, nil
}

func (of *BulkOntopCSVAPI) handlerImportCSVUpdate(ctx context.Context, email string, req bulkImportCSVUpdateReq) (bulkImportCSVUpdateRes, error) {
	schemeType, err := shared.ToSchemeType(req.Type)
	if err != nil {
		return bulkImportCSVUpdateRes{}, err
	}
	data, err := of.bulkOntopCSVService.ParseCSVFile(ctx, req.File, schemeType)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVUpdateRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("parse csv file err: %w", err)
		return bulkImportCSVUpdateRes{}, err
	}

	err = of.bulkOntopCSVService.ValidateBulkImportCSVData(ctx, data)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVUpdateRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("validate csv file err: %w", err)
		return bulkImportCSVUpdateRes{}, err
	}

	// get ontop fare from db
	fetchOnTopFn := of.onTopFareRepo.FindByIDs
	onTopModel, err := of.bulkOntopCSVService.FetchOnTopFareFromDB(ctx, fetchOnTopFn, data.UniqueIDs)
	if err != nil {
		return bulkImportCSVUpdateRes{}, fmt.Errorf("fetch on top from db err: %w", err)
	}

	// check record exist
	err = of.bulkOntopCSVService.CheckExist(ctx, data.UniqueIDs, onTopModel.IdMap)
	if err != nil {
		return bulkImportCSVUpdateRes{}, fmt.Errorf("check record exist from db err: %w", err)
	}

	// converting to update on top req phase
	reqs := ontopfare.UpdateOnTopFareReqs{}
	getRegionFunc := polygon.GetRegionFn(of.polygonService.GetRegion)
	err = reqs.FromBulkImportCSV(email, data, getRegionFunc)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVUpdateRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("converting on top req err: %w", err)
		return bulkImportCSVUpdateRes{}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}

	// create operation requests
	operations, err := createUpdateOperationRequests(email, reqs, onTopModel)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVUpdateRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("create update operation err: %w", err)
		return bulkImportCSVUpdateRes{}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}

	// bulk to db
	res := of.bulkUpdateToDB(ctx, operations)

	return res, nil
}

func (of *BulkOntopCSVAPI) handlerImportCSVDelete(ctx context.Context, email string, req bulkImportCSVDeleteReq) (bulkImportCSVDeleteRes, error) {
	data, err := of.bulkOntopCSVService.ParseDeleteCSVFile(ctx, req.File)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVDeleteRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("parse csv file err: %w", err)
		return bulkImportCSVDeleteRes{}, err
	}

	err = of.bulkOntopCSVService.ValidateBulkDeleteCSVData(ctx, data)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVDeleteRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("parse csv file err: %w", err)
		return bulkImportCSVDeleteRes{}, err
	}

	// get ontop fare from db
	fetchOnTopFn := of.onTopFareRepo.FindByIDs
	onTopModel, err := of.bulkOntopCSVService.FetchOnTopFareFromDB(ctx, fetchOnTopFn, data.IDs)
	if err != nil {
		err := fmt.Errorf("fetch on top from db err: %w", err)
		return bulkImportCSVDeleteRes{}, err
	}

	// check record exist
	err = of.bulkOntopCSVService.CheckExist(ctx, data.IDs, onTopModel.IdMap)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVDeleteRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("check exist err: %w", err)
		return bulkImportCSVDeleteRes{}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}

	// create operation requests
	operations, err := createDeleteOperationRequests(email, data, onTopModel)
	if err != nil {
		var merr *bulkcsv.CSVMultipleError
		if errors.As(err, &merr) {
			return bulkImportCSVDeleteRes{}, bulkcsv.ErrRowValidationError(merr)
		}
		err := fmt.Errorf("create delete operation err: %w", err)
		return bulkImportCSVDeleteRes{}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}

	// bulk to db
	res := of.bulkDeleteToDB(ctx, operations)

	return res, nil
}
