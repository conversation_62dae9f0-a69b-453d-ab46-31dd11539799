package account

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/structpb"

	formServicePb "git.wndv.co/go/proto/lineman/form_service/v1"
	"git.wndv.co/lineman/absinthe/crypt"
	absintheUtils "git.wndv.co/lineman/absinthe/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/polygonutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func req(tripID string) (*gin.Context, *httptest.ResponseRecorder) {
	ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("/claims-order/%s", tripID), nil)
	driver.SetDriverIDToContext(ctx, "driverID")
	ctx.Params = gin.Params{
		{Key: "trip_id", Value: tripID},
	}
	return ctx, recorder
}

func TestAccountAPI_GetOrderClaimList(t *testing.T) {
	timeutils.Freeze()

	newGRPCStruct := func(src map[string]interface{}) *structpb.Struct {
		res, _ := structpb.NewStruct(src)
		return res
	}

	t.Run("should return all claims types when get claim list", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		var forms []*formServicePb.Form
		var allTitle []string
		for subtype, s := range model.FormSubTypeTitle {
			forms = append(forms, &formServicePb.Form{
				Status:      formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				FormSubtype: subtype,
				Value: newGRPCStruct(map[string]interface{}{
					"reason":      "reason",
					"orderId":     "orderId",
					"serviceType": "serviceType",
				})})
			allTitle = append(allTitle, s)
		}
		fr := &formServicePb.ListFormResponse{
			CountTotal: int32(len(forms)),
			Data:       forms,
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil)

		ctx, recorder := testutil.TestRequestContext("GET", "claims", nil)
		driver.SetDriverIDToContext(ctx, "driverID")
		svc.GetClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual struct {
			Data []FormClaim `json:"data"`
		}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, len(forms), len(actual.Data))
		require.Equal(tt, "orderId", actual.Data[0].OrderID)
		for _, datum := range actual.Data {
			absintheUtils.StrContains(datum.Title, allTitle)
		}
	})

	t.Run("should return claims list correctly", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(true)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "1",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
				{
					OrderID:     "2",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Order{Region: "BKK"}, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 6, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยค่าอาหาร", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "ขอชดเชยค่าเสียเวลา: ออเดอร์ยกเลิก", actual.ClaimSubTypes[1].Title)
		require.Equal(tt, "ขอชดเชยค่าเสียเวลา: รออาหารนาน", actual.ClaimSubTypes[2].Title)
		require.Equal(tt, "ขอชดเชยค่าที่จอดรถ", actual.ClaimSubTypes[3].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปส่งไม่ตรง (ร้านค้า ไป ลูกค้า)", actual.ClaimSubTypes[4].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปรับไม่ตรง (ไรเดอร์ ไป ร้านค้า)", actual.ClaimSubTypes[5].Title)
		require.Equal(tt, "1", actual.LastClaim.OrderID)
		require.Equal(tt, "FORM_STATUS_SUBMITED", actual.LastClaim.Status)
	})

	t.Run("should return claims list for food and not BKK", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "1",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
				{
					OrderID:     "2",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Order{Region: "PATTAYA"}, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 5, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยค่าอาหาร", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "ขอชดเชยค่าเสียเวลา: ออเดอร์ยกเลิก", actual.ClaimSubTypes[1].Title)
		require.Equal(tt, "ขอชดเชยค่าเสียเวลา: รออาหารนาน", actual.ClaimSubTypes[2].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปส่งไม่ตรง (ร้านค้า ไป ลูกค้า)", actual.ClaimSubTypes[3].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปรับไม่ตรง (ไรเดอร์ ไป ร้านค้า)", actual.ClaimSubTypes[4].Title)
		require.Equal(tt, "1", actual.LastClaim.OrderID)
		require.Equal(tt, "FORM_STATUS_SUBMITED", actual.LastClaim.Status)
	})

	t.Run("should return non-order-base claims list correctly", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		tripID := "non-order-base"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "1",
				})}},
		}
		deps.formService.EXPECT().ListForm(gomock.Any(), gomock.Any()).Return(fr, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 3, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยค่าโควิด", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "ขอชดเชยอุบัติเหตุ", actual.ClaimSubTypes[1].Title)
		require.Equal(tt, "แจ้งปัญหาเติมเครดิตไม่เข้า", actual.ClaimSubTypes[2].Title)
	})

	t.Run("should return non-order-base claims list correctly (non-order-id)", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		tripID := "non-order-base"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{
				Status:      formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				FormSubtype: formServicePb.FormSubtype_FORM_SUBTYPE_RIDER_COMPENSATION,
				Value:       newGRPCStruct(map[string]interface{}{})}},
		}
		deps.formService.EXPECT().ListForm(gomock.Any(), gomock.Any()).Return(fr, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 3, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยค่าโควิด", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "ขอชดเชยอุบัติเหตุ", actual.ClaimSubTypes[1].Title)
		require.Equal(tt, "แจ้งปัญหาเติมเครดิตไม่เข้า", actual.ClaimSubTypes[2].Title)
		require.Equal(tt, "ขอชดเชยค่าเสียเวลา: ออเดอร์ยกเลิก", actual.LastClaim.Title)
	})

	t.Run("should return all claims types but not in blacklist", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{
			config: DBConfig{
				FormClaimSubtypeBlacklist: types.NewStringSet("FORM_SUBTYPE_FOOD_CLAIM", "FORM_SUBTYPE_ACCIDENT_CLAIM", "FORM_SUBTYPE_RIDER_COMPENSATION"),
			},
		})
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "1",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
				{
					OrderID:     "2",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Order{Region: "BKK"}, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 4, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยค่าเสียเวลา: รออาหารนาน", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "ขอชดเชยค่าที่จอดรถ", actual.ClaimSubTypes[1].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปส่งไม่ตรง (ร้านค้า ไป ลูกค้า)", actual.ClaimSubTypes[2].Title)
		require.Equal(tt, "1", actual.LastClaim.OrderID)
		require.Equal(tt, "FORM_STATUS_SUBMITED", actual.LastClaim.Status)
	})

	t.Run("should return non-order-base claims list but not in blacklist", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{
			config: DBConfig{
				FormClaimSubtypeBlacklist: types.NewStringSet("FORM_SUBTYPE_COVID_CLAIM"),
			},
		})
		defer finish()

		tripID := "non-order-base"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "1",
				})}},
		}
		deps.formService.EXPECT().ListForm(gomock.Any(), gomock.Any()).Return(fr, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 2, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยอุบัติเหตุ", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "แจ้งปัญหาเติมเครดิตไม่เข้า", actual.ClaimSubTypes[1].Title)
	})

	t.Run("(Bike)(RIDER_COMPENSATION) Enable Flag - Cancellation Source is user - should allow to submit", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			Region:  "BKK",
			CancelDetail: model.CancelDetail{
				Source:      model.SourceUSER,
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
			},
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 2, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยค่าเสียเวลา: ออเดอร์ยกเลิก", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปรับไม่ตรง (ไรเดอร์ ไป ร้านค้า)", actual.ClaimSubTypes[1].Title)
		require.Len(tt, actual.ClaimSubTypes[0].Orders, 1)
		require.Equal(tt, "order-1", actual.ClaimSubTypes[0].Orders[0].OrderID)
		require.True(tt, actual.ClaimSubTypes[0].Orders[0].IsClaimable)
	})

	t.Run("(Bike)(RIDER_COMPENSATION) Enable Flag - Cancellation Source is not user - reason match with the configured - should not allow to submit", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 2, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยค่าเสียเวลา: ออเดอร์ยกเลิก", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปรับไม่ตรง (ไรเดอร์ ไป ร้านค้า)", actual.ClaimSubTypes[1].Title)
		require.Len(tt, actual.ClaimSubTypes[0].Orders, 1)
		require.Equal(tt, "order-1", actual.ClaimSubTypes[0].Orders[0].OrderID)
		require.False(tt, actual.ClaimSubTypes[0].Orders[0].IsClaimable)
	})

	t.Run("(Bike)(RIDER_COMPENSATION) Enable Flag - Update Cancellation Metadata return error - should return error", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil)

		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Order{Region: "BKK"}, nil)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:      model.SourceUSER,
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
			},
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.StatusDriverMatched.String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():      {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		deps.mapService.EXPECT().FindDistances(gomock.Any(), []mapservice.Location{
			{
				Lat: 1.1,
				Lng: 1.1,
			},
		}, mapservice.Location{
			Lat: 2.2,
			Lng: 2.2,
		}).Return(&mapservice.Table{
			Distances: []float64{5001.1},
		}, nil)

		mockError := errors.New("mocked error")
		deps.orderService.EXPECT().UpdateOrderCancellationMetadata(gomock.Any(), gomock.Any(), gomock.Any()).Do(func(ctx context.Context, order *model.Order, opts ...repository.Option) {
			require.Len(tt, opts, 0)

			require.NotNil(tt, order.CancelDetail.CancellationMetadata)
			require.Equal(tt, model.CancellationMetadata{
				ActualDistanceBetweenDriverMatchedAndCancelled: fp.ToPointer(5001.1),
			}, *order.CancelDetail.CancellationMetadata)
		}).Return(mockError)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("(Bike)(RIDER_COMPENSATION) Enable Flag - Should calculate the ActualDistance if the data is not persist", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:               model.SourceUSER,
				CancelledBy:          model.CancelledByCS.String(),
				Name:                 "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.StatusDriverMatched.String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():      {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		deps.mapService.EXPECT().FindDistances(gomock.Any(), []mapservice.Location{
			{
				Lat: 1.1,
				Lng: 1.1,
			},
		}, mapservice.Location{
			Lat: 2.2,
			Lng: 2.2,
		}).Return(&mapservice.Table{
			Distances: []float64{12.3},
		}, nil)

		expectedUpdatedCancellationMetadata := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:      model.SourceUSER,
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{
					ActualDistanceBetweenDriverMatchedAndCancelled: fp.ToPointer(12.3),
				},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.StatusDriverMatched.String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():      {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().UpdateOrderCancellationMetadata(gomock.Any(), &expectedUpdatedCancellationMetadata, gomock.Any()).Return(nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("(Bike)(RIDER_COMPENSATION) Enable Flag - Should not calculate the ActualDistance if the data is already exists", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
				BikeCancelReasonThatShouldWaitLongerThan10Mins:            types.NewStringSet("Test"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:      model.SourceUSER,
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{
					DisplacementBetweenArrivedAtAndCancelled: fp.ToPointer(12.34),
				},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("(Bike)(RIDER_COMPENSATION) Enable Flag - Should not calculate the ActualDistance if the CANCELED history location is not exists", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:      model.SourceUSER,
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{
					DisplacementBetweenArrivedAtAndCancelled: fp.ToPointer(12.34),
				},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.StatusDriverMatched.String(): {Lat: 1.1, Lng: 1.1},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("(Bike)(RIDER_COMPENSATION) Enable Flag - Should return error if calculate the calculating ActualDistance in mapservice return error", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:               model.SourceUSER,
				CancelledBy:          model.CancelledByCS.String(),
				Name:                 "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.StatusDriverMatched.String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():      {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		mockedError := errors.New("mocked error")
		deps.mapService.EXPECT().FindDistances(gomock.Any(), []mapservice.Location{
			{
				Lat: 1.1,
				Lng: 1.1,
			},
		}, mapservice.Location{
			Lat: 2.2,
			Lng: 2.2,
		}).Return(nil, mockedError)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("(Bike)(RIDER_COMPENSATION) Enable Flag - Should not calculate the Displacement if the data is already exists", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:      model.SourceUSER,
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{
					DisplacementBetweenArrivedAtAndCancelled: fp.ToPointer(12.34),
				},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("(Bike)(RIDER_COMPENSATION) Enable Flag - Should not calculate the Displacement if the CANCELED history location is not existss", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:               model.SourceUSER,
				CancelledBy:          model.CancelledByCS.String(),
				Name:                 "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("(Bike)(RIDER_COMPENSATION) Enable Flag - Should calculate the Displacement if history hs ARRIVED_AT_0 and CANCELED and the data is not exists", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:               model.SourceUSER,
				CancelledBy:          model.CancelledByCS.String(),
				Name:                 "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		expectedUpdatedCancellationMetadataOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:      model.SourceUSER,
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{
					DisplacementBetweenArrivedAtAndCancelled: fp.ToPointer(polygonutil.DistanceInMeter(model.Location{Lat: 1.1, Lng: 1.1}, model.Location{Lat: 2.2, Lng: 2.2})),
				},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().UpdateOrderCancellationMetadata(gomock.Any(), &expectedUpdatedCancellationMetadataOrder).Return(nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("(RIDER_COMPENSATION) Enable Flag - Order is not Bike", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:               model.SourceUSER,
				CancelledBy:          model.CancelledByCS.String(),
				Name:                 "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("(Rare case)(RIDER_COMPENSATION) Enable Flag - more than 1 order in trip - should query another order up", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
				{
					OrderID:     "order-2",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:               model.SourceUSER,
				CancelledBy:          model.CancelledByCS.String(),
				Name:                 "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)
		expectedUpdatedCancellationMetadataOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:      model.SourceUSER,
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{
					DisplacementBetweenArrivedAtAndCancelled: fp.ToPointer(polygonutil.DistanceInMeter(model.Location{Lat: 1.1, Lng: 1.1}, model.Location{Lat: 2.2, Lng: 2.2})),
				},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().UpdateOrderCancellationMetadata(gomock.Any(), &expectedUpdatedCancellationMetadataOrder).Return(nil)

		expectedOrder2 := model.Order{
			OrderID: "order-2",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:               model.SourceUSER,
				CancelledBy:          model.CancelledByCS.String(),
				Name:                 "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-2", gomock.Any()).Return(&expectedOrder2, nil)

		expectedUpdatedCancellationMetadataOrder2 := model.Order{
			OrderID: "order-2",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:      model.SourceUSER,
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{
					DisplacementBetweenArrivedAtAndCancelled: fp.ToPointer(polygonutil.DistanceInMeter(model.Location{Lat: 1.1, Lng: 1.1}, model.Location{Lat: 2.2, Lng: 2.2})),
				},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().UpdateOrderCancellationMetadata(gomock.Any(), &expectedUpdatedCancellationMetadataOrder2).Return(nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("(Rare case)(RIDER_COMPENSATION) Enable Flag - more than 1 order in trip - should return error if get order failed", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
				{
					OrderID:     "order-2",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:               model.SourceUSER,
				CancelledBy:          model.CancelledByCS.String(),
				Name:                 "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)
		expectedUpdatedCancellationMetadataOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:      model.SourceUSER,
				CancelledBy: model.CancelledByCS.String(),
				Name:        "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{
					DisplacementBetweenArrivedAtAndCancelled: fp.ToPointer(polygonutil.DistanceInMeter(model.Location{Lat: 1.1, Lng: 1.1}, model.Location{Lat: 2.2, Lng: 2.2})),
				},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().UpdateOrderCancellationMetadata(gomock.Any(), &expectedUpdatedCancellationMetadataOrder).Return(nil)

		mockedError := errors.New("mocked error")
		deps.orderService.EXPECT().Get(gomock.Any(), "order-2", gomock.Any()).Return(nil, mockedError)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("(RIDER_COMPENSATION) Disable Flag - more than 1 order in trip - should return error if get order failed", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(
			tt,
			config.DriverPeriodCompletedTripsConfig{},
			config.PaymentConfig{},
			&order.AtomicContingencyConfig{},
			&AtomicDBConfig{},
			WithAtomicFormServiceConfig(service.FormServiceConfig{
				BikeCancelDetailNameSubmitRiderCompensationFormPrevention: types.NewStringSet("SurlyTheSame"),
			}),
			WithFormConfig(
				config.FormConfig{
					IsEnableRiderCompensationTimeAndDistanceLogicFeature: false,
				},
			),
		)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "order-1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		expectedOrder := model.Order{
			OrderID: "order-1",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source:               model.SourceUSER,
				CancelledBy:          model.CancelledByCS.String(),
				Name:                 "SurlyTheSame",
				CancellationMetadata: &model.CancellationMetadata{},
			},
			Region: "BKK",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				CreatedAt:   timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.NewArrivedAtOrderStatus(0).String(): {Lat: 1.1, Lng: 1.1},
				model.StatusCanceled.String():             {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.orderService.EXPECT().Get(gomock.Any(), "order-1", gomock.Any()).Return(&expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 2, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยค่าเสียเวลา: ออเดอร์ยกเลิก", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปรับไม่ตรง (ไรเดอร์ ไป ร้านค้า)", actual.ClaimSubTypes[1].Title)
		require.Len(tt, actual.ClaimSubTypes[0].Orders, 1)
		require.Equal(tt, "order-1", actual.ClaimSubTypes[0].Orders[0].OrderID)
		require.False(tt, actual.ClaimSubTypes[0].Orders[0].IsClaimable)
	})

	t.Run("should return all claims types but not in blacklist for a full-time driver", func(tt *testing.T) {
		atomicDBConfig := &AtomicDBConfig{
			config: DBConfig{
				FormClaimSubtypeBlacklist:               types.NewStringSet("FORM_SUBTYPE_FOOD_CLAIM", "FORM_SUBTYPE_ACCIDENT_CLAIM", "FORM_SUBTYPE_RIDER_COMPENSATION"),
				FormClaimSubtypeBlacklistFullTimeDriver: types.NewStringSet("FORM_SUBTYPE_LONG_WAITING_TIME_COMPENSATION", "FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM"),
			},
		}
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, atomicDBConfig)
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{
				DriverType: crypt.NewLazyEncryptedString(string(model.DriverTypeFullTime)),
			}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(true)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "1",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
				{
					OrderID:     "2",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Order{Region: "BKK"}, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 2, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยค่าที่จอดรถ", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปส่งไม่ตรง (ร้านค้า ไป ลูกค้า)", actual.ClaimSubTypes[1].Title)
		require.Equal(tt, "1", actual.LastClaim.OrderID)
		require.Equal(tt, "FORM_STATUS_SUBMITED", actual.LastClaim.Status)
		require.ElementsMatch(tt, []string{"FORM_SUBTYPE_FOOD_CLAIM", "FORM_SUBTYPE_ACCIDENT_CLAIM", "FORM_SUBTYPE_RIDER_COMPENSATION"}, atomicDBConfig.Get().FormClaimSubtypeBlacklist.GetElements())
		require.ElementsMatch(tt, []string{"FORM_SUBTYPE_LONG_WAITING_TIME_COMPENSATION", "FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM"}, atomicDBConfig.Get().FormClaimSubtypeBlacklistFullTimeDriver.GetElements())
	})

	t.Run("should return all claims types but not in blacklist with full-time blacklist", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{
			config: DBConfig{
				FormClaimSubtypeBlacklist:               types.NewStringSet("FORM_SUBTYPE_FOOD_CLAIM", "FORM_SUBTYPE_ACCIDENT_CLAIM", "FORM_SUBTYPE_RIDER_COMPENSATION"),
				FormClaimSubtypeBlacklistFullTimeDriver: types.NewStringSet("FORM_SUBTYPE_LONG_WAITING_TIME_COMPENSATION", "FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM"),
			},
		})
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(true)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "1",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
				{
					OrderID:     "2",
					IsFraud:     false,
					ServiceType: model.ServiceFood,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{Status: formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "1",
				})}},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Order{Region: "BKK"}, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 4, len(actual.ClaimSubTypes))
		require.Equal(tt, "ขอชดเชยค่าเสียเวลา: รออาหารนาน", actual.ClaimSubTypes[0].Title)
		require.Equal(tt, "ขอชดเชยค่าที่จอดรถ", actual.ClaimSubTypes[1].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปส่งไม่ตรง (ร้านค้า ไป ลูกค้า)", actual.ClaimSubTypes[2].Title)
		require.Equal(tt, "ขอชดเชยระยะทางไปรับไม่ตรง (ไรเดอร์ ไป ร้านค้า)", actual.ClaimSubTypes[3].Title)
		require.Equal(tt, "1", actual.LastClaim.OrderID)
		require.Equal(tt, "FORM_STATUS_SUBMITED", actual.LastClaim.Status)
	})

	timeutils.Unfreeze()

}

func TestAccountAPI_GetOrderClaimList_Bike_2WQRPaymentClaim(t *testing.T) {
	timeutils.Freeze()

	newGRPCStruct := func(src map[string]interface{}) *structpb.Struct {
		res, _ := structpb.NewStruct(src)
		return res
	}

	t.Run("claimable when order completed and QR status is waiting for payment", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(true)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{}

		expectedOrder := &model.Order{
			Status: model.StatusCompleted,
			HeadTo: 1,
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				PayAtStop:   1,
				Routes: []model.Stop{
					{}, {
						Pauses: model.PauseSet{model.PauseQRPayment: true},
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								RawBaseFee: 120,
								RoadFee:    50,
								Discounts: []model.Discount{{
									Type:     model.DiscountTypeSubsidize,
									Category: "COUPON",
									Discount: 20,
									Code:     "XXX",
								}},
								Total:         150,
								PaymentMethod: model.PaymentMethodQRPromptPay,
								QRPromptPayInfo: model.QRPromptPayInfo{
									Status:          model.QRPromptPayStatusWaitingForPayment,
									IsUserResolveQR: false,
								},
							},
						},
					},
				},
				CreatedAt: timeutils.Now(),
			},
			History: map[string]time.Time{
				string(model.StatusCompleted): timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.StatusCompleted.String(): {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		var QRPaymentClaim ClaimSubType

		for _, claimSubType := range actual.ClaimSubTypes {
			if claimSubType.SubType == model.FormSubtypeDriver2WQRPaymentClaim.ToString() {
				QRPaymentClaim = claimSubType
				break
			}
		}
		require.NotNil(tt, QRPaymentClaim)
		require.True(tt, QRPaymentClaim.Orders[0].IsClaimable)
		require.False(tt, QRPaymentClaim.Orders[0].IsClaimed)
		require.Empty(tt, QRPaymentClaim.Orders[0].ClaimableMessage)
	})

	t.Run("form already created but driver still see claim list", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(true)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{
				FormSubtype: formServicePb.FormSubtype_FORM_SUBTYPE_DRIVER_2W_QR_PAYMENT_CLAIM,
				Status:      formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "1",
				})}},
		}

		expectedOrder := &model.Order{
			Status: model.StatusCompleted,
			HeadTo: 1,
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				PayAtStop:   1,
				Routes: []model.Stop{
					{}, {
						Pauses: model.PauseSet{model.PauseQRPayment: true},
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								RawBaseFee: 120,
								RoadFee:    50,
								Discounts: []model.Discount{{
									Type:     model.DiscountTypeSubsidize,
									Category: "COUPON",
									Discount: 20,
									Code:     "XXX",
								}},
								Total:         150,
								PaymentMethod: model.PaymentMethodQRPromptPay,
								QRPromptPayInfo: model.QRPromptPayInfo{
									Status:          model.QRPromptPayStatusWaitingForPayment,
									IsUserResolveQR: false,
									FormID:          "x",
								},
							},
						},
					},
				},
				CreatedAt: timeutils.Now(),
			},
			History: map[string]time.Time{
				string(model.StatusCompleted): timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.StatusCompleted.String(): {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		var QRPaymentClaim ClaimSubType

		for _, claimSubType := range actual.ClaimSubTypes {
			if claimSubType.SubType == model.FormSubtypeDriver2WQRPaymentClaim.ToString() {
				QRPaymentClaim = claimSubType
				break
			}
		}
		require.NotNil(tt, QRPaymentClaim)
		require.True(tt, QRPaymentClaim.Orders[0].IsClaimable)
		require.True(tt, QRPaymentClaim.Orders[0].IsClaimed)
		require.Empty(tt, QRPaymentClaim.Orders[0].ClaimableMessage)
	})

	t.Run("qr payment claim should not show when feature flag disable", func(tt *testing.T) {
		svc, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		tripID := "trip-id"
		ctx, recorder := req(tripID)

		deps.driverRepository.EXPECT().
			FindDriverMinimalByDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.DriverMinimal{}, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsQRPaymentClaimFormEnabled.Name).
			Return(false)

		deps.tripRepo.EXPECT().
			GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.Trip{TripID: tripID, Orders: model.TripOrders{
				{
					OrderID:     "1",
					IsFraud:     false,
					ServiceType: model.ServiceBike,
				},
			}}, nil)

		fr := &formServicePb.ListFormResponse{
			CountTotal: 1,
			Data: []*formServicePb.Form{{
				FormSubtype: formServicePb.FormSubtype_FORM_SUBTYPE_DRIVER_2W_QR_PAYMENT_CLAIM,
				Status:      formServicePb.FormStatus_FORM_STATUS_SUBMITED,
				Value: newGRPCStruct(map[string]interface{}{
					"orderId": "1",
				})}},
		}

		expectedOrder := &model.Order{
			Status: model.StatusCompleted,
			HeadTo: 1,
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				PayAtStop:   1,
				Routes: []model.Stop{
					{}, {
						Pauses: model.PauseSet{model.PauseQRPayment: true},
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								RawBaseFee: 120,
								RoadFee:    50,
								Discounts: []model.Discount{{
									Type:     model.DiscountTypeSubsidize,
									Category: "COUPON",
									Discount: 20,
									Code:     "XXX",
								}},
								Total:         150,
								PaymentMethod: model.PaymentMethodQRPromptPay,
								QRPromptPayInfo: model.QRPromptPayInfo{
									Status:          model.QRPromptPayStatusWaitingForPayment,
									IsUserResolveQR: false,
									FormID:          "x",
								},
							},
						},
					},
				},
				CreatedAt: timeutils.Now(),
			},
			History: map[string]time.Time{
				string(model.StatusCompleted): timeutils.Now(),
			},
			HistoryLocation: map[string]model.LocationWithUpdatedAt{
				model.StatusCompleted.String(): {Lat: 2.2, Lng: 2.2},
			},
		}
		deps.formService.EXPECT().
			ListForm(gomock.Any(), gomock.Any()).
			Return(fr, nil).Times(2)

		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(expectedOrder, nil)

		svc.GetOrderClaimList(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual OrderClaimListResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		var QRPaymentClaim ClaimSubType

		for _, claimSubType := range actual.ClaimSubTypes {
			if claimSubType.SubType == model.FormSubtypeDriver2WQRPaymentClaim.ToString() {
				QRPaymentClaim = claimSubType
				break
			}
		}

		require.Empty(tt, QRPaymentClaim)
	})

}
