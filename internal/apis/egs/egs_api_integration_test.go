//go:build integration_test
// +build integration_test

package egs_test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/egs"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/testdata"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	xgotimeutil "git.wndv.co/lineman/xgo/timeutil"
)

func TestEGSAPI_UpdateInstallmentsProductDetail(t *testing.T) {

	type checkInstallment func(t *testing.T, src model.Installment)

	t.Run("update installment product detail success", func(t *testing.T) {
		testcases := []struct {
			name                 string
			got                  []string
			want                 []string
			checkInstallmentFunc checkInstallment
			targetInstallmentID  []int
		}{
			{
				name:                "update stock id, stock priority and principal price",
				got:                 []string{fmt.Sprintf(`[{"installmentId": "%s", "stockSku": "SKU_SINGLE_01", "stockId": "รอบ 1", "productDetail":{"sku":"SKU_SINGLE_01","stockId":"รอบ 2","principalPrice":210,"stockPriority":2}}]`, testdata.ObjectId(1))},
				want:                []string{`[{"installmentId":"000000000000000000000001", "installmentPrincipalPrice":410, "productDetails":[{"barcode":"SINGLE01", "isReward":false, "principalPrice":210, "sku":"SKU_SINGLE_01", "stockID":"รอบ 2", "stockLabel":"", "stockPriority":2}, {"barcode":"SINGLE02", "isReward":false, "principalPrice":200, "sku":"SKU_SINGLE_02", "stockID":"รอบ 1", "stockLabel":"", "stockPriority":1}]}]`},
				targetInstallmentID: []int{1},
				checkInstallmentFunc: func(t *testing.T, src model.Installment) {
					assert.Equal(t, types.Money(410), src.Principal)
					expectedProductDetails := []model.ProductDetail{
						{
							SKU:            "SKU_SINGLE_01",
							StockID:        "รอบ 2",
							PrincipalPrice: 210,
							Barcode:        "SINGLE01",
							StockLabel:     "",
							StockPriority:  2,
							IsReward:       false,
						},
						{
							SKU:            "SKU_SINGLE_02",
							StockID:        "รอบ 1",
							PrincipalPrice: 200,
							Barcode:        "SINGLE02",
							StockLabel:     "",
							StockPriority:  1,
							IsReward:       false,
						},
					}
					assert.Equal(t, expectedProductDetails, src.ProductDetails)
				},
			},
			{
				name:                "update stock priority and principal price for 2 installments",
				got:                 []string{fmt.Sprintf(`[{"installmentId": "%s", "stockSku": "SKU_SINGLE_01", "stockId": "รอบ 1", "productDetail":{"sku":"SKU_SINGLE_01","stockId":"รอบ 3","principalPrice":210,"stockPriority":3}}, {"installmentId": "%s", "stockSku": "SKU_SINGLE_01", "stockId": "รอบ 3", "productDetail":{"sku":"SKU_SINGLE_01","stockId":"รอบ 1","principalPrice":200,"stockPriority":1}}]`, testdata.ObjectId(1), testdata.ObjectId(2))},
				want:                []string{`[{"installmentId":"000000000000000000000001", "installmentPrincipalPrice":410, "productDetails":[{"barcode":"SINGLE01", "isReward":false, "principalPrice":210, "sku":"SKU_SINGLE_01", "stockID":"รอบ 3", "stockLabel":"", "stockPriority":3}, {"barcode":"SINGLE02", "isReward":false, "principalPrice":200, "sku":"SKU_SINGLE_02", "stockID":"รอบ 1", "stockLabel":"", "stockPriority":1}]}, {"installmentId":"000000000000000000000002", "installmentPrincipalPrice":200, "productDetails":[{"barcode":"SINGLE01", "isReward":false, "principalPrice":200, "sku":"SKU_SINGLE_01", "stockID":"รอบ 1", "stockLabel":"", "stockPriority":1}]}]`},
				targetInstallmentID: []int{1, 2},
				checkInstallmentFunc: func(t *testing.T, src model.Installment) {
					switch src.ID.Hex() {
					case testdata.ObjectId(1):
						assert.Equal(t, types.Money(410), src.Principal)
						expectedProductDetails := []model.ProductDetail{
							{
								SKU:            "SKU_SINGLE_01",
								StockID:        "รอบ 3",
								PrincipalPrice: 210,
								Barcode:        "SINGLE01",
								StockLabel:     "",
								StockPriority:  3,
								IsReward:       false,
							},
							{
								SKU:            "SKU_SINGLE_02",
								StockID:        "รอบ 1",
								PrincipalPrice: 200,
								Barcode:        "SINGLE02",
								StockLabel:     "",
								StockPriority:  1,
								IsReward:       false,
							},
						}
						assert.Equal(t, expectedProductDetails, src.ProductDetails)
					case testdata.ObjectId(2):
						assert.Equal(t, types.Money(200), src.Principal)
						expectedProductDetails := []model.ProductDetail{
							{
								SKU:            "SKU_SINGLE_01",
								StockID:        "รอบ 1",
								PrincipalPrice: 200,
								Barcode:        "SINGLE01",
								StockLabel:     "",
								StockPriority:  1,
								IsReward:       false,
							},
						}
						assert.Equal(t, expectedProductDetails, src.ProductDetails)
					}
				},
			},
			{
				name:                "update principal price for a second stock",
				got:                 []string{fmt.Sprintf(`[{"installmentId": "%s", "stockSku": "SKU_SINGLE_02", "stockId": "รอบ 1", "productDetail":{"sku":"SKU_SINGLE_XXX","stockId":"รอบ 1","principalPrice":10000,"stockPriority":1}}]`, testdata.ObjectId(1))},
				want:                []string{`[{"installmentId":"000000000000000000000001", "installmentPrincipalPrice":10200, "productDetails":[{"barcode":"SINGLE01", "isReward":false, "principalPrice":200, "sku":"SKU_SINGLE_01", "stockID":"รอบ 1", "stockLabel":"", "stockPriority":1}, {"barcode":"SINGLE02", "isReward":false, "principalPrice":10000, "sku":"SKU_SINGLE_02", "stockID":"รอบ 1", "stockLabel":"", "stockPriority":1}]}]`},
				targetInstallmentID: []int{1},
				checkInstallmentFunc: func(t *testing.T, src model.Installment) {
					assert.Equal(t, types.Money(10200), src.Principal)
					expectedProductDetails := []model.ProductDetail{
						{
							SKU:            "SKU_SINGLE_01",
							StockID:        "รอบ 1",
							PrincipalPrice: 200,
							Barcode:        "SINGLE01",
							StockLabel:     "",
							StockPriority:  1,
							IsReward:       false,
						},
						{
							SKU:            "SKU_SINGLE_02",
							StockID:        "รอบ 1",
							PrincipalPrice: 10000,
							Barcode:        "SINGLE02",
							StockLabel:     "",
							StockPriority:  1,
							IsReward:       false,
						},
					}
					assert.Equal(t, expectedProductDetails, src.ProductDetails)
				},
			},
		}

		for _, tc := range testcases {
			tc := tc
			t.Run(tc.name, func(t *testing.T) {
				t.Parallel()

				container := ittest.NewContainer(t)
				if err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_installment"); err != nil {
					t.Errorf("Unexpected error initfixture: %v", err)
				}

				for i := 0; i < len(tc.got); i++ {
					got, want := tc.got[i], tc.want[i]

					var req []egs.UpdateInstallmentProductDetailReq
					err := json.Unmarshal([]byte(got), &req)
					assert.NoError(t, err)

					gctx := testutil.NewContextWithRecorder()
					gctx.SetPUT("/v1/egs/installments/product_detail")
					gctx.Body().RawJSON(got).Build()
					gctx.Send(container.GinEngineRouter)
					gctx.AssertResponseCode(t, http.StatusOK)
					assert.JSONEq(t, want, gctx.ResponseRecorder.Body.String())

					if tc.checkInstallmentFunc != nil {
						installmentDB := testutil.NewDBHelper(t, container.DBConnectionForTest, "installments")
						for _, targetID := range tc.targetInstallmentID {
							installmentObjectID, _ := primitive.ObjectIDFromHex(testdata.ObjectId(targetID))
							var updatedInstallment model.Installment
							installmentDB.FindOneByQuery(bson.M{"_id": installmentObjectID}, &updatedInstallment)
							tc.checkInstallmentFunc(t, updatedInstallment)
						}
					}
				}
			})

		}
	})
}

func TestEGSAPI_UpdateInstallmentStatus(tt *testing.T) {

	compareInstallment := func(t *testing.T, baseInstallment, updatedInstallment model.Installment) {
		if !baseInstallment.ExpectedDeliveryDate.IsZero() {
			require.Equal(t, baseInstallment.ExpectedDeliveryDate.Unix(), updatedInstallment.ExpectedDeliveryDate.Unix())
			baseInstallment.ExpectedDeliveryDate = updatedInstallment.ExpectedDeliveryDate
		}
		require.Equal(t, baseInstallment.Start.Unix(), updatedInstallment.Start.Unix())
		baseInstallment.Start = updatedInstallment.Start
		require.Equal(t, baseInstallment.End.Unix(), updatedInstallment.End.Unix())
		baseInstallment.End = updatedInstallment.End
		if !baseInstallment.UpdatedAt.IsZero() {
			require.Equal(t, baseInstallment.UpdatedAt.Unix(), updatedInstallment.UpdatedAt.Unix())
		}
		baseInstallment.UpdatedAt = updatedInstallment.UpdatedAt
		require.Len(t, updatedInstallment.InstallmentLogs, len(baseInstallment.InstallmentLogs))
		for index := range updatedInstallment.InstallmentLogs {
			require.NotEmpty(t, updatedInstallment.InstallmentLogs[index].TransRefID)
			if !baseInstallment.InstallmentLogs[index].CreatedAt.IsZero() {
				require.Equal(t, baseInstallment.InstallmentLogs[index].CreatedAt.Unix(), updatedInstallment.InstallmentLogs[index].CreatedAt.Unix())
			}
			baseInstallment.InstallmentLogs[index].CreatedAt = updatedInstallment.InstallmentLogs[index].CreatedAt
			if baseInstallment.InstallmentLogs[index].TransRefID.String() != "" {
				require.Equal(t, baseInstallment.InstallmentLogs[index].TransRefID, updatedInstallment.InstallmentLogs[index].TransRefID)
			}
			baseInstallment.InstallmentLogs[index].TransRefID = updatedInstallment.InstallmentLogs[index].TransRefID
			require.Len(t, updatedInstallment.InstallmentLogs[index].ActualAmountLogs, len(baseInstallment.InstallmentLogs[index].ActualAmountLogs))
			for index2 := range baseInstallment.InstallmentLogs[index].ActualAmountLogs {
				if !baseInstallment.InstallmentLogs[index].ActualAmountLogs[index2].CreatedAt.IsZero() {
					require.Equal(
						t,
						baseInstallment.InstallmentLogs[index].ActualAmountLogs[index2].CreatedAt.Unix(),
						updatedInstallment.InstallmentLogs[index].ActualAmountLogs[index2].CreatedAt.Unix(),
					)
				}
				baseInstallment.InstallmentLogs[index].ActualAmountLogs[index2].CreatedAt = updatedInstallment.InstallmentLogs[index].ActualAmountLogs[index2].CreatedAt
			}
		}
		require.Equal(t, baseInstallment, updatedInstallment)
	}

	type checkInstallment func(t *testing.T, src model.Installment)

	type testData struct {
		name                       string
		expectedHTTPCode           int
		targetInstallmentID        string
		getExpectedInstallmentFunc func(src model.Installment, freezedTime time.Time) model.Installment
		getRequestURLFunc          func(freezedTime time.Time) string
		getExpectedResponseFunc    func(freezedTime time.Time) string
		freezingTime               time.Time
	}
	testSet := []testData{
		{
			name: "Active an one-time installment from a PENDING status",
			getRequestURLFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"installmentId": "%s", "status": "ACTIVE", "driverId": "LMFABC001", "amount": 345, "lastAttempt": false}`, testdata.ObjectId(3))
			},
			getExpectedResponseFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"InstallmentID": "%s"}`, testdata.ObjectId(3))
			},
			targetInstallmentID: testdata.ObjectId(3),
			getExpectedInstallmentFunc: func(baseInstallment model.Installment, freezedTime time.Time) model.Installment {
				currentTime := timeutil.BangkokNow()
				baseInstallment.Status = model.InstallmentCompleted
				baseInstallment.Start = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())
				baseInstallment.End = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())
				baseInstallment.UpdatedAt = time.Time{} // Set
				baseInstallment.InstallmentLogs = []model.InstallmentLog{
					{
						Amount: 345,
						ActualAmountLogs: []model.ActualAmountLog{
							{
								ActualAmount: 345,
								CreatedAt:    time.Time{}, // Set
							},
						},
						CreatedAt:                time.Time{}, // Set
						InstallmentPayment:       1,
						InstallmentPaymentStatus: model.InstallmentPaymentStatusSuccessful,
						TransRefID:               "", // Set
						IsDeductionLog:           false,
						IsCutOff:                 false,
					},
				}
				baseInstallment.ExpectedDeliveryDate = time.Time{}
				return baseInstallment
			},
			expectedHTTPCode: http.StatusOK,
		},
		{
			name: "Active an one-time installment from a COMPLETED status",
			getRequestURLFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"installmentId": "%s", "status": "ACTIVE", "driverId": "LMFABC002", "amount": 345, "lastAttempt": false, "expectedDeliveryDate": "%s"}`, testdata.ObjectId(4), freezedTime.Format(time.RFC3339))
			},
			getExpectedResponseFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"InstallmentID": "%s"}`, testdata.ObjectId(4))
			},
			targetInstallmentID: testdata.ObjectId(4),
			getExpectedInstallmentFunc: func(baseInstallment model.Installment, freezedTime time.Time) model.Installment {
				baseInstallment.Status = model.InstallmentCompleted
				baseInstallment.Start = time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC) // fixture
				baseInstallment.End = time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC)   // fixture
				baseInstallment.UpdatedAt = time.Time{}                               // Set
				baseInstallment.ExpectedDeliveryDate = freezedTime
				baseInstallment.InstallmentLogs = []model.InstallmentLog{
					{
						Amount: 345,
						ActualAmountLogs: []model.ActualAmountLog{
							{
								ActualAmount: 345,
								CreatedAt:    time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC), // fixture
							},
						},
						CreatedAt:                time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC), // fixture
						InstallmentPayment:       1,
						InstallmentPaymentStatus: model.InstallmentPaymentStatusSuccessful,
						TransRefID:               crypt.EncryptedString("this_is_a_trans_ref_id"), // fixture
						IsDeductionLog:           false,
						IsCutOff:                 false,
					},
				}
				return baseInstallment
			},
			expectedHTTPCode: http.StatusOK,
		},
		{
			name: "Active an one-time installment from a COMPLETED status with no expected delivery date",
			getRequestURLFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"installmentId": "%s", "status": "ACTIVE", "driverId": "LMFABC004", "amount": 345, "lastAttempt": false}`, testdata.ObjectId(6))
			},
			getExpectedResponseFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"code": "INTERNAL_ERROR", "message": "INVALID_DELIVERY_DATE_TO_UPDATE", "timestamp": %v}`, freezedTime.Unix()*1_000_000_000)
			},
			targetInstallmentID: testdata.ObjectId(4),
			getExpectedInstallmentFunc: func(baseInstallment model.Installment, freezedTime time.Time) model.Installment {
				return baseInstallment
			},
			expectedHTTPCode: http.StatusInternalServerError,
		},
		{
			name: "Active an one-time installment from a COMPLETED status with expected delivery date from a past month",
			getRequestURLFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"installmentId": "%s", "status": "ACTIVE", "driverId": "LMFABC004", "amount": 345, "lastAttempt": false, "expectedDeliveryDate": "%s"}`, testdata.ObjectId(6), freezedTime.AddDate(0, -1, 0).Format(time.RFC3339))
			},
			getExpectedResponseFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"InstallmentID": "%s"}`, testdata.ObjectId(6))
			},
			targetInstallmentID: testdata.ObjectId(6),
			getExpectedInstallmentFunc: func(baseInstallment model.Installment, freezedTime time.Time) model.Installment {
				baseInstallment.Status = model.InstallmentCompleted
				baseInstallment.Start = time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC)                                  // fixture
				baseInstallment.End = time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC)                                    // fixture
				baseInstallment.UpdatedAt = time.Time{}                                                                // Set
				baseInstallment.ExpectedDeliveryDate = time.Date(2000, 04, 20, 0, 0, 0, 0, timeutil.BangkokLocation()) // Expect today since the date is on previous fiscal month
				baseInstallment.IsExpectedDeliveryDateOverridden = true
				baseInstallment.InstallmentLogs = []model.InstallmentLog{
					{
						Amount: 345,
						ActualAmountLogs: []model.ActualAmountLog{
							{
								ActualAmount: 345,
								CreatedAt:    time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC), // fixture
							},
						},
						CreatedAt:                time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC), // fixture
						InstallmentPayment:       1,
						InstallmentPaymentStatus: model.InstallmentPaymentStatusSuccessful,
						TransRefID:               crypt.EncryptedString("this_is_a_trans_ref_id"), // fixture
						IsDeductionLog:           false,
						IsCutOff:                 false,
					},
				}
				return baseInstallment
			},
			expectedHTTPCode: http.StatusOK,
			// NOTE:
			// freezingTime = `2000-03-31 10:00:00 +0000 UTC`
			// freezingTime.AddDate(0, -1, 0) = `2000-03-02 10:00:00 +0000 UTC` <-- `2000-02-31 10:00:00 +0000 UTC`
			freezingTime: time.Date(2000, 04, 20, 10, 0, 0, 0, time.UTC),
		},
		{
			name: "Active an one-time installment from a COMPLETED status with expected delivery date from a next month",
			getRequestURLFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"installmentId": "%s", "status": "ACTIVE", "driverId": "LMFABC003", "amount": 345, "lastAttempt": false, "expectedDeliveryDate": "%s"}`, testdata.ObjectId(5), freezedTime.AddDate(0, 1, 0).Format(time.RFC3339))
			},
			getExpectedResponseFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"InstallmentID": "%s"}`, testdata.ObjectId(5))
			},
			targetInstallmentID: testdata.ObjectId(5),
			getExpectedInstallmentFunc: func(baseInstallment model.Installment, freezedTime time.Time) model.Installment {
				baseInstallment.Status = model.InstallmentCompleted
				baseInstallment.Start = time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC) // fixture
				baseInstallment.End = time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC)   // fixture
				baseInstallment.UpdatedAt = time.Time{}                               // Set
				baseInstallment.ExpectedDeliveryDate = freezedTime.AddDate(0, 1, 0)
				baseInstallment.InstallmentLogs = []model.InstallmentLog{
					{
						Amount: 345,
						ActualAmountLogs: []model.ActualAmountLog{
							{
								ActualAmount: 345,
								CreatedAt:    time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC), // fixture
							},
						},
						CreatedAt:                time.Date(2024, 2, 29, 12, 0, 0, 0, time.UTC), // fixture
						InstallmentPayment:       1,
						InstallmentPaymentStatus: model.InstallmentPaymentStatusSuccessful,
						TransRefID:               crypt.EncryptedString("this_is_a_trans_ref_id"), // fixture
						IsDeductionLog:           false,
						IsCutOff:                 false,
					},
				}
				return baseInstallment
			},
			expectedHTTPCode: http.StatusOK,
		},
		{
			name: "Active an tenor installment from a PENDING status with no expected delivery date",
			getRequestURLFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"installmentId": "%s", "status": "ACTIVE", "driverId": "LMFABC005"}`, testdata.ObjectId(7))
			},
			getExpectedResponseFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"code": "INTERNAL_ERROR", "message": "INVALID_EXPECTED_DELIVERY_DATE_TO_ACTIVE_ONE_TIME_INSTALLMENT", "timestamp": %v}`, freezedTime.Unix()*1_000_000_000)
			},
			targetInstallmentID: testdata.ObjectId(7),
			getExpectedInstallmentFunc: func(baseInstallment model.Installment, freezedTime time.Time) model.Installment {
				return baseInstallment
			},
			expectedHTTPCode: http.StatusInternalServerError,
		},
		{
			name: "Active an tenor installment from a PENDING status before cut-off time",
			// The cut-off time is depend on egs.Config.EgsInstallmentCutOffTime config (default 20:30 every day)
			freezingTime: time.Date(2024, 6, 24, 20, 00, 0, 0, timeutil.BangkokLocation()),
			getRequestURLFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"installmentId": "%s", "status": "ACTIVE", "driverId": "LMFABC005", "expectedDeliveryDate": "%s"}`, testdata.ObjectId(7), freezedTime.Format(time.RFC3339))
			},
			getExpectedResponseFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"InstallmentID": "%s"}`, testdata.ObjectId(7))
			},
			targetInstallmentID: testdata.ObjectId(7),
			getExpectedInstallmentFunc: func(baseInstallment model.Installment, freezedTime time.Time) model.Installment {
				baseInstallment.Status = model.InstallmentActive
				baseInstallment.Start = freezedTime
				baseInstallment.End = freezedTime.AddDate(0, 0, 59)
				baseInstallment.UpdatedAt = time.Time{} // Set
				baseInstallment.ExpectedDeliveryDate = freezedTime
				baseInstallment.InstallmentLogs = []model.InstallmentLog{}
				return baseInstallment
			},
			expectedHTTPCode: http.StatusOK,
		},
		{
			name: "Active an tenor installment from a PENDING status after cut-off time",
			// The cut-off time is depend on egs.Config.EgsInstallmentCutOffTime config (default 20:30 every day)
			freezingTime: time.Date(2024, 6, 24, 20, 30, 0, 0, timeutil.BangkokLocation()),
			getRequestURLFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"installmentId": "%s", "status": "ACTIVE", "driverId": "LMFABC005", "expectedDeliveryDate": "%s"}`, testdata.ObjectId(10), freezedTime.Format(time.RFC3339))
			},
			getExpectedResponseFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"InstallmentID": "%s"}`, testdata.ObjectId(10))
			},
			targetInstallmentID: testdata.ObjectId(10),
			getExpectedInstallmentFunc: func(baseInstallment model.Installment, freezedTime time.Time) model.Installment {
				baseInstallment.Status = model.InstallmentActive
				baseInstallment.Start = freezedTime.AddDate(0, 0, 1)
				baseInstallment.End = freezedTime.AddDate(0, 0, 60)
				baseInstallment.UpdatedAt = time.Time{} // Set
				baseInstallment.ExpectedDeliveryDate = freezedTime
				baseInstallment.InstallmentLogs = []model.InstallmentLog{}
				return baseInstallment
			},
			expectedHTTPCode: http.StatusOK,
		},
		{
			name: "Active an tenor installment from a ACTIVE status",
			getRequestURLFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"installmentId": "%s", "status": "ACTIVE", "driverId": "LMFABC006", "expectedDeliveryDate": "%s"}`, testdata.ObjectId(8), freezedTime.Format(time.RFC3339))
			},
			getExpectedResponseFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"code": "INTERNAL_ERROR", "message": "INVALID_TENOR_INSTALLMENT_STATUS_TO_UPDATE", "timestamp": %v}`, freezedTime.Unix()*1_000_000_000)
			},
			targetInstallmentID: testdata.ObjectId(8),
			getExpectedInstallmentFunc: func(baseInstallment model.Installment, freezedTime time.Time) model.Installment {
				return baseInstallment
			},
			expectedHTTPCode: http.StatusInternalServerError,
		},
		{
			name: "Active an tenor installment from a PENDING status with 1st day of month in UTC",
			getRequestURLFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"installmentId": "%s", "status": "ACTIVE", "driverId": "LMFABC005", "expectedDeliveryDate": "2024-02-29T17:00:00Z"}`, testdata.ObjectId(9))
			},
			getExpectedResponseFunc: func(freezedTime time.Time) string {
				return fmt.Sprintf(`{"InstallmentID": "%s"}`, testdata.ObjectId(9))
			},
			targetInstallmentID: testdata.ObjectId(9),
			getExpectedInstallmentFunc: func(baseInstallment model.Installment, freezedTime time.Time) model.Installment {
				baseInstallment.Status = model.InstallmentActive
				baseInstallment.Start = freezedTime
				baseInstallment.End = freezedTime.AddDate(0, 0, 59)
				baseInstallment.UpdatedAt = time.Time{} // Set
				baseInstallment.InstallmentLogs = []model.InstallmentLog{}
				baseInstallment.ExpectedDeliveryDate = time.Date(2024, 2, 29, 17, 0, 0, 0, time.UTC)
				return baseInstallment
			},
			expectedHTTPCode: http.StatusOK,
			freezingTime:     time.Date(2024, 3, 15, 10, 0, 0, 0, time.UTC),
		},
	}

	container := ittest.NewContainer(tt)
	if err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_installment"); err != nil {
		tt.Errorf("Unexpected error initfixture: %v", err)
	}

	for index := range testSet {
		testData := testSet[index]

		mockedTime := time.Now()
		if !testData.freezingTime.IsZero() {
			mockedTime = testData.freezingTime
		}
		xgotimeutil.FreezeWithTime(mockedTime.Unix() * 1000)
		defer xgotimeutil.Unfreeze()

		tt.Run(testData.name, func(t *testing.T) {
			installmentDB := testutil.NewDBHelper(t, container.DBConnectionForTest, "installments")
			installmentObjectID, _ := primitive.ObjectIDFromHex(testData.targetInstallmentID)

			var baseInstallment model.Installment
			if testData.getExpectedInstallmentFunc != nil {
				installmentDB.FindOneByQuery(bson.M{"_id": installmentObjectID}, &baseInstallment)
				baseInstallment = testData.getExpectedInstallmentFunc(baseInstallment, mockedTime)
			}

			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/v1/egs/installments/status")
			gctx.Body().RawJSON(testData.getRequestURLFunc(mockedTime)).Build()
			gctx.Send(container.GinEngineRouter)
			gctx.AssertResponseCode(t, testData.expectedHTTPCode)

			assert.JSONEq(t, testData.getExpectedResponseFunc(mockedTime), gctx.ResponseRecorder.Body.String())

			var updatedInstallment model.Installment
			installmentDB.FindOneByQuery(bson.M{"_id": installmentObjectID}, &updatedInstallment)

			compareInstallment(t, baseInstallment, updatedInstallment)
		})
	}
}
