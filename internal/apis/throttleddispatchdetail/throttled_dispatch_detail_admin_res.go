package throttleddispatchdetail

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type ThrottledDispatchDetailRes struct {
	ZoneID            string  `json:"zoneId"`
	ZoneCode          string  `json:"zoneCode"`
	IntervalInSeconds float64 `json:"intervalInSeconds"`
	Enabled           bool    `json:"enabled"`

	EnabledSearchRadiusOffset bool    `json:"enabledSearchRadiusOffset"`
	SearchRadiusOffsetKM      float64 `json:"searchRadiusOffsetKM"`

	EnableOverrideMatchRestaurantFirstMRThreshold bool    `json:"enableOverrideMatchRestaurantFirstMRThreshold"`
	MatchRestaurantFirstMRThreshold               float64 `json:"matchRestaurantFirstMRThreshold"`

	EnabledH3Partitioning        bool   `json:"enabledH3Partitioning"`
	H3PartitioningResolution     int    `json:"h3PartitioningResolution"`
	MultipleOrderAggressiveLevel string `json:"multipleOrderAggressiveLevel"`
	EnabledMedianSplit           bool   `json:"enabledMedianSplit"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func ThrottledDispatchDetailResFromModel(m model.ThrottledDispatchDetailWithZoneCode) ThrottledDispatchDetailRes {
	return ThrottledDispatchDetailRes{
		ZoneID:                    m.ZoneID.Hex(),
		ZoneCode:                  m.ZoneCode,
		IntervalInSeconds:         m.Interval.Seconds(),
		Enabled:                   m.Enabled,
		EnabledSearchRadiusOffset: m.EnabledSearchRadiusOffset,
		SearchRadiusOffsetKM:      m.SearchRadiusOffsetKM,
		EnableOverrideMatchRestaurantFirstMRThreshold: m.EnableOverrideMatchRestaurantFirstMRThreshold,
		MatchRestaurantFirstMRThreshold:               m.MatchRestaurantFirstMRThreshold,
		EnabledH3Partitioning:                         m.EnabledH3Partitioning,
		H3PartitioningResolution:                      m.GetH3PartitioningResolution(),
		MultipleOrderAggressiveLevel:                  m.MultipleOrderAggressiveLevel,
		CreatedAt:                                     m.CreatedAt,
		UpdatedAt:                                     m.UpdatedAt,
		EnabledMedianSplit:                            m.EnabledMedianSplit,
	}
}
