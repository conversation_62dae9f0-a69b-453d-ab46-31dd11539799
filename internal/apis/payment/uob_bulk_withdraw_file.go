package payment

import (
	"bytes"
	"encoding/csv"
)

const (
	SUCCESSFUL = "SUCCESSFUL"
	FAILED     = "FAILED"
	PROCESSING = "PROCESSING"
)

type CSVUOBWithdraw struct{}

func NewCSVUOBWithdraw() *CSVUOBWithdraw {
	return &CSVUOBWithdraw{}
}

func (c *CSVUOBWithdraw) Parse(data *UobBulkWithdrawResultRes) (*bytes.Buffer, error) {
	buf := bytes.NewBuffer([]byte{})

	writer := csv.NewWriter(buf)
	defer writer.Flush()

	_ = writer.Write([]string{"Driver ID", "First Name", "Last Name", "Account Number", "Account Name", "Bank Code", "Requested Amount", "Transaction Reference Number", "UOB Make Payment", "Update Transaction Status", "Remark"})

	for _, d := range data.Successes {
		ext := d.ExportData
		row := []string{ext.Driver<PERSON>, ext.FirstName, ext.LastName, ext.AccountNumber, ext.AccountName, ext.BankCode, ext.RequestedAmount, ext.RefID, SUCCESSFUL, SUCCESSFUL, ext.Remark}
		err := writer.Write(row)
		if err != nil {
			return nil, err
		}
	}

	for _, d := range data.Failures {
		ext := d.ExportData
		makePaymentStatus := FAILED
		updateTransaction := FAILED
		if d.IsUobMakePaymentSuccess {
			makePaymentStatus = SUCCESSFUL
		}

		if d.IsUpdateWithdrawTransactionStatusSuccess {
			updateTransaction = SUCCESSFUL
		}

		row := []string{ext.DriverID, ext.FirstName, ext.LastName, ext.AccountNumber, ext.AccountName, ext.BankCode, ext.RequestedAmount, ext.RefID, makePaymentStatus, updateTransaction, ext.Remark}
		err := writer.Write(row)
		if err != nil {
			return nil, err
		}
	}

	for _, d := range data.Processing {
		ext := d.ExportData
		row := []string{ext.DriverID, ext.FirstName, ext.LastName, ext.AccountNumber, ext.AccountName, ext.BankCode, ext.RequestedAmount, ext.RefID, FAILED, PROCESSING, ext.Remark}
		err := writer.Write(row)
		if err != nil {
			return nil, err
		}
	}

	return buf, nil
}
