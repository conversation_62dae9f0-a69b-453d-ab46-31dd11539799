package payment

import (
	"encoding/json"
	"time"

	"github.com/pkg/errors"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type ListApprovalRes []ApprovalRes

func NewListApprovalRes(approvals []model.Approval) ListApprovalRes {
	res := make(ListApprovalRes, len(approvals))
	for i, a := range approvals {
		res[i] = *NewApprovalRes(a)
	}
	return res
}

type ApprovalRes struct {
	ApprovalID string         `json:"approvalId"`
	RequestBy  string         `json:"requestedBy"`
	Status     string         `json:"status"`
	Remarks    []model.Remark `json:"remarks"`
	Info       interface{}    `json:"info"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func NewApprovalRes(approval model.Approval) *ApprovalRes {
	var info interface{}
	switch t := approval.Info.(type) {
	case *model.CreditPurchaseInfo:
		info = NewCreditPurchaseInfoRes(*t)
	case *model.CreditVoidInfo:
		info = NewCreditVoidInfoRes(*t)
	case *model.CreditVoidPurchaseInfo:
		info = NewCreditVoidPurchaseInfoRes(*t)
	case *model.CreditVoidChargeInfo:
		info = NewCreditVoidChargeInfoRes(*t)
	case *model.CreditChargeInfo:
		info = NewCreditChargeInfoRes(*t)
	case *model.WalletVoidInfo:
		info = NewWalletVoidInfoRes(*t)
	case *model.WalletVoidAddInfo:
		info = NewWalletVoidAddInfoRes(*t)
	}

	return &ApprovalRes{
		ApprovalID: approval.ApprovalID,
		RequestBy:  approval.RequestBy,
		Info:       info,
		Status:     string(approval.Status),
		Remarks:    approval.Remarks,
		CreatedAt:  approval.CreatedAt,
		UpdatedAt:  approval.UpdatedAt,
	}
}

func (caq *ApprovalRes) UnmarshalJSON(b []byte) error {
	approve := struct {
		ApprovalID string         `json:"approvalId"`
		RequestBy  string         `json:"requestedBy"`
		Status     string         `json:"status"`
		Remarks    []model.Remark `json:"remarks"`
		CreatedAt  time.Time      `json:"createdAt"`
		UpdatedAt  time.Time      `json:"updatedAt"`

		Info struct {
			Category model.ApprovalCategory `json:"category"`
			Action   model.ApprovalAction   `json:"action"`
		} `json:"info"`
	}{}

	if err := json.Unmarshal(b, &approve); err != nil {
		return err
	}

	info := struct {
		Info interface{} `json:"info"`
	}{}
	switch {
	case approve.Info.Category == model.CreditCategory && approve.Info.Action == model.PurchaseAction:
		info.Info = &CreditPurchaseInfoRes{}
	case approve.Info.Category == model.CreditCategory && approve.Info.Action == model.VoidAction:
		info.Info = &CreditVoidInfoRes{}
	case approve.Info.Category == model.CreditCategory && approve.Info.Action == model.VoidPurchaseAction:
		info.Info = &CreditVoidPurchaseInfoRes{}
	case approve.Info.Category == model.CreditCategory && approve.Info.Action == model.VoidChargeAction:
		info.Info = &CreditVoidChargeInfoRes{}
	case approve.Info.Category == model.CreditCategory && approve.Info.Action == model.ChargeAction:
		info.Info = &CreditChargeInfoRes{}
	case approve.Info.Category == model.WalletCategory && approve.Info.Action == model.VoidAction:
		info.Info = &WalletVoidInfoRes{}
	case approve.Info.Category == model.WalletCategory && approve.Info.Action == model.VoidAddAction:
		info.Info = &WalletVoidAddInfoRes{}
	default:
		return errors.New("unknown category and action")
	}

	if err := json.Unmarshal(b, &info); err != nil {
		return err
	}

	caq.ApprovalID = approve.ApprovalID
	caq.RequestBy = approve.RequestBy
	caq.Status = approve.Status
	caq.Remarks = approve.Remarks
	caq.CreatedAt = approve.CreatedAt
	caq.UpdatedAt = approve.UpdatedAt
	caq.Info = info.Info

	return nil
}

type ApprovalInfoRes struct {
	Category string `json:"category"`
	Action   string `json:"action"`
}

type CreditPurchaseInfoRes struct {
	ApprovalInfoRes
	DriverID       string                `json:"purchaseDriverId"`
	TransRefID     crypt.EncryptedString `json:"transRefId"`
	Amount         types.Money           `json:"amount"`
	IsFree         bool                  `json:"isFree,omitempty"`
	ExpirationDate time.Time             `json:"expirationDate,omitempty"`
}

func NewCreditPurchaseInfoRes(model model.CreditPurchaseInfo) *CreditPurchaseInfoRes {
	return &CreditPurchaseInfoRes{
		ApprovalInfoRes: ApprovalInfoRes{
			Category: string(model.Category()),
			Action:   string(model.Action()),
		},
		DriverID:       model.DriverID(),
		TransRefID:     model.TxnRefID(),
		Amount:         model.Amount(),
		IsFree:         model.IsFree(),
		ExpirationDate: model.ExpirationDate(),
	}
}

type CreditVoidInfoRes struct {
	ApprovalInfoRes
	VoidTxnID    string  `json:"voidTransactionId"`
	VoidDriverID string  `json:"voidDriverId"`
	Amount       float64 `json:"amount"`
}

func NewCreditVoidInfoRes(model model.CreditVoidInfo) *CreditVoidInfoRes {
	return &CreditVoidInfoRes{
		ApprovalInfoRes: ApprovalInfoRes{
			Category: string(model.Category()),
			Action:   string(model.Action()),
		},
		VoidTxnID:    model.TxnID(),
		VoidDriverID: model.DriverID(),
		Amount:       model.Amount().Float64(),
	}
}

type CreditVoidPurchaseInfoRes struct {
	ApprovalInfoRes
	VoidTxnID        string      `json:"voidTransactionId"`
	VoidDriverID     string      `json:"voidDriverId"`
	PurchaseDriverID string      `json:"purchaseDriverId"`
	Amount           types.Money `json:"amount"`
}

func NewCreditVoidPurchaseInfoRes(model model.CreditVoidPurchaseInfo) *CreditVoidPurchaseInfoRes {
	return &CreditVoidPurchaseInfoRes{
		ApprovalInfoRes: ApprovalInfoRes{
			Category: string(model.Category()),
			Action:   string(model.Action()),
		},
		VoidTxnID:        model.TxnID(),
		VoidDriverID:     model.DriverID(),
		PurchaseDriverID: model.PurchaseDriverID(),
		Amount:           model.Amount(),
	}
}

type CreditVoidChargeInfoRes struct {
	ApprovalInfoRes
	VoidTxnID string      `json:"voidTransactionId"`
	Amount    types.Money `json:"amount"`
	OrderID   string      `json:"orderId"`
}

func NewCreditVoidChargeInfoRes(model model.CreditVoidChargeInfo) *CreditVoidChargeInfoRes {
	return &CreditVoidChargeInfoRes{
		ApprovalInfoRes: ApprovalInfoRes{
			Category: string(model.Category()),
			Action:   string(model.Action()),
		},
		VoidTxnID: model.TxnID(),
		Amount:    model.Amount(),
		OrderID:   model.OrderID(),
	}
}

type CreditChargeInfoRes struct {
	ApprovalInfoRes
	Amount     types.Money           `json:"amount"`
	OrderID    string                `json:"orderId"`
	SubType    string                `json:"subType"`
	DriverID   string                `json:"driverId"`
	TransRefID crypt.EncryptedString `json:"transRefId"`
}

func NewCreditChargeInfoRes(model model.CreditChargeInfo) *CreditChargeInfoRes {
	return &CreditChargeInfoRes{
		ApprovalInfoRes: ApprovalInfoRes{
			Category: string(model.Category()),
			Action:   string(model.Action()),
		},
		Amount:     model.Amount(),
		OrderID:    model.OrderID(),
		SubType:    string(model.SubType()),
		DriverID:   model.DriverID(),
		TransRefID: model.TransRefID(),
	}
}

type WalletVoidInfoRes struct {
	ApprovalInfoRes
	VoidTxnID    string  `json:"voidTransactionId,omitempty"`
	VoidDriverID string  `json:"voidDriverId"`
	Amount       float64 `json:"amount"`
}

func NewWalletVoidInfoRes(model model.WalletVoidInfo) *WalletVoidInfoRes {
	return &WalletVoidInfoRes{
		ApprovalInfoRes: ApprovalInfoRes{
			Category: string(model.Category()),
			Action:   string(model.Action()),
		},
		VoidTxnID:    model.TxnID(),
		VoidDriverID: model.DriverID(),
		Amount:       model.Amount().Float64(),
	}
}

type WalletVoidAddInfoRes struct {
	ApprovalInfoRes
	VoidTxnID    string      `json:"voidTransactionId,omitempty"`
	VoidDriverID string      `json:"voidDriverId"`
	Amount       types.Money `json:"amount"`
}

func NewWalletVoidAddInfoRes(model model.WalletVoidAddInfo) *WalletVoidAddInfoRes {
	return &WalletVoidAddInfoRes{
		ApprovalInfoRes: ApprovalInfoRes{
			Category: string(model.Category()),
			Action:   string(model.Action()),
		},
		VoidTxnID:    model.TxnID(),
		VoidDriverID: model.DriverID(),
		Amount:       model.Amount(),
	}
}
