package payment

import (
	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

type TransactionSchemeAPI struct {
	txnSchemeRepo repository.TransactionSchemeRepository
	auditLogRepo  repository.AuditLogRepository
}

func ProvideTransactionSchemeAPI(txnSchemeRepo repository.TransactionSchemeRepository, auditLogRepository repository.AuditLogRepository) *TransactionSchemeAPI {
	return &TransactionSchemeAPI{
		txnSchemeRepo: txnSchemeRepo,
		auditLogRepo:  auditLogRepository,
	}
}

func (t TransactionSchemeAPI) List(gctx *gin.Context) {
	var req ListTransactionSchemesReq
	if err := gctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	ctx := gctx.Request.Context()
	page, size, skip := utils.ParsePaginationWithLimit(gctx)
	query := req.toQuery()
	if _, err := query.Query(); err != nil {
		apiutil.ErrBadRequest(gctx, apiErrors.ErrInvalidRequest(err))
		return
	}

	// to check if there is a next page
	sizePlus := size + 1
	actualtTxnSchemes, err := t.txnSchemeRepo.Find(ctx, query, skip, sizePlus, []string{"name", "-created_at"}, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	fakeCount := ((page - 1) * size) + len(actualtTxnSchemes)
	resTxnSchemes := actualtTxnSchemes
	hasNext := len(actualtTxnSchemes) == sizePlus
	if hasNext {
		fakeCount = (page * size) + 1
		// remove a last item that use for checking a next page
		resTxnSchemes = actualtTxnSchemes[:len(actualtTxnSchemes)-1]
	}

	res := make([]TransactionSchemeRes, len(resTxnSchemes))
	for i, txnScheme := range resTxnSchemes {
		res[i] = NewTransactionSchemeRes(txnScheme)
	}
	apiutil.OKList(gctx, res, fakeCount)
}

func (t TransactionSchemeAPI) Get(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	id := gctx.Param("id")

	txnScheme, err := t.txnSchemeRepo.FindOneByID(ctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
		} else {
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}
	apiutil.OK(gctx, NewTransactionSchemeRes(*txnScheme))
}

func (t TransactionSchemeAPI) Create(gctx *gin.Context) {
	var req CreateTransactionSchemeReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if err := req.Validate(); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	ctx := gctx.Request.Context()
	txnScheme := req.ToTransactionScheme()
	query := persistence.NewTransactionSchemeQuery().WithType(txnScheme.Type).WithSubType(txnScheme.SubType)
	isExist, err := t.txnSchemeRepo.IsExistOnAllScheme(ctx, query, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	if isExist {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, model.ErrTxnSchemeAlreadyExist))
		return
	}

	if err := t.txnSchemeRepo.Create(ctx, txnScheme); err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	if err := t.insertAuditLog(gctx, model.TxnSchemeCreateAudit, nil, txnScheme); err != nil {
		_ = gctx.Error(apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.Created(gctx, apiutil.EmptyBody())
}

func (t TransactionSchemeAPI) Update(gctx *gin.Context) {
	var req UpdateTransactionSchemeReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	ctx := gctx.Request.Context()
	id := gctx.Param("id")
	txnScheme, err := t.txnSchemeRepo.FindOneByID(ctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
		} else {
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	txnSchemeBeforeUpdate := *txnScheme
	req.Update(txnScheme)
	if err := t.txnSchemeRepo.Update(ctx, txnScheme); err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	if err := t.insertAuditLog(gctx, model.TxnSchemeUpdateAudit, txnSchemeBeforeUpdate, txnScheme); err != nil {
		_ = gctx.Error(apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.NoContent(gctx)
}

func (t TransactionSchemeAPI) Archived(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	id := gctx.Param("id")
	txnScheme, err := t.txnSchemeRepo.FindOneByID(ctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
		} else {
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	txnSchemeBeforeUpdate := *txnScheme
	txnScheme.Archived()
	if err := t.txnSchemeRepo.Update(ctx, txnScheme); err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	if err := t.insertAuditLog(gctx, model.TxnSchemeArchivedAudit, txnSchemeBeforeUpdate, txnScheme); err != nil {
		_ = gctx.Error(apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}
	apiutil.NoContent(gctx)
}

func (t TransactionSchemeAPI) insertAuditLog(gctx *gin.Context, action model.AuditAction, before interface{}, after interface{}) error {
	ctx := gctx.Request.Context()
	email := "n/a"
	if user, isExist := auth.GetAdminUserFromGctx(gctx); isExist {
		email = user.GetEmail()
	}
	auditLog := model.NewAuditLog(
		model.AuditLogActor{ID: email},
		model.AuditObject{ObjectType: model.TxnSchemeAuditObject, ID: "n/a"},
		action,
		before,
		after,
	)
	return t.auditLogRepo.Insert(ctx, &auditLog)
}
