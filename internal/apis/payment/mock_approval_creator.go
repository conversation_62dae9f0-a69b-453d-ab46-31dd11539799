// Code generated by MockGen. DO NOT EDIT.
// Source: ./approval_creator.go

// Package payment is a generated GoMock package.
package payment

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockApprovalCreator is a mock of ApprovalCreator interface.
type MockApprovalCreator struct {
	ctrl     *gomock.Controller
	recorder *MockApprovalCreatorMockRecorder
}

// MockApprovalCreatorMockRecorder is the mock recorder for MockApprovalCreator.
type MockApprovalCreatorMockRecorder struct {
	mock *MockApprovalCreator
}

// NewMockApprovalCreator creates a new mock instance.
func NewMockApprovalCreator(ctrl *gomock.Controller) *MockApprovalCreator {
	mock := &MockApprovalCreator{ctrl: ctrl}
	mock.recorder = &MockApprovalCreatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockApprovalCreator) EXPECT() *MockApprovalCreatorMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockApprovalCreator) Create(ctx context.Context, approvalReq CreateApprovalReq) (*model.Approval, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, approvalReq)
	ret0, _ := ret[0].(*model.Approval)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockApprovalCreatorMockRecorder) Create(ctx, approvalReq interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockApprovalCreator)(nil).Create), ctx, approvalReq)
}
