package payment

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/metrics/push"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type UOBPayoutMetric struct {
	featureFlagService   featureflag.Service
	transactionDataStore persistence.TransactionDataStore
	cfg                  config.PaymentConfig

	mPusher          push.MetricPusher
	statusGauge      push.GaugeMetricPush
	autoApproveGauge push.GaugeMetricPush
}

func ProvideUOBPayoutMetric(
	transactionDataStore persistence.TransactionDataStore,
	cfg config.PaymentConfig,
	featureflagService featureflag.Service,
) *UOBPayoutMetric {
	mPusher := push.NewDefaultMetricPusher("driver-payout-withdrawal-metric", "driver_payout_withdrawal_metric")
	statusGauge := push.NewGaugeMetricPush(
		mPusher,
		"uob_payout_transaction_counter",
		"Number of all UOB payout transactions by status",
		"status",
	)

	autoApproveGauge := push.NewGaugeMetricPush(
		mPusher,
		"uob_payout_auto_approve_status",
		"Status of UOB payout auto approve, 1 if enabled, 0 if disabled",
	)

	return &UOBPayoutMetric{
		featureFlagService:   featureflagService,
		transactionDataStore: transactionDataStore,
		cfg:                  cfg,
		mPusher:              mPusher,
		statusGauge:          statusGauge,
		autoApproveGauge:     autoApproveGauge,
	}
}

func (m *UOBPayoutMetric) Run(ctx context.Context) error {
	m.mPusher.Add(ctx)

	var pendingCount, processingCount, successCount, failCount int

	last24Hours := timeutil.BangkokNow().Add(-24 * time.Hour)
	res, err := m.GetWithdrawalTransactionCount(ctx, last24Hours)
	if err != nil {
		logx.Error().Err(err).Msg("[UOBPayoutMetric]: failed to count withdrawal transactions from database")
		return err
	}

	for _, r := range res {
		switch r.Status {
		case model.PendingTransactionStatus:
			pendingCount = r.Count
		case model.ProcessingTransactionStatus:
			processingCount = r.Count
		case model.SuccessTransactionStatus:
			successCount = r.Count
		case model.FailTransactionStatus:
			failCount = r.Count
		}
	}

	m.statusGauge.Observe(ctx, float64(pendingCount), push.NewAttribute("status", "pending"))
	m.statusGauge.Observe(ctx, float64(processingCount), push.NewAttribute("status", "processing"))
	m.statusGauge.Observe(ctx, float64(successCount), push.NewAttribute("status", "success"))
	m.statusGauge.Observe(ctx, float64(failCount), push.NewAttribute("status", "fail"))

	autoApprove := m.featureFlagService.IsEnabledWithDefaultTrue(ctx, featureflag.IsUOBPayoutAutoApprovalTransactionEnabled.Name)
	if autoApprove {
		m.autoApproveGauge.Observe(ctx, 1)
	} else {
		m.autoApproveGauge.Observe(ctx, 0)
	}
	return nil
}

type WithdrawalTransactionCount struct {
	Status model.TransactionStatus `bson:"_id"`
	Count  int                     `bson:"count"`
}

// GetWithdrawalTransactionCount returns the count of withdrawal transactions
// that are created after the given time. The result is grouped by status.
func (m *UOBPayoutMetric) GetWithdrawalTransactionCount(ctx context.Context, createAfterAt time.Time) ([]WithdrawalTransactionCount, error) {
	var result []WithdrawalTransactionCount
	err := m.transactionDataStore.Aggregate(ctx, []bson.M{
		{
			"$match": bson.M{
				"action":        model.WithdrawTransactionAction,
				"info.type":     model.WithdrawTransactionType,
				"info.category": model.WalletTransactionCategory,
				"created_at": bson.M{
					"$gte": createAfterAt,
				},
			},
		},
		{
			"$group": bson.M{
				"_id":   "$status",
				"count": bson.M{"$sum": 1},
			},
		},
	}, &result, repository.WithReadSecondaryPreferred())
	return result, err
}

func makeTransactionQuery(status model.TransactionStatus) *persistence.TransactionQuery {
	q := persistence.TransactionQuery{
		InfoType: model.WithdrawTransactionType,
		Category: model.WalletTransactionCategory,
		Statuses: []model.TransactionStatus{status},
	}

	return &q
}
