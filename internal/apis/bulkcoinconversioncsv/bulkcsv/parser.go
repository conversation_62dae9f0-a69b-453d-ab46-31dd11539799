package bulkcsv

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"

	"github.com/jszwec/csvutil"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	internalError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
)

func parseCSVFile(reader io.Reader) (BulkImportCSVData, error) {
	csvReader := csv.NewReader(reader)

	dec, err := csvutil.NewDecoder(csvReader)
	if err != nil {
		if errors.Is(err, io.EOF) {
			return BulkImportCSVData{}, ErrEmptyFileOrHeader()
		}
		err = apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, fmt.Errorf("create csv decoder error: %w", err))
		return BulkImportCSVData{}, err
	}

	dec.DisallowMissingColumns = true

	var (
		idx            int
		processingData BulkImportCSVData
		dataRow        []CoinConversionCSV
		ids            []string
		merr           = NewCSVMultipleError()
	)

	uniqueRegionMap := map[string]CoinConversionCSV{}

	for {
		rowIndex := idx + 1

		var row CoinConversionCSV
		err := dec.Decode(&row)
		if err != nil {
			if err == io.EOF {
				break
			}

			var missingColumnsErr *csvutil.MissingColumnsError
			if errors.As(err, &missingColumnsErr) {
				return BulkImportCSVData{}, ErrMissingColumns(missingColumnsErr)
			}

			var invalidDecodeErr *csvutil.InvalidDecodeError
			if errors.As(err, &invalidDecodeErr) {
				err = apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, fmt.Errorf("unable to decode on row %v: %v", rowIndex, err.Error()))
				return BulkImportCSVData{}, err
			}

			merr.AddError(internalError.NewCSVRowError(
				rowIndex,
				"-",
				err.Error(),
			))

			idx++
			continue
		}

		merr.AddSuccess(SuccessRow{
			Row:    rowIndex,
			Region: row.Region,
		})
		dataRow = append(dataRow, row)
		uniqueRegionMap[row.Region] = row
		ids = append(ids, row.Region)
		idx++
	}

	if merr.HasError() {
		return BulkImportCSVData{}, fmt.Errorf("row validation error: %w", merr)
	}

	processingData.RawRows = dataRow
	processingData.SchemeSize = len(dataRow)
	processingData.UniqueRegions = ids
	processingData.UniqueRegionsMap = uniqueRegionMap
	return processingData, nil
}
