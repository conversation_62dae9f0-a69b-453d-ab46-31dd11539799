package partnerAuth

import (
	"crypto/x509"
	"fmt"
	"strings"

	"github.com/beevik/etree"
	dsig "github.com/russellhaering/goxmldsig"
)

func (auth *CitiAuthenticationService) SigningXML() (string, error) {
	if auth.signinContext == nil {
		return "", fmt.Errorf("SigningXML signinContext is nil")
	}

	auth.signinContext.IdAttribute = "ID"
	auth.signinContext.Prefix = ""
	auth.signinContext.Canonicalizer = dsig.MakeC14N10RecCanonicalizer()

	documentToSign := etree.NewDocument()
	if err := documentToSign.ReadFromString(auth.CitiAuthPayload); err != nil {
		return "", err
	}

	// Sign the element
	signedElement, err := auth.signinContext.SignEnveloped(documentToSign.Root())
	if err != nil {
		return "", err
	}

	certIssuerName := strings.Split(auth.LmwnCert.Issuer.ToRDNSequence().String(), ",")
	issuerSerial := etree.NewElement("X509IssuerSerial")
	issuerName := etree.NewElement("X509IssuerName")
	issuerName.SetText(strings.Join(certIssuerName, ", "))
	serialNumber := etree.NewElement("X509SerialNumber")
	serialNumber.SetText(auth.LmwnCert.SerialNumber.String())
	issuerSerial.AddChild(issuerName)
	issuerSerial.AddChild(serialNumber)
	signedElement.FindElement("/Signature/KeyInfo/X509Data").InsertChildAt(0, issuerSerial)

	// Serialize the signed element. It is important not to modify the element
	// after it has been signed - even pretty-printing the XML will invalidate
	// the signature.
	doc := etree.NewDocument()
	doc.SetRoot(signedElement)
	str, err := doc.WriteToString()
	if err != nil {
		return "", err
	}

	str = strings.ReplaceAll(str, `"/>`, `" />`)
	return str, nil
}

// ---

func (auth *CitiAuthenticationService) VerifySignedXML(xmlStr string) (bool, string, error) {
	documentToSign := etree.NewDocument()
	if err := documentToSign.ReadFromString(xmlStr); err != nil {
		return false, "", err
	}

	rootEl := documentToSign.Root()
	return auth.VerifySignedXMLByEl(rootEl)
}

func (auth *CitiAuthenticationService) VerifySignedXMLByEl(rootEl *etree.Element) (bool, string, error) {
	ctx := dsig.NewDefaultValidationContext(&dsig.MemoryX509CertificateStore{
		Roots: []*x509.Certificate{
			auth.CitiSigningCert,
		},
	})

	// It is important to only use the returned validated element.
	// See: https://www.w3.org/TR/xmldsig-bestpractices/#check-what-is-signed
	rootEl.Attr = []etree.Attr{}
	validated, err := ctx.Validate(rootEl)
	if err != nil {
		return false, "", err
	}

	doc := etree.NewDocument()
	doc.SetRoot(validated)
	str, err := doc.WriteToString()
	if err != nil {
		return false, "", err
	}

	return true, str, nil
}
