package region

import (
	"errors"
	"fmt"
	"net/http/httptest"
	"net/url"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestRegionAPI_ListRegistrationProvinces(t *testing.T) {
	provinceNames := []string{"<province-0>", "<province-1>"}

	reqCtx := func(req ListRegistrationProvinceRequest) (*gin.Context, *httptest.ResponseRecorder) {
		query := url.Values{}
		if req.Category != 0 {
			query.Add("category", strconv.Itoa(req.Category))
		}

		reqURL := fmt.Sprintf("/regions/registration-provinces?%s", query.Encode())
		gctx, recorder := testutil.TestRequestContext("GET", reqURL, nil)
		return gctx, recorder
	}

	t.Run("respond only non whitelist-autoapprove provinces", func(t *testing.T) {
		t.Parallel()

		provinceAPI, deps, finish := newTestRegionAPI(t)
		defer finish()

		deps.ProvinceRepository.EXPECT().
			List(gomock.Any(), repository.ProvinceQuery{IsWhitelistAutoApprove: types.NewBool(false)}).
			Return([]model.Province{{Name: provinceNames[0]}, {Name: provinceNames[1]}}, nil)

		gctx, recorder := reqCtx(ListRegistrationProvinceRequest{})

		provinceAPI.ListRegistrationProvince(gctx)

		assert.Empty(t, gctx.Errors.Errors())
		var res []ProvinceResponse
		testutil.DecodeJSON(t, recorder.Body, &res)
		assert.Len(t, res, 2)
		assert.Equal(t, provinceNames[0], res[0].Name)
		assert.Equal(t, provinceNames[1], res[1].Name)
	})

	t.Run("can filter by category", func(t *testing.T) {
		t.Parallel()

		tcs := []struct {
			req           ListRegistrationProvinceRequest
			expectedQuery repository.ProvinceQuery
		}{
			{
				req:           ListRegistrationProvinceRequest{Category: 1},
				expectedQuery: repository.ProvinceQuery{IsWhitelistAutoApprove: types.NewBool(true)},
			},
			{
				req:           ListRegistrationProvinceRequest{Category: 0},
				expectedQuery: repository.ProvinceQuery{IsWhitelistAutoApprove: types.NewBool(false)},
			},
		}

		for _, tc := range tcs {
			_tc := tc
			t.Run(fmt.Sprintf("%v", _tc.req), func(t *testing.T) {
				t.Parallel()

				provinceAPI, deps, finish := newTestRegionAPI(t)
				defer finish()

				deps.ProvinceRepository.EXPECT().
					List(gomock.Any(), _tc.expectedQuery).
					Return([]model.Province{{Name: provinceNames[0]}, {Name: provinceNames[1]}}, nil)

				gctx, recorder := reqCtx(_tc.req)
				provinceAPI.ListRegistrationProvince(gctx)

				assert.Empty(t, gctx.Errors.Errors())
				var res []ProvinceResponse
				testutil.DecodeJSON(t, recorder.Body, &res)
				assert.Len(t, res, 2)
				assert.Equal(t, provinceNames[0], res[0].Name)
				assert.Equal(t, provinceNames[1], res[1].Name)
			})
		}

	})

	t.Run("respond internal error on repository failure", func(t *testing.T) {
		t.Parallel()

		provinceAPI, deps, finish := newTestRegionAPI(t)
		defer finish()

		deps.ProvinceRepository.EXPECT().
			List(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("repository error"))

		gctx, _ := reqCtx(ListRegistrationProvinceRequest{})
		provinceAPI.ListRegistrationProvince(gctx)

		lastErr := gctx.Errors.Last().Err
		assert.Error(t, lastErr)
		apiErr := lastErr.(*api.Error)
		assert.Equal(t, api.ERRCODE_INTERNAL_ERROR, apiErr.Code)
	})
}

type regionAPIDeps struct {
	ProvinceRepository *mock_repository.MockProvinceRepository
}

func newTestRegionAPI(t *testing.T) (RegionAPI, regionAPIDeps, func()) {
	ctrl := gomock.NewController(t)
	provinceRepository := mock_repository.NewMockProvinceRepository(ctrl)
	cfg := &AtomicRegionConfig{}
	cfg.Parse()
	return RegionAPI{provinceRepository: provinceRepository, cfg: cfg}, regionAPIDeps{ProvinceRepository: provinceRepository}, ctrl.Finish
}
