//go:build integration_test
// +build integration_test

package region_test

import (
	"net/http"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"

	regionapi "git.wndv.co/lineman/fleet-distribution/internal/apis/region"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestIntegrationRegionAPI_List(t *testing.T) {
	t.Run("province list when HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST is on, filters out provinces that is_whitelist_auto_approve=y when cat=0", func(t *testing.T) {
		t.<PERSON>llel()
		container := ittest.NewContainer(t)

		tcs := []testCase{
			{url: "/v1/regions/registration-provinces", expectedProvinces: []string{"อยุธยา"}},
			{url: "/v1/regions/registration-provinces?category=0", expectedProvinces: []string{"อยุธยา"}},
			{url: "/v1/regions/registration-provinces?category=1", expectedProvinces: []string{"กรุงเทพมหานคร", "ชลบุรี"}},
		}

		assertListProvince(t, container, tcs)
	})

	t.Run("province list when HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST is off", func(t *testing.T) {
		os.Setenv("HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST", "false")
		defer os.Setenv("HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST", "true")
		container := ittest.NewContainer(t)

		tcs := []testCase{
			{url: "/v1/regions/registration-provinces", expectedProvinces: []string{"กรุงเทพมหานคร", "อยุธยา", "ชลบุรี"}},
			{url: "/v1/regions/registration-provinces?category=0", expectedProvinces: []string{"กรุงเทพมหานคร", "อยุธยา", "ชลบุรี"}},
			{url: "/v1/regions/registration-provinces?category=1", expectedProvinces: []string{"กรุงเทพมหานคร", "ชลบุรี"}},
		}

		assertListProvince(t, container, tcs)
	})

	t.Run("filtered out provinces should still show up for update (driver logged in)", func(t *testing.T) {
		t.Parallel()
		container := ittest.NewContainer(t)

		tcs := []testCase{
			{url: "/v1/regions/registration-provinces", expectedProvinces: []string{"กรุงเทพมหานคร", "อยุธยา", "ชลบุรี", "นนทบุรี"}, asLoggedIn: true},
			{url: "/v1/regions/registration-provinces?category=0", expectedProvinces: []string{"กรุงเทพมหานคร", "อยุธยา", "ชลบุรี", "นนทบุรี"}, asLoggedIn: true},
			{url: "/v1/regions/registration-provinces?category=1", expectedProvinces: []string{"กรุงเทพมหานคร", "อยุธยา", "ชลบุรี", "นนทบุรี"}, asLoggedIn: true},
		}

		assertListProvince(t, container, tcs)
	})

	t.Run("province list with all is_whitelist_auto_approve is false", func(t *testing.T) {
		t.Run("category 1 shows every provinces HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST is true", func(t *testing.T) {
			t.Parallel()
			container := ittest.NewContainer(t)
			container.ProvincesTestData.SetAllWhitelistAutoApprove(false)

			tcs := []testCase{
				{url: "/v1/regions/registration-provinces?category=1", expectedProvinces: []string{"กรุงเทพมหานคร", "อยุธยา", "ชลบุรี"}},
			}

			assertListProvince(t, container, tcs)
		})
		t.Run("category 1 shows every provinces HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST is false", func(t *testing.T) {
			os.Setenv("HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST", "false")
			defer os.Setenv("HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST", "true")
			container := ittest.NewContainer(t)
			container.ProvincesTestData.SetAllWhitelistAutoApprove(false)

			tcs := []testCase{
				{url: "/v1/regions/registration-provinces?category=1", expectedProvinces: []string{"กรุงเทพมหานคร", "อยุธยา", "ชลบุรี"}},
			}

			assertListProvince(t, container, tcs)
		})
	})

	t.Run("province list with all is_whitelist_auto_approve is true", func(t *testing.T) {
		t.Run("when HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST is true, category 0 hides every provinces", func(t *testing.T) {
			t.Parallel()
			container := ittest.NewContainer(t)
			container.ProvincesTestData.SetAllWhitelistAutoApprove(true)

			tcs := []testCase{
				{url: "/v1/regions/registration-provinces", expectedProvinces: []string{}},
				{url: "/v1/regions/registration-provinces?category=0", expectedProvinces: []string{}},
			}

			assertListProvince(t, container, tcs)
		})
		t.Run("when HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST is false, category 0 shows every provinces (trivial case)", func(t *testing.T) {
			os.Setenv("HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST", "false")
			defer os.Setenv("HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST", "true")
			container := ittest.NewContainer(t)
			container.ProvincesTestData.SetAllWhitelistAutoApprove(true)

			tcs := []testCase{
				{url: "/v1/regions/registration-provinces", expectedProvinces: []string{"กรุงเทพมหานคร", "อยุธยา", "ชลบุรี"}},
				{url: "/v1/regions/registration-provinces?category=0", expectedProvinces: []string{"กรุงเทพมหานคร", "อยุธยา", "ชลบุรี"}},
			}

			assertListProvince(t, container, tcs)
		})
	})
}

type testCase struct {
	url               string
	expectedProvinces []string
	asLoggedIn        bool
}

func assertListProvince(t *testing.T, ctn *ittest.IntegrationTestContainer, tcs []testCase) {
	for _, tc := range tcs {
		t.Run("url="+tc.url, func(t *testing.T) {
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("%s", tc.url)
			if tc.asLoggedIn {
				ctx.Authorized(ctn.RedisTokenStore, "DRV_PATTAYA_ONLINE")
			}

			ctn.GinEngineRouter.HandleContext(ctx.GinCtx())

			ctx.AssertResponseCode(t, http.StatusOK)
			var res []regionapi.ProvinceResponse
			ctx.DecodeJSONResponse(&res)
			actualProvinces := make([]string, 0, len(res))
			for _, province := range res {
				actualProvinces = append(actualProvinces, province.Name)
			}
			assert.ElementsMatch(t, actualProvinces, tc.expectedProvinces)
		})
	}
}
