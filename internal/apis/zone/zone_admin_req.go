package zone

import (
	"github.com/twpayne/go-geom"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
)

type CreateZoneReq struct {
	Name        string      `json:"name" binding:"required"`
	DisplayName string      `json:"displayName" binding:"required"`
	Region      string      `json:"region" binding:"required"`
	Coordinates Coordinates `json:"coordinates" binding:"required"`
}

func (req CreateZoneReq) toModel() (service.CreateZoneRequest, error) {
	return service.CreateZoneRequest{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Region:      req.Region,
		Coordinates: service.ZoneCoordinates(req.Coordinates),
	}, nil
}

type UpdateZoneReq struct {
	DisplayName string      `json:"displayName" binding:"required"`
	Coordinates Coordinates `json:"coordinates" binding:"required"`
	Active      bool        `json:"active"`
}

func (req UpdateZoneReq) toModel() (service.UpdateZoneRequest, error) {
	return service.UpdateZoneRequest{
		DisplayName: req.DisplayName,
		Active:      req.Active,
		Coordinates: service.ZoneCoordinates(req.Coordinates),
	}, nil
}

type Coordinates [][][]geom.Coord
