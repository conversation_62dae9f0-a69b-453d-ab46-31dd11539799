package trip

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type ListDetail struct {
	TripID            string           `json:"tripId"`
	DriverID          string           `json:"driverId"`
	CreatedAt         time.Time        `json:"createdAt"`
	TotalActiveOrders int              `json:"totalActiveOrders"`
	Status            model.TripStatus `json:"status"`
	TotalPrice        types.Money      `json:"totalPrice"`
	ActiveOrders      []string         `json:"activeOrders"`
}

func NewAdminTripList(trips []model.Trip) []ListDetail {
	var res []ListDetail
	for _, v := range trips {
		res = append(res, ListDetail{
			TripID:            v.TripID,
			DriverID:          v.DriverID,
			CreatedAt:         v.CreatedAt,
			TotalActiveOrders: len(v.GetActiveOrderIDs()),
			Status:            v.Status,
			TotalPrice:        v.DriverWageSummary.TotalDriverWage,
			ActiveOrders:      v.GetActiveOrderIDs(),
		})
	}

	return res
}
