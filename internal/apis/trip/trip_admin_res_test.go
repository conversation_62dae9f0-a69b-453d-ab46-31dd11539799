package trip

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func Test_NewAdminTripList(t *testing.T) {
	trips := []model.Trip{
		{
			TripID:   "t1",
			DriverID: "d1",
			Orders: model.TripOrders{
				{
					OrderID: "o1",
					Status:  model.StatusDriveTo,
				},
			},
			DriverWageSummary: model.TripDriverWageSummary{
				TotalDriverWage: 10.00,
			},
			Status:    model.TripStatusCompleted,
			CreatedAt: time.Date(2022, 9, 21, 13, 0, 0, 0, time.UTC),
		},
	}

	res := NewAdminTripList(trips)

	r := res[0]

	require.Equal(t, 1, len(res))
	require.Equal(t, "t1", r.TripID)
	require.Equal(t, "d1", r.DriverID)
	require.Equal(t, types.Money(10), r.TotalPrice)
	require.Equal(t, model.TripStatusCompleted, r.Status)
	require.Equal(t, false, r.CreatedAt.IsZero())
	require.Equal(t, []string{"o1"}, r.ActiveOrders)
	require.Equal(t, 1, r.TotalActiveOrders)

}
