package trip

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestTripDetailRes(t *testing.T) {
	t.<PERSON>()

	genOrder1 := func() *model.Order {
		return &model.Order{
			Quote: model.Quote{
				QuoteID:     "quote-1",
				UserID:      "user-1",
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{
						ID:      "stop-1",
						Name:    "stop-name-1",
						Address: "address with subdistrict, Tambon Bang Kaew, blah blah",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 2.03319032,
							Lng: 2.03482783,
						},
						Memo: "memo",
						PickingItems: []model.Item{
							{
								Name:     "caution",
								Price:    0,
								Quantity: 0,
								Memo:     "อย่าจับลูกค้า",
							},
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
								Options: []model.Option{
									{
										Name:        "option-name-1",
										DisplayName: "display-name-1",
									},
								},
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
								Options: []model.Option{
									{
										Name:        "option-name-2",
										DisplayName: "display-name-2",
									},
								},
							},
						},
						DeliveryItems:  []model.Item{},
						CollectPayment: false,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{},
							ItemFee:     model.ItemFeeSummary{},
							Total:       0,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
							RestaurantDirection: model.RestaurantDirection{
								RestaurantID: "stop-1",
								Parking:      "stop-1 parking",
								Direction:    "stop-1 direction",
								Memo:         "stop-1 memo",
								UpdatedAt:    time.Now().UTC(),
							},
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
					{
						ID:      "stop-2",
						Name:    "stop-name-2",
						Address: "address-2",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 3.03319032,
							Lng: 3.03482783,
						},
						Memo:         "memo",
						PickingItems: []model.Item{},
						DeliveryItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						CollectPayment: true,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							ItemFee: model.ItemFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								Discounts:     []model.Discount{},
								ItemFee:       250.0,
								SubTotal:      250.0,
								Total:         250.0,
							},
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								BaseFee:       30,
								RoadFee:       20,
								AdditionalFee: map[string]float64{},
								Discounts: []model.Discount{
									{
										Type:     model.DiscountTypeSubsidize,
										Category: "cate-1",
										Code:     "code-1",
										Discount: 20,
									},
								},
								SubTotal:       50,
								Total:          30,
								Commission:     0,
								WithholdingTax: 0,
							},
							Total: 250 + 30,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
				},
				DistributeRegions: []model.RegionCode{"AYUTTHAYA"},
				Distance:          20.0,
				CreatedAt:         time.Now().UTC(),
				UpdatedAt:         time.Now().UTC(),
				Options: model.OrderOptions{
					RequireBox: false,
				},
			},
			OrderID: "order-1",
			Status:  model.StatusCompleted,
			Driver:  "driver-1",
			Region:  model.RegionCode("AYUTTHAYA"),
			History: map[string]time.Time{
				string(model.StatusDropOffDone): time.Now().UTC().Add(5 * time.Minute),
				string(model.StatusCompleted):   time.Now().UTC(),
			},
			ExpireAt:    time.Now(),
			RatingScore: 4,
			Comment:     "",
			CancelDetail: model.CancelDetail{
				Reason: "i love you",
			},
			DeliveringPhotoStatus: model.DeliveringPhotoStatusPassed,
		}
	}
	genOrder2 := func() *model.Order {
		return &model.Order{
			Quote: model.Quote{
				QuoteID:     "quote-2",
				UserID:      "user-2",
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{
						ID:      "stop-1",
						Name:    "stop-name-1",
						Address: "address with subdistrict, Tambon Bang Kaew, blah blah",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 2.03319032,
							Lng: 2.03482783,
						},
						Memo: "memo",
						PickingItems: []model.Item{
							{
								Name:     "caution",
								Price:    0,
								Quantity: 0,
								Memo:     "อย่าจับลูกค้า",
							},
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						DeliveryItems:  []model.Item{},
						CollectPayment: false,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{},
							ItemFee:     model.ItemFeeSummary{},
							Total:       0,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
							RestaurantDirection: model.RestaurantDirection{
								RestaurantID: "stop-1",
								Parking:      "stop-1 parking",
								Direction:    "stop-1 direction",
								Memo:         "stop-1 memo",
								UpdatedAt:    time.Now().UTC(),
							},
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
					{
						ID:      "stop-3",
						Name:    "stop-name-3",
						Address: "address-3",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 4.03319032,
							Lng: 4.03482783,
						},
						Memo:         "memo",
						PickingItems: []model.Item{},
						DeliveryItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						CollectPayment: true,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							ItemFee: model.ItemFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								Discounts:     []model.Discount{},
								ItemFee:       250.0,
								SubTotal:      250.0,
								Total:         250.0,
							},
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								BaseFee:       30,
								RoadFee:       20,
								AdditionalFee: map[string]float64{},
								Discounts: []model.Discount{
									{
										Type:     model.DiscountTypeSubsidize,
										Category: "cate-1",
										Code:     "code-1",
										Discount: 20,
									},
								},
								SubTotal:       50,
								Total:          30,
								Commission:     0,
								WithholdingTax: 0,
							},
							Total: 250 + 30,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
				},
				DistributeRegions: []model.RegionCode{"AYUTTHAYA"},
				Distance:          20.0,
				CreatedAt:         time.Now().UTC(),
				UpdatedAt:         time.Now().UTC(),
				Options: model.OrderOptions{
					RequireBox: false,
				},
			},
			OrderID: "order-2",
			Status:  model.StatusArrivedAt,
			Driver:  "driver-1",
			Region:  model.RegionCode("AYUTTHAYA"),
			History: map[string]time.Time{
				string(model.StatusDropOffDone): time.Now().UTC().Add(5 * time.Minute),
				string(model.StatusCompleted):   time.Now().UTC(),
			},
			ExpireAt:    time.Now(),
			RatingScore: 4,
			Comment:     "",
			CancelDetail: model.CancelDetail{
				Reason: "i love you2",
			},
			DeliveringPhotoStatus: model.DeliveringPhotoStatusPassed,
		}
	}
	genOrder3 := func() *model.Order {
		return &model.Order{
			Quote: model.Quote{
				QuoteID:     "quote-1",
				UserID:      "user-1",
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{
						ID:      "stop-4",
						Name:    "stop-name-4",
						Address: "address with subdistrict, Tambon Bang Kaew, blah blah",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 5.03319032,
							Lng: 5.03482783,
						},
						Memo: "memo",
						PickingItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						DeliveryItems:  []model.Item{},
						CollectPayment: false,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{},
							ItemFee:     model.ItemFeeSummary{},
							Total:       0,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
							RestaurantDirection: model.RestaurantDirection{
								RestaurantID: "stop-1",
								Parking:      "stop-1 parking",
								Direction:    "stop-1 direction",
								Memo:         "stop-1 memo",
								UpdatedAt:    time.Now().UTC(),
							},
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
					{
						ID:      "stop-2",
						Name:    "stop-name-2",
						Address: "address-2",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 3.03319032,
							Lng: 3.03482783,
						},
						Memo:         "memo",
						PickingItems: []model.Item{},
						DeliveryItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						CollectPayment: true,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							ItemFee: model.ItemFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								Discounts:     []model.Discount{},
								ItemFee:       250.0,
								SubTotal:      250.0,
								Total:         250.0,
							},
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								BaseFee:       30,
								RoadFee:       20,
								AdditionalFee: map[string]float64{},
								Discounts: []model.Discount{
									{
										Type:     model.DiscountTypeSubsidize,
										Category: "cate-1",
										Code:     "code-1",
										Discount: 20,
									},
								},
								SubTotal:       50,
								Total:          30,
								Commission:     0,
								WithholdingTax: 0,
							},
							Total: 250 + 30,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
				},
				DistributeRegions: []model.RegionCode{"AYUTTHAYA"},
				Distance:          20.0,
				CreatedAt:         time.Now().UTC(),
				UpdatedAt:         time.Now().UTC(),
				Options: model.OrderOptions{
					RequireBox: false,
				},
			},
			OrderID: "order-3",
			Status:  model.StatusCompleted,
			Driver:  "driver-1",
			Region:  model.RegionCode("AYUTTHAYA"),
			History: map[string]time.Time{
				string(model.StatusDropOffDone): time.Now().UTC().Add(5 * time.Minute),
				string(model.StatusCompleted):   time.Now().UTC(),
			},
			ExpireAt:    time.Now(),
			RatingScore: 4,
			Comment:     "",
			CancelDetail: model.CancelDetail{
				Reason: "i love you3",
			},
			DeliveringPhotoStatus: model.DeliveringPhotoStatusPassed,
		}
	}
	genOrder4 := func() *model.Order {
		return &model.Order{
			Quote: model.Quote{
				QuoteID:     "quote-1",
				UserID:      "user-1",
				ServiceType: model.ServiceBike,
				PayAtStop:   1,
				Routes: []model.Stop{
					{
						ID:      "stop-1",
						Name:    "stop-name-1",
						Address: "address with subdistrict, Tambon Bang Kaew, blah blah",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 2.03319032,
							Lng: 2.03482783,
						},
						Memo:           "memo",
						CollectPayment: false,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{},
							ItemFee:     model.ItemFeeSummary{},
							Total:       0,
						},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
					{
						ID:      "stop-2",
						Name:    "stop-name-2",
						Address: "address-2",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 3.03319032,
							Lng: 3.03482783,
						},
						Memo:         "memo",
						PickingItems: []model.Item{},
						DeliveryItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						CollectPayment: true,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								BaseFee:       30,
								RoadFee:       20,
								AdditionalFee: map[string]float64{},
								Discounts: []model.Discount{
									{
										Type:     model.DiscountTypeSubsidize,
										Category: "cate-1",
										Code:     "code-1",
										Discount: 20,
									},
								},
								SubTotal:       50,
								Total:          30,
								Commission:     4.5,
								CommissionRate: 0.15,
							},
							Total: 30,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
				},
				DistributeRegions: []model.RegionCode{"AYUTTHAYA"},
				Distance:          20.0,
				CreatedAt:         time.Now().UTC(),
				UpdatedAt:         time.Now().UTC(),
				Options: model.OrderOptions{
					RequireBox: false,
				},
			},
			OrderID: "order-1",
			Status:  model.StatusCompleted,
			Driver:  "driver-1",
			Region:  model.RegionCode("AYUTTHAYA"),
			History: map[string]time.Time{
				string(model.StatusDropOffDone): time.Now().UTC().Add(5 * time.Minute),
				string(model.StatusCompleted):   time.Now().UTC(),
			},
			ExpireAt:    time.Now(),
			RatingScore: 4,
			Comment:     "",
			CancelDetail: model.CancelDetail{
				Reason: "i love you",
			},
			DeliveringPhotoStatus: model.DeliveringPhotoStatusPassed,
		}
	}

	genRoundTrip := func() *model.Order {
		return &model.Order{
			Quote: model.Quote{
				QuoteID:     "quote-1",
				UserID:      "user-1",
				ServiceType: model.ServiceMessenger,
				Routes: []model.Stop{
					{
						ID:      "stop-1",
						Name:    "stop-name-1",
						Address: "address with subdistrict, Tambon Bang Kaew, blah blah",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 2.03319032,
							Lng: 2.03482783,
						},
						Memo: "memo",
						PickingItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						DeliveryItems:  []model.Item{},
						CollectPayment: false,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{},
							ItemFee:     model.ItemFeeSummary{},
							Total:       0,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
							RestaurantDirection: model.RestaurantDirection{
								RestaurantID: "stop-1",
								Parking:      "stop-1 parking",
								Direction:    "stop-1 direction",
								Memo:         "stop-1 memo",
								UpdatedAt:    time.Now().UTC(),
							},
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
					{
						ID:      "stop-2",
						Name:    "stop-name-2",
						Address: "address-2",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 3.03319032,
							Lng: 3.03482783,
						},
						Memo:         "memo",
						PickingItems: []model.Item{},
						DeliveryItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						CollectPayment: true,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							ItemFee: model.ItemFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								Discounts:     []model.Discount{},
								ItemFee:       250.0,
								SubTotal:      250.0,
								Total:         250.0,
							},
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								BaseFee:       30,
								RoadFee:       20,
								AdditionalFee: map[string]float64{},
								Discounts: []model.Discount{
									{
										Type:     model.DiscountTypeSubsidize,
										Category: "cate-1",
										Code:     "code-1",
										Discount: 20,
									},
								},
								SubTotal:       50,
								Total:          30,
								Commission:     0,
								WithholdingTax: 0,
							},
							Total: 250 + 30,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
					{
						ID:      "stop-1",
						Name:    "stop-name-1",
						Address: "address with subdistrict, Tambon Bang Kaew, blah blah",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 2.03319032,
							Lng: 2.03482783,
						},
						Memo: "memo",
						PickingItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						DeliveryItems:  []model.Item{},
						CollectPayment: false,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{},
							ItemFee:     model.ItemFeeSummary{},
							Total:       0,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
							RestaurantDirection: model.RestaurantDirection{
								RestaurantID: "stop-1",
								Parking:      "stop-1 parking",
								Direction:    "stop-1 direction",
								Memo:         "stop-1 memo",
								UpdatedAt:    time.Now().UTC(),
							},
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
				},
				DistributeRegions: []model.RegionCode{"AYUTTHAYA"},
				Distance:          20.0,
				CreatedAt:         time.Now().UTC(),
				UpdatedAt:         time.Now().UTC(),
				Options: model.OrderOptions{
					RequireBox: false,
					RoundTrip:  true,
				},
			},
			OrderID: "order-1",
			Status:  model.StatusCompleted,
			Driver:  "driver-1",
			Region:  model.RegionCode("AYUTTHAYA"),
			History: map[string]time.Time{
				string(model.StatusDropOffDone): time.Now().UTC().Add(5 * time.Minute),
				string(model.StatusCompleted):   time.Now().UTC(),
			},
			ExpireAt:              time.Now(),
			RatingScore:           4,
			Comment:               "",
			CancelDetail:          model.CancelDetail{},
			DeliveringPhotoStatus: model.DeliveringPhotoStatusPassed,
		}
	}
	genCompletedSingleTrip := func(order *model.Order) *model.Trip {
		return &model.Trip{
			TripID: "trip-1",
			Routes: []model.TripRoute{
				{
					ID:                    order.Routes[0].ID,
					Location:              order.Routes[0].Location,
					Distance:              order.Routes[0].Distance,
					EstimatedDeliveryTime: order.Routes[0].EstimatedDeliveryTime,
					Action:                model.TripActionPickUp,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order.OrderID,
							StopID:  0,
							Done:    true,
						},
					},
				},
				{
					ID:                    order.Routes[1].ID,
					Location:              order.Routes[1].Location,
					Distance:              order.Routes[1].Distance,
					EstimatedDeliveryTime: order.Routes[1].EstimatedDeliveryTime,
					Action:                model.TripActionDropOff,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order.OrderID,
							StopID:  1,
							Done:    true,
						},
					},
				},
			},
			Orders: []model.TripOrder{
				{
					OrderID:     order.OrderID,
					ServiceType: model.ServiceFood,
					Status:      model.StatusCompleted,
					HeadTo:      1,
				},
			},
			HeadTo: 1,
			Status: model.TripStatusCompleted,
			DriverWageSummary: model.TripDriverWageSummary{
				Outstanding:    5.0,
				TransferAmount: 10.0,
			},
		}
	}
	genArrivedAtMultipleDropOffTrip := func(order1 *model.Order, order2 *model.Order) *model.Trip {
		return &model.Trip{
			TripID: "trip-2",
			Routes: []model.TripRoute{
				{
					ID:       order1.Routes[0].ID,
					Location: order1.Routes[0].Location,
					Distance: order1.Routes[0].Distance,
					Action:   model.TripActionPickUp,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order1.OrderID,
							StopID:  0,
							Done:    true,
						},
						{
							OrderID: order2.OrderID,
							StopID:  0,
							Done:    false,
						},
					},
				},
				{
					ID:       order1.Routes[1].ID,
					Location: order1.Routes[1].Location,
					Distance: order1.Routes[1].Distance,
					Action:   model.TripActionDropOff,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order1.OrderID,
							StopID:  1,
							Done:    true,
						},
					},
				},
				{
					ID:       order2.Routes[1].ID,
					Location: order2.Routes[1].Location,
					Distance: order2.Routes[1].Distance,
					Action:   model.TripActionDropOff,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order2.OrderID,
							StopID:  1,
							Done:    true,
						},
					},
				},
			},
			Orders: []model.TripOrder{
				{
					OrderID:     order1.OrderID,
					ServiceType: model.ServiceFood,
					Status:      model.StatusArrivedAt,
					HeadTo:      0,
				},
				{
					OrderID:     order2.OrderID,
					ServiceType: model.ServiceFood,
					Status:      model.StatusArrivedAt,
					HeadTo:      0,
				},
			},
			HeadTo: 0,
			Status: model.TripStatusArrivedAt,
		}
	}
	genArrivedAtMultiplePickUpTrip := func(order1 *model.Order, order2 *model.Order) *model.Trip {
		return &model.Trip{
			TripID: "trip-3",
			Routes: []model.TripRoute{
				{
					ID:       order1.Routes[0].ID,
					Location: order1.Routes[0].Location,
					Distance: order1.Routes[0].Distance,
					Action:   model.TripActionPickUp,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order1.OrderID,
							StopID:  0,
							Done:    false,
						},
					},
				},
				{
					ID:       order2.Routes[0].ID,
					Location: order2.Routes[0].Location,
					Distance: order2.Routes[0].Distance,
					Action:   model.TripActionPickUp,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order2.OrderID,
							StopID:  0,
							Done:    false,
						},
					},
				},
				{
					ID:       order1.Routes[1].ID,
					Location: order1.Routes[1].Location,
					Distance: order1.Routes[1].Distance,
					Action:   model.TripActionDropOff,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order1.OrderID,
							StopID:  1,
							Done:    false,
						},
					},
				},
				{
					ID:       order1.Routes[1].ID,
					Location: order1.Routes[1].Location,
					Distance: order1.Routes[1].Distance,
					Action:   model.TripActionDropOff,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order2.OrderID,
							StopID:  1,
							Done:    false,
						},
					},
				},
			},
			Orders: []model.TripOrder{
				{
					OrderID:     order1.OrderID,
					ServiceType: model.ServiceFood,
					Status:      model.StatusArrivedAt,
					HeadTo:      0,
				},
				{
					OrderID:     order2.OrderID,
					ServiceType: model.ServiceFood,
					Status:      model.StatusArrivedAt,
					HeadTo:      0,
				},
			},
			HeadTo: 0,
			Status: model.TripStatusArrivedAt,
		}
	}

	genOrderDetailsMap := func(orders []model.Order) map[string]order.OrderDetailRes {
		orderDetailsMap := make(map[string]order.OrderDetailRes)
		for _, o := range orders {
			odr := order.NewOrderDetailRes(o, nil, nil, nil, order.OrderAPIConfig{EnableEarning: true, AtomicOrderDBConfig: &order.AtomicOrderDBConfig{Config: order.OrderDBConfig{}}}, order.ContingencyConfig{}, model.NegativeBalanceGroupConfig{}, model.TierNegativeBalanceConfig{}, false)
			orderDetailsMap[odr.OrderID] = odr
		}
		return orderDetailsMap
	}

	t.Run("create completed single trip", func(tt *testing.T) {
		ord := genOrder1()
		trip := genCompletedSingleTrip(ord)
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*ord}), order.OrderAPIConfig{}, false, types.NewBool(false))

		require.Equal(tt, "trip-1", actual.TripID)
		require.Len(tt, actual.Routes, 2)
		require.Len(tt, actual.Orders, 1)
		require.Len(tt, actual.Orders[0].Items, 3)
		require.Equal(tt, types.Money(10.0), actual.TransferDetail.TransferAmount)
		require.Equal(tt, types.Money(5.0), actual.TransferDetail.Outstanding)

		stop := actual.Routes[0]
		info := stop.StopOrders[0].Info.StopInfo.(*model.StopInfoFood)
		require.Equal(tt, "stop-1", stop.ID)
		require.Equal(tt, "stop-name-1", stop.Name)
		require.Equal(tt, "address with subdistrict, Tambon Bang Kaew, blah blah", stop.Address)
		require.Equal(tt, []string{"*********"}, stop.Phones)
		require.Equal(tt, model.Location{Lat: 2.03319032, Lng: 2.03482783}, stop.Location)
		require.Equal(tt, 250.0, stop.StopOrders[0].ItemsPrice)
		require.Equal(tt, model.DurationSecond(15), info.EstimatedCookingTime)
		require.Equal(tt, model.PriceSchemeRMS, info.PriceScheme)
		require.Equal(tt, types.Distance(40), stop.Distance)
		require.Equal(tt, model.DurationSecond(20), stop.EstimatedDeliveryTime)
		require.Equal(tt, "stop-1", info.RestaurantDirection.RestaurantID)
		require.Equal(tt, "stop-1 parking", info.RestaurantDirection.Parking)
		require.Equal(tt, "stop-1 direction", info.RestaurantDirection.Direction)
		require.Equal(tt, "stop-1 memo", info.RestaurantDirection.Memo)
		require.EqualValues(tt, actual.MoneySummary, stop.MoneySummary)

		stop = actual.Routes[1]
		info = stop.StopOrders[0].Info.StopInfo.(*model.StopInfoFood)
		require.Equal(tt, "stop-2", stop.ID)
		require.Equal(tt, "stop-name-2", stop.Name)
		require.Equal(tt, "address-2", stop.Address)
		require.Equal(tt, []string{"*********"}, stop.Phones)
		require.Equal(tt, model.Location{Lat: 3.03319032, Lng: 3.03482783}, stop.Location)

		require.Equal(tt, 30.0, actual.Orders[0].PriceSummary.DeliveryFee.BaseFee)
		require.Equal(tt, 20.0, actual.Orders[0].PriceSummary.DeliveryFee.RoadFee)
		require.Len(tt, actual.Orders[0].PriceSummary.DeliveryFee.Discounts, 1)
		require.Equal(tt, 20.0, actual.Orders[0].PriceSummary.DeliveryFee.Discounts[0].Discount)
		require.Equal(tt, 50.0, actual.Orders[0].PriceSummary.DeliveryFee.SubTotal)
		require.Equal(tt, 30.0, actual.Orders[0].PriceSummary.DeliveryFee.Total)
		require.Equal(tt, 250.0, actual.Orders[0].PriceSummary.OrderPrice)
		require.Equal(tt, 280.0, actual.Orders[0].PriceSummary.Total)

		require.Equal(tt, model.DurationSecond(15), info.EstimatedCookingTime)
		require.Equal(tt, model.PriceSchemeRMS, info.PriceScheme)
		require.Equal(tt, types.Distance(40), stop.Distance)
		require.Equal(tt, model.DurationSecond(20), stop.EstimatedDeliveryTime)
		require.EqualValues(tt, actual.MoneySummary, stop.MoneySummary)
	})

	t.Run("create completed single trip - with revenue agent model", func(tt *testing.T) {
		ord := genOrder4()
		trip := genCompletedSingleTrip(ord)
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*ord}), order.OrderAPIConfig{}, false, types.NewBool(false))

		require.Equal(tt, "trip-1", actual.TripID)
		require.Len(tt, actual.Routes, 2)
		require.Len(tt, actual.Orders, 1)
		require.Equal(tt, types.Money(10.0), actual.TransferDetail.TransferAmount)
		require.Equal(tt, types.Money(5.0), actual.TransferDetail.Outstanding)

		stop := actual.Routes[0]
		require.Equal(tt, "stop-1", stop.ID)
		require.Equal(tt, "stop-name-1", stop.Name)
		require.Equal(tt, "address with subdistrict, Tambon Bang Kaew, blah blah", stop.Address)
		require.Equal(tt, []string{"*********"}, stop.Phones)
		require.Equal(tt, model.Location{Lat: 2.03319032, Lng: 2.03482783}, stop.Location)
		require.Equal(tt, types.Distance(40), stop.Distance)
		require.Equal(tt, model.DurationSecond(20), stop.EstimatedDeliveryTime)
		require.EqualValues(tt, actual.MoneySummary, stop.MoneySummary)

		stop = actual.Routes[1]
		require.Equal(tt, "stop-2", stop.ID)
		require.Equal(tt, "stop-name-2", stop.Name)
		require.Equal(tt, "address-2", stop.Address)
		require.Equal(tt, []string{"*********"}, stop.Phones)
		require.Equal(tt, model.Location{Lat: 3.03319032, Lng: 3.03482783}, stop.Location)

		require.Equal(tt, 30.0, actual.Orders[0].PriceSummary.DeliveryFee.BaseFee)
		require.Equal(tt, 20.0, actual.Orders[0].PriceSummary.DeliveryFee.RoadFee)
		require.Len(tt, actual.Orders[0].PriceSummary.DeliveryFee.Discounts, 1)
		require.Equal(tt, 20.0, actual.Orders[0].PriceSummary.DeliveryFee.Discounts[0].Discount)
		require.Equal(tt, 50.0, actual.Orders[0].PriceSummary.DeliveryFee.SubTotal)
		require.Equal(tt, 30.0, actual.Orders[0].PriceSummary.DeliveryFee.Total)
		require.Equal(tt, 15, actual.Orders[0].CommissionRate)
		require.Equal(tt, types.Money(4.5), actual.Orders[0].Commission)
		require.Equal(tt, 30.0, actual.Orders[0].PriceSummary.Total)

		require.Equal(tt, types.Distance(40), stop.Distance)
		require.Equal(tt, model.DurationSecond(20), stop.EstimatedDeliveryTime)
		require.EqualValues(tt, actual.MoneySummary, stop.MoneySummary)
	})

	t.Run("create arrived at multiple drop off trip (should hide future DROP OFF stop)", func(tt *testing.T) {
		order1, order2 := genOrder1(), genOrder2()
		order1.Status = model.StatusDriverArrivedRestaurant
		order2.Status = model.StatusDriverArrivedRestaurant
		trip := genArrivedAtMultipleDropOffTrip(order1, order2)
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*order1, *order2}), order.OrderAPIConfig{}, false, types.NewBool(false))

		require.Equal(tt, "trip-2", actual.TripID)
		require.Len(tt, actual.Routes, 3)

		require.Equal(tt, "stop-1", actual.Routes[0].ID)
		require.Equal(tt, "stop-2", actual.Routes[1].ID)
		require.Equal(tt, "stop-3", actual.Routes[2].ID)

		require.Equal(tt, false, actual.Routes[0].Hidden)
		require.Equal(tt, false, actual.Routes[1].Hidden)
		require.Equal(tt, true, actual.Routes[2].Hidden)

		require.Len(tt, actual.Orders[0].Items, 3)
		require.Len(tt, actual.Orders[1].Items, 3)

		require.Equal(tt, model.Location{
			Lat: 2.03319032,
			Lng: 2.03482783,
		}, actual.Routes[0].Location)
		require.Equal(tt, model.Location{
			Lat: 3.03319032,
			Lng: 3.03482783,
		}, actual.Routes[1].Location)
		require.Equal(tt, model.Location{
			Lat: 4.03319032,
			Lng: 4.03482783,
		}, actual.Routes[2].Location)
	})

	t.Run("should have correct trip route money summary multiple drop off trip ", func(tt *testing.T) {
		order1, order2 := genOrder1(), genOrder2()
		order1.Status = model.StatusDriverArrivedRestaurant
		order2.Status = model.StatusDriverArrivedRestaurant
		trip := genArrivedAtMultipleDropOffTrip(order1, order2)
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*order1, *order2}), order.OrderAPIConfig{}, false, types.NewBool(false))
		require.EqualValues(tt, actual.MoneySummary, actual.Routes[0].MoneySummary)
	})

	t.Run("should have correct trip route money summary multiple drop off trip ", func(tt *testing.T) {
		order1, order2 := genOrder1(), genOrder3()
		trip := genArrivedAtMultiplePickUpTrip(order1, order2)
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*order1, *order2}), order.OrderAPIConfig{}, false, types.NewBool(false))
		expect1 := NewActiveMoneySummaryRes(genOrderDetailsMap([]model.Order{*order1}))
		expect2 := NewActiveMoneySummaryRes(genOrderDetailsMap([]model.Order{*order2}))
		require.EqualValues(tt, expect1, actual.Routes[0].MoneySummary)
		require.EqualValues(tt, expect2, actual.Routes[1].MoneySummary)
		require.EqualValues(tt, expect1, actual.Routes[2].MoneySummary)
		require.EqualValues(tt, expect2, actual.Routes[3].MoneySummary)
	})

	t.Run("create arrived at multiple drop off trip (should hide future DROP OFF stop) - with RemoveZeroQuantityPickingItems = true", func(tt *testing.T) {
		order1, order2 := genOrder1(), genOrder2()
		order1.Status = model.StatusDriverArrivedRestaurant
		order2.Status = model.StatusDriverArrivedRestaurant
		trip := genArrivedAtMultipleDropOffTrip(order1, order2)
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*order1, *order2}), order.OrderAPIConfig{RemoveZeroQuantityPickingItems: true}, false, types.NewBool(false))

		require.Equal(tt, "trip-2", actual.TripID)
		require.Len(tt, actual.Routes, 3)

		require.Equal(tt, "stop-1", actual.Routes[0].ID)
		require.Equal(tt, "stop-2", actual.Routes[1].ID)
		require.Equal(tt, "stop-3", actual.Routes[2].ID)

		require.Equal(tt, false, actual.Routes[0].Hidden)
		require.Equal(tt, false, actual.Routes[1].Hidden)
		require.Equal(tt, true, actual.Routes[2].Hidden)

		require.Len(tt, actual.Orders[0].Items, 2)
		require.Len(tt, actual.Orders[1].Items, 2)

		require.Equal(tt, "option-name-1", actual.Orders[0].Items[0].Options[0].Name)
		require.Equal(tt, "display-name-1", actual.Orders[0].Items[0].Options[0].DisplayName)
		require.Equal(tt, "option-name-2", actual.Orders[0].Items[1].Options[0].Name)
		require.Equal(tt, "display-name-2", actual.Orders[0].Items[1].Options[0].DisplayName)

		require.Equal(tt, model.Location{
			Lat: 2.03319032,
			Lng: 2.03482783,
		}, actual.Routes[0].Location)
		require.Equal(tt, model.Location{
			Lat: 3.03319032,
			Lng: 3.03482783,
		}, actual.Routes[1].Location)
		require.Equal(tt, model.Location{
			Lat: 4.03319032,
			Lng: 4.03482783,
		}, actual.Routes[2].Location)
	})

	t.Run("create arrived at multiple drop off trip (should hide past DROP OFF stop)", func(tt *testing.T) {
		order1, order2 := genOrder1(), genOrder2()
		trip := genArrivedAtMultipleDropOffTrip(order1, order2)
		trip.HeadTo = 2
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*order1, *order2}), order.OrderAPIConfig{}, false, types.NewBool(false))

		require.Equal(tt, false, actual.Routes[0].Hidden)
		require.Equal(tt, true, actual.Routes[1].Hidden)
		require.Equal(tt, false, actual.Routes[2].Hidden)

		require.Equal(tt, []string{}, actual.Routes[1].Phones)
		require.Equal(tt, "", actual.Routes[1].Address)
		require.Equal(tt, "", actual.Routes[1].Memo)
		require.Equal(tt, "", actual.Routes[1].Name)
		require.Equal(tt, types.Distance(0), actual.Routes[1].Distance)
		require.Equal(tt, model.Location{
			Lat: 3.03319032,
			Lng: 3.03482783,
		}, actual.Routes[1].Location)
		require.Equal(tt, []string{"*********"}, actual.Routes[2].Phones)
	})

	t.Run("create arrived at multiple pick up trip (should hide future PICK UP stop)", func(tt *testing.T) {
		order1, order2 := genOrder1(), genOrder3()
		trip := genArrivedAtMultiplePickUpTrip(order1, order2)
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*order1, *order2}), order.OrderAPIConfig{}, false, types.NewBool(false))

		require.Equal(tt, "trip-3", actual.TripID)
		require.Len(tt, actual.Routes, 4)

		require.Equal(tt, "stop-1", actual.Routes[0].ID)
		require.Equal(tt, "stop-4", actual.Routes[1].ID)
		require.Equal(tt, "stop-2", actual.Routes[2].ID)

		require.Equal(tt, false, actual.Routes[0].Hidden)
		require.Equal(tt, false, actual.Routes[1].Hidden)
		require.Equal(tt, true, actual.Routes[2].Hidden)

		require.Equal(tt, model.Location{
			Lat: 2.03319032,
			Lng: 2.03482783,
		}, actual.Routes[0].Location)
		require.Equal(tt, model.Location{
			Lat: 5.03319032,
			Lng: 5.03482783,
		}, actual.Routes[1].Location)
		require.Equal(tt, model.Location{
			Lat: 3.03319032,
			Lng: 3.03482783,
		}, actual.Routes[2].Location)
	})

	t.Run("trip cancelled", func(tt *testing.T) {
		order1, order2 := genOrder1(), genOrder3()
		trip := genArrivedAtMultiplePickUpTrip(order1, order2)

		trip.Status = model.TripStatusCanceled
		order1.Status, trip.Orders[0].Status = model.StatusCanceled, model.StatusCanceled
		order2.Status, trip.Orders[1].Status = model.StatusCanceled, model.StatusCanceled

		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*order1, *order2}), order.OrderAPIConfig{}, false, types.NewBool(false))

		require.Len(tt, actual.InactiveOrders, 2)
		require.Equal(tt, order1.OrderID, actual.InactiveOrders[0].OrderID)
		require.Equal(tt, order1.ServiceType, actual.InactiveOrders[0].ServiceType)
		require.Equal(tt, order1.Status, actual.InactiveOrders[0].Status)
		require.Equal(tt, order1.Options, actual.InactiveOrders[0].Options)
		require.Equal(tt, order1.CancelDetail, actual.InactiveOrders[0].CancelDetail)
		require.Equal(tt, order1.Routes[0].Name, actual.InactiveOrders[0].PickUpName)
		require.Equal(tt, order1.Routes[0].Address, actual.InactiveOrders[0].PickUpAddress)
		require.Equal(tt, order1.Routes[0].PickingItems, actual.InactiveOrders[0].Items)
		require.Equal(tt, order1.Routes[1].Name, actual.InactiveOrders[0].DropOffName)
		require.Equal(tt, order1.Routes[1].Address, actual.InactiveOrders[0].DropOffAddress)

		require.Equal(tt, order2.OrderID, actual.InactiveOrders[1].OrderID)
		require.Equal(tt, order2.ServiceType, actual.InactiveOrders[1].ServiceType)
		require.Equal(tt, order2.Status, actual.InactiveOrders[1].Status)
		require.Equal(tt, order2.Options, actual.InactiveOrders[1].Options)
		require.Equal(tt, order2.CancelDetail, actual.InactiveOrders[1].CancelDetail)
		require.Equal(tt, order2.Routes[0].Name, actual.InactiveOrders[1].PickUpName)
		require.Equal(tt, order2.Routes[0].Address, actual.InactiveOrders[1].PickUpAddress)
		require.Equal(tt, order2.Routes[0].PickingItems, actual.InactiveOrders[1].Items)
		require.Equal(tt, order2.Routes[1].Name, actual.InactiveOrders[1].DropOffName)
		require.Equal(tt, order2.Routes[1].Address, actual.InactiveOrders[1].DropOffAddress)
	})

	t.Run("round trip", func(tt *testing.T) {
		o := genRoundTrip()
		trip, err := model.NewEndedTrip(o.OrderID, *o)
		require.NoError(tt, err)

		actual := NewTripDetailRes(trip, genOrderDetailsMap([]model.Order{*o}), order.OrderAPIConfig{}, false, types.NewBool(false))

		require.Len(tt, actual.Orders, 1)
		require.Len(tt, actual.Routes, 3)
		for i := range o.Routes {
			require.Equal(tt, o.Routes[i].Name, actual.Routes[i].Name)
			require.Equal(tt, o.Routes[i].Address, actual.Routes[i].Address)
			require.Equal(tt, o.Routes[i].Location, actual.Routes[i].Location)
			require.Equal(tt, o.Routes[i].Phones, actual.Routes[i].Phones)
			require.EqualValues(tt, actual.MoneySummary, actual.Routes[i].MoneySummary)
		}
	})

	t.Run("check rain status in completed single trip - raining", func(tt *testing.T) {
		ord := genOrder1()
		ord.IsRequireDeliveringPhotoURL = true
		ord.Routes[1].Pauses = model.PauseSet{
			model.PauseDeliveringPhoto: false,
		}
		ord.Routes[1].IsRain = true
		ord.HeadTo = 1

		trip := genCompletedSingleTrip(ord)
		atomicDbConfig := order.AtomicOrderDBConfig{
			Config: order.OrderDBConfig{
				DisableVOSPhoto: false,
			},
		}
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*ord}), order.OrderAPIConfig{
			AtomicOrderDBConfig: &atomicDbConfig,
		}, false, types.NewBool(false))

		require.Equal(tt, "trip-1", actual.TripID)
		require.Equal(tt, false, actual.Orders[0].IsRequireDeliveringPhotoURL)
		require.Equal(tt, true, actual.Orders[0].IsRain)
	})

	t.Run("check rain status in completed single trip - not raining", func(tt *testing.T) {
		ord := genOrder1()
		ord.IsRequireDeliveringPhotoURL = true
		ord.Routes[1].Pauses = model.PauseSet{
			model.PauseDeliveringPhoto: true,
		}
		ord.Routes[1].IsRain = false
		ord.HeadTo = 1

		trip := genCompletedSingleTrip(ord)
		atomicDbConfig := order.AtomicOrderDBConfig{
			Config: order.OrderDBConfig{
				DisableVOSPhoto: false,
			},
		}
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*ord}), order.OrderAPIConfig{
			AtomicOrderDBConfig: &atomicDbConfig,
		}, false, types.NewBool(false))

		require.Equal(tt, "trip-1", actual.TripID)
		require.Equal(tt, true, actual.Orders[0].IsRequireDeliveringPhotoURL)
		require.Equal(tt, false, actual.Orders[0].IsRain)
	})

	t.Run("check rain status in completed single trip - raining but not require PoD", func(tt *testing.T) {
		ord := genOrder1()
		ord.IsRequireDeliveringPhotoURL = false
		ord.Routes[1].Pauses = model.PauseSet{
			model.PauseDeliveringPhoto: false,
		}
		ord.Routes[1].IsRain = true
		ord.HeadTo = 1

		trip := genCompletedSingleTrip(ord)
		atomicDbConfig := order.AtomicOrderDBConfig{
			Config: order.OrderDBConfig{
				DisableVOSPhoto: false,
			},
		}
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*ord}), order.OrderAPIConfig{
			AtomicOrderDBConfig: &atomicDbConfig,
		}, false, types.NewBool(false))

		require.Equal(tt, "trip-1", actual.TripID)
		require.Equal(tt, false, actual.Orders[0].IsRequireDeliveringPhotoURL)
		require.Equal(tt, false, actual.Orders[0].IsRain)
	})

	t.Run("create completed single trip with tip", func(tt *testing.T) {
		ord := genOrder1()
		ord.TipAmount = 30
		ord.Tips = model.TipRecords{
			{
				ID:          "ONGOING_TIP_ID",
				Amount:      10,
				OrderStatus: "DRIVER_TO_DESTINATION",
				CreatedAt:   timeutil.BangkokNow(),
			},
			{
				ID:          "COMPLETED_TIP_ID",
				Amount:      20,
				OrderStatus: "COMPLETED",
				CreatedAt:   timeutil.BangkokNow(),
			},
		}

		trip := genCompletedSingleTrip(ord)
		actual := NewTripDetailRes(*trip, genOrderDetailsMap([]model.Order{*ord}), order.OrderAPIConfig{}, false, types.NewBool(false))

		require.Equal(tt, types.Money(30), actual.Earning.TotalTipAmount)
		require.Len(tt, actual.Orders, 1)
		require.Equal(tt, types.Money(20), actual.Orders[0].TipAmount)
		require.Equal(tt, types.Money(10), actual.Orders[0].OnGoingTipAmount)
	})

}

func TestTripHistoryDetailRes(t *testing.T) {
	t.Parallel()

	genOrder1 := func() *model.Order {
		return &model.Order{
			Quote: model.Quote{
				QuoteID:     "quote-1",
				UserID:      "user-1",
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{
						ID:      "stop-1",
						Name:    "stop-name-1",
						Address: "address with subdistrict, Tambon Bang Kaew, blah blah",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 2.03319032,
							Lng: 2.03482783,
						},
						Memo: "memo",
						PickingItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						DeliveryItems:  []model.Item{},
						CollectPayment: false,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{},
							ItemFee:     model.ItemFeeSummary{},
							Total:       0,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
							RestaurantDirection: model.RestaurantDirection{
								RestaurantID: "stop-1",
								Parking:      "stop-1 parking",
								Direction:    "stop-1 direction",
								Memo:         "stop-1 memo",
								UpdatedAt:    time.Now().UTC(),
							},
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
					{
						ID:      "stop-2",
						Name:    "stop-name-2",
						Address: "address-2",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 3.03319032,
							Lng: 3.03482783,
						},
						Memo:         "memo",
						PickingItems: []model.Item{},
						DeliveryItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						CollectPayment: true,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							ItemFee: model.ItemFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								Discounts:     []model.Discount{},
								ItemFee:       250.0,
								SubTotal:      250.0,
								Total:         250.0,
							},
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								BaseFee:       30,
								RoadFee:       20,
								AdditionalFee: map[string]float64{},
								Discounts: []model.Discount{
									{
										Type:     model.DiscountTypeSubsidize,
										Category: "cate-1",
										Code:     "code-1",
										Discount: 20,
									},
								},
								SubTotal:       50,
								Total:          30,
								Commission:     0,
								WithholdingTax: 0,
							},
							Total: 250 + 30,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
				},
				DistributeRegions: []model.RegionCode{"AYUTTHAYA"},
				Distance:          20.0,
				CreatedAt:         time.Now().UTC(),
				UpdatedAt:         time.Now().UTC(),
				Options: model.OrderOptions{
					RequireBox: false,
				},
			},
			OrderID: "order-1",
			Status:  model.StatusCompleted,
			Driver:  "driver-1",
			Region:  model.RegionCode("AYUTTHAYA"),
			History: map[string]time.Time{
				string(model.StatusDropOffDone): time.Now().UTC().Add(5 * time.Minute),
				string(model.StatusCompleted):   time.Now().UTC(),
			},
			ExpireAt:              time.Now(),
			RatingScore:           4,
			Comment:               "",
			CancelDetail:          model.CancelDetail{},
			DeliveringPhotoStatus: model.DeliveringPhotoStatusPassed,
		}
	}
	genOrder3 := func() *model.Order {
		return &model.Order{
			Quote: model.Quote{
				QuoteID:     "quote-1",
				UserID:      "user-1",
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{
						ID:      "stop-4",
						Name:    "stop-name-4",
						Address: "address with subdistrict, Tambon Bang Kaew, blah blah",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 5.03319032,
							Lng: 5.03482783,
						},
						Memo: "memo",
						PickingItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						DeliveryItems:  []model.Item{},
						CollectPayment: false,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{},
							ItemFee:     model.ItemFeeSummary{},
							Total:       0,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
							RestaurantDirection: model.RestaurantDirection{
								RestaurantID: "stop-1",
								Parking:      "stop-1 parking",
								Direction:    "stop-1 direction",
								Memo:         "stop-1 memo",
								UpdatedAt:    time.Now().UTC(),
							},
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
					{
						ID:      "stop-2",
						Name:    "stop-name-2",
						Address: "address-2",
						Phones:  []string{"*********"},
						Location: model.Location{
							Lat: 3.03319032,
							Lng: 3.03482783,
						},
						Memo:         "memo",
						PickingItems: []model.Item{},
						DeliveryItems: []model.Item{
							{
								Name:     "item-1",
								Price:    100.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
							{
								Name:     "item-2",
								Price:    150.0,
								Quantity: 2,
								Memo:     "memo-1",
							},
						},
						CollectPayment: true,
						ItemsPrice:     250.0,
						PriceSummary: model.PriceSummary{
							ItemFee: model.ItemFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								Discounts:     []model.Discount{},
								ItemFee:       250.0,
								SubTotal:      250.0,
								Total:         250.0,
							},
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
								BaseFee:       30,
								RoadFee:       20,
								AdditionalFee: map[string]float64{},
								Discounts: []model.Discount{
									{
										Type:     model.DiscountTypeSubsidize,
										Category: "cate-1",
										Code:     "code-1",
										Discount: 20,
									},
								},
								SubTotal:       50,
								Total:          30,
								Commission:     0,
								WithholdingTax: 0,
							},
							Total: 250 + 30,
						},
						Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
							EstimatedCookingTime: 15,
							PriceScheme:          model.PriceSchemeRMS,
						}},
						EstimatedDeliveryTime: 20,
						Distance:              40,
					},
				},
				DistributeRegions: []model.RegionCode{"AYUTTHAYA"},
				Distance:          20.0,
				CreatedAt:         time.Now().UTC(),
				UpdatedAt:         time.Now().UTC(),
				Options: model.OrderOptions{
					RequireBox: false,
				},
			},
			OrderID: "order-3",
			Status:  model.StatusCompleted,
			Driver:  "driver-1",
			Region:  model.RegionCode("AYUTTHAYA"),
			History: map[string]time.Time{
				string(model.StatusDropOffDone): time.Now().UTC().Add(5 * time.Minute),
				string(model.StatusCompleted):   time.Now().UTC(),
			},
			ExpireAt:              time.Now(),
			RatingScore:           4,
			Comment:               "",
			CancelDetail:          model.CancelDetail{},
			DeliveringPhotoStatus: model.DeliveringPhotoStatusPassed,
		}
	}

	genCompletedSingleTrip := func(order *model.Order) *model.Trip {
		return &model.Trip{
			TripID: "trip-1",
			Routes: []model.TripRoute{
				{
					ID:       order.Routes[0].ID,
					Location: order.Routes[0].Location,
					Distance: order.Routes[0].Distance,
					Action:   model.TripActionPickUp,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order.OrderID,
							StopID:  0,
							Done:    true,
						},
					},
				},
				{
					ID:       order.Routes[1].ID,
					Location: order.Routes[1].Location,
					Distance: order.Routes[1].Distance,
					Action:   model.TripActionDropOff,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order.OrderID,
							StopID:  1,
							Done:    true,
						},
					},
				},
			},
			Orders: []model.TripOrder{
				{
					OrderID:     order.OrderID,
					ServiceType: model.ServiceFood,
					Status:      model.StatusCompleted,
					HeadTo:      1,
				},
			},
			HeadTo: 1,
			Status: model.TripStatusCompleted,
			DriverWageSummary: model.TripDriverWageSummary{
				TransferAmount: 10.0,
				Outstanding:    11.0,
			},
		}
	}
	genArrivedAtMultiplePickUpTrip := func(order1 *model.Order, order2 *model.Order) *model.Trip {
		return &model.Trip{
			TripID: "trip-3",
			Routes: []model.TripRoute{
				{
					ID:       order1.Routes[0].ID,
					Location: order1.Routes[0].Location,
					Distance: order1.Routes[0].Distance,
					Action:   model.TripActionPickUp,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order1.OrderID,
							StopID:  0,
							Done:    false,
						},
					},
				},
				{
					ID:       order2.Routes[0].ID,
					Location: order2.Routes[0].Location,
					Distance: order2.Routes[0].Distance,
					Action:   model.TripActionPickUp,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order2.OrderID,
							StopID:  0,
							Done:    false,
						},
					},
				},
				{
					ID:       order1.Routes[1].ID,
					Location: order1.Routes[1].Location,
					Distance: order1.Routes[1].Distance,
					Action:   model.TripActionDropOff,
					StopOrders: []model.TripStopOrder{
						{
							OrderID: order1.OrderID,
							StopID:  1,
							Done:    false,
						},
						{
							OrderID: order2.OrderID,
							StopID:  1,
							Done:    false,
						},
					},
				},
			},
			Orders: []model.TripOrder{
				{
					OrderID:     order1.OrderID,
					ServiceType: model.ServiceFood,
					Status:      model.StatusArrivedAt,
					HeadTo:      0,
				},
				{
					OrderID:     order2.OrderID,
					ServiceType: model.ServiceFood,
					Status:      model.StatusArrivedAt,
					HeadTo:      0,
				},
			},
			HeadTo: 0,
			Status: model.TripStatusArrivedAt,
		}
	}

	genOrderDetailsMap := func(orders []model.Order) map[string]order.OrderDetailRes {
		orderDetailsMap := make(map[string]order.OrderDetailRes)
		for _, o := range orders {
			odr := order.NewOrderDetailRes(o, nil, nil, nil, order.OrderAPIConfig{EnableEarning: true, AtomicOrderDBConfig: &order.AtomicOrderDBConfig{Config: order.OrderDBConfig{}}}, order.ContingencyConfig{}, model.NegativeBalanceGroupConfig{}, model.TierNegativeBalanceConfig{}, false)
			orderDetailsMap[odr.OrderID] = odr
		}
		return orderDetailsMap
	}

	t.Run("create completed single trip", func(tt *testing.T) {
		order := genOrder1()
		trip := genCompletedSingleTrip(order)
		actual := NewTripHistoryDetailRes(*trip, genOrderDetailsMap([]model.Order{*order}))

		require.Equal(tt, "trip-1", actual.TripID)
		require.Len(tt, actual.Routes, 2)
		require.Len(tt, actual.Orders, 1)
		require.Len(tt, actual.Orders[0].Items, 2)

		stop := actual.Routes[0]
		info := stop.StopOrders[0].Info.StopInfo.(*model.StopInfoFood)
		require.Equal(tt, "stop-1", stop.ID)
		require.Equal(tt, "stop-name-1", stop.Name)
		require.Equal(tt, "address with subdistrict, Tambon Bang Kaew, blah blah", stop.Address)
		require.Equal(tt, model.Location{Lat: 2.03319032, Lng: 2.03482783}, stop.Location)
		require.Equal(tt, 250.0, stop.StopOrders[0].ItemsPrice)
		require.Equal(tt, model.DurationSecond(15), info.EstimatedCookingTime)
		require.Equal(tt, model.PriceSchemeRMS, info.PriceScheme)
		require.Equal(tt, "stop-1", info.RestaurantDirection.RestaurantID)
		require.Equal(tt, "stop-1 parking", info.RestaurantDirection.Parking)
		require.Equal(tt, "stop-1 direction", info.RestaurantDirection.Direction)
		require.Equal(tt, "stop-1 memo", info.RestaurantDirection.Memo)

		stop = actual.Routes[1]
		info = stop.StopOrders[0].Info.StopInfo.(*model.StopInfoFood)
		require.Equal(tt, "stop-2", stop.ID)

		// transfer detail
		require.Equal(tt, TransferDetailRes{TransferAmount: 10.0, Outstanding: 11.0}, actual.TransferDetail)

		// hide sensitive info
		require.Equal(tt, "", stop.Name)
		require.Equal(tt, "", stop.Address)
		require.Equal(tt, model.Location{Lat: 0, Lng: 0}, stop.Location)

		require.Equal(tt, 30.0, actual.Orders[0].PriceSummary.DeliveryFee.BaseFee)
		require.Equal(tt, 20.0, actual.Orders[0].PriceSummary.DeliveryFee.RoadFee)
		require.Len(tt, actual.Orders[0].PriceSummary.DeliveryFee.Discounts, 1)
		require.Equal(tt, 20.0, actual.Orders[0].PriceSummary.DeliveryFee.Discounts[0].Discount)
		require.Equal(tt, 50.0, actual.Orders[0].PriceSummary.DeliveryFee.SubTotal)
		require.Equal(tt, 30.0, actual.Orders[0].PriceSummary.DeliveryFee.Total)
		require.Equal(tt, 250.0, actual.Orders[0].PriceSummary.OrderPrice)
		require.Equal(tt, 280.0, actual.Orders[0].PriceSummary.Total)
		require.Equal(tt, order.Routes[0].Distance, actual.Routes[0].Distance)

		require.Equal(tt, model.DurationSecond(15), info.EstimatedCookingTime)
		require.Equal(tt, model.PriceSchemeRMS, info.PriceScheme)
	})

	t.Run("trip cancelled", func(tt *testing.T) {
		order1, order2 := genOrder1(), genOrder3()
		trip := genArrivedAtMultiplePickUpTrip(order1, order2)

		trip.Status = model.TripStatusCanceled
		order1.Status, trip.Orders[0].Status = model.StatusCanceled, model.StatusCanceled
		order2.Status, trip.Orders[1].Status = model.StatusCanceled, model.StatusCanceled

		actual := NewTripHistoryDetailRes(*trip, genOrderDetailsMap([]model.Order{*order1, *order2}))

		require.Len(tt, actual.InactiveOrders, 2)
		require.Equal(tt, order1.OrderID, actual.InactiveOrders[0].OrderID)
		require.Equal(tt, order1.ServiceType, actual.InactiveOrders[0].ServiceType)
		require.Equal(tt, order1.Status, actual.InactiveOrders[0].Status)
		require.Equal(tt, order1.Options, actual.InactiveOrders[0].Options)
		require.Equal(tt, order1.Routes[0].Info, actual.InactiveOrders[0].PickUpInfo)
		require.Equal(tt, order1.Routes[0].Name, actual.InactiveOrders[0].PickUpName)
		require.Equal(tt, order1.Routes[0].Address, actual.InactiveOrders[0].PickUpAddress)
		require.Equal(tt, order1.Routes[0].PickingItems, actual.InactiveOrders[0].Items)
		require.Equal(tt, "", actual.InactiveOrders[0].DropOffName)
		require.Equal(tt, "", actual.InactiveOrders[0].DropOffAddress)

		// transfer detail
		require.Empty(tt, actual.TransferDetail)

		require.Equal(tt, order2.OrderID, actual.InactiveOrders[1].OrderID)
		require.Equal(tt, order2.ServiceType, actual.InactiveOrders[1].ServiceType)
		require.Equal(tt, order2.Status, actual.InactiveOrders[1].Status)
		require.Equal(tt, order2.Options, actual.InactiveOrders[1].Options)
		require.Equal(tt, order2.Routes[0].Info, actual.InactiveOrders[1].PickUpInfo)
		require.Equal(tt, order2.Routes[0].Name, actual.InactiveOrders[1].PickUpName)
		require.Equal(tt, order2.Routes[0].Address, actual.InactiveOrders[1].PickUpAddress)
		require.Equal(tt, order2.Routes[0].PickingItems, actual.InactiveOrders[1].Items)
		require.Equal(tt, "", actual.InactiveOrders[1].DropOffName)
		require.Equal(tt, "", actual.InactiveOrders[1].DropOffAddress)
	})

	t.Run("create completed single trip with tip id", func(tt *testing.T) {
		order := genOrder1()
		order.TipAmount = 30
		order.Tips = model.TipRecords{
			{
				ID:          "ONGOING_TIP_ID",
				Amount:      10,
				OrderStatus: "DRIVER_TO_DESTINATION",
				CreatedAt:   timeutil.BangkokNow(),
			},
			{
				ID:          "COMPLETED_TIP_ID",
				Amount:      20,
				OrderStatus: "COMPLETED",
				CreatedAt:   timeutil.BangkokNow(),
			},
		}

		trip := genCompletedSingleTrip(order)
		actual := NewTripHistoryDetailRes(*trip, genOrderDetailsMap([]model.Order{*order}))

		require.Equal(tt, types.Money(30), actual.Earning.TotalTipAmount)
		require.Len(tt, actual.Orders, 1)
		require.Equal(tt, types.Money(20), actual.Orders[0].TipAmount)
		require.Equal(tt, types.Money(10), actual.Orders[0].OnGoingTipAmount)
	})
}

func TestNewEarningRes(t *testing.T) {
	t.Parallel()

	t.Run("principal model", func(tt *testing.T) {
		trip := model.Trip{
			RevenuePrincipalModel: true,
			DriverWageSummary: model.TripDriverWageSummary{
				TotalDriverWage: types.NewMoney(50),
				WithHoldingTax:  types.NewMoney(3),
			},
		}
		orders := map[string]order.OrderDetailRes{
			"order-1": {
				PayAtStop: 1,
				Routes: []order.StopRes{
					{},
					{PriceSummary: order.PriceSummaryRes{
						Earning: &model.Earning{
							OnTop:          float64(20),
							TotalOnTopFare: float64(20),
						},
					}},
				},
				OnTopWithholdingTax: types.NewMoney(4),
			},
			"order-2": {
				PayAtStop: 1,
				Routes: []order.StopRes{
					{},
					{PriceSummary: order.PriceSummaryRes{
						Earning: &model.Earning{
							OnTop:          float64(40),
							TotalOnTopFare: float64(40),
						},
					}},
				},
				OnTopWithholdingTax: types.NewMoney(5),
			},
		}

		earning := NewEarningRes(trip, orders)
		require.Equal(tt, types.NewMoney(50), earning.WageWallet)
		require.Equal(tt, types.NewMoney(0), earning.WageCash)
		require.Equal(tt, types.NewMoney(60), earning.OnTop)
		require.Equal(tt, types.NewMoney(12), earning.Tax)
		require.Equal(tt, types.NewMoney(110), earning.TotalBeforeTax)
		require.Equal(tt, types.NewMoney(98), earning.TotalAfterTax)
	})

	t.Run("agent model", func(tt *testing.T) {
		trip := model.Trip{
			DriverWageSummary: model.TripDriverWageSummary{
				Commission:                10,
				AdditionServiceCommission: 5,
				CommissionRate:            0.15,
				WithHoldingOnTopTax:       0.12,
				WithHoldingOnTopTaxRate:   0.03,
			},
		}
		orders := map[string]order.OrderDetailRes{
			"order-1": {
				PayAtStop: 1,
				Routes: []order.StopRes{
					{},
					{
						PriceSummary: order.PriceSummaryRes{
							Earning: &model.Earning{
								Cash:           float64(50),
								Wallet:         float64(30),
								OnTop:          float64(20),
								TotalOnTopFare: float64(20),
							},
						}},
				},
				WithholdingTax:      types.NewMoney(4),
				RevenueAgentModel:   true,
				OnTopFareCommission: 3,
			},
		}

		earning := NewEarningRes(trip, orders)
		require.Equal(tt, types.NewMoney(10), earning.WageWallet)
		require.Equal(tt, types.NewMoney(50), earning.WageCash)
		require.Equal(tt, types.NewMoney(20), earning.OnTop)
		require.Equal(tt, types.NewMoney(0), earning.Tax)
		require.Equal(tt, types.NewMoney(18), earning.CommissionAmount) // COMMISSION(15) + ON_TOP_FARE_COMMISSION(3)
		require.Equal(tt, 15, earning.CommissionRate)
		require.Equal(tt, types.NewMoney(80), earning.TotalBeforeTax)
		require.Equal(tt, types.NewMoney(62).Sub(trip.DriverWageSummary.WithHoldingOnTopTax), earning.TotalAfterTax)
		require.Equal(tt, 3, earning.WithHoldingOnTopTaxRate)
		require.Equal(tt, types.NewMoney(0.12), earning.WithHoldingOnTopTax)
	})

	t.Run("agent model with additional service for cash", func(tt *testing.T) {
		trip := model.Trip{
			DriverWageSummary: model.TripDriverWageSummary{
				Commission:                10,
				AdditionServiceCommission: 5,
				CommissionRate:            0.15,
				WithHoldingOnTopTax:       5,
				WithHoldingOnTopTaxRate:   0.30,
			},
		}
		orders := map[string]order.OrderDetailRes{
			"order-1": {
				PayAtStop: 1,
				Routes: []order.StopRes{
					{},
					{PriceSummary: order.PriceSummaryRes{
						DeliveryFee: order.DeliveryFeeRes{
							PaymentMethod: model.PaymentMethodCash,
						},
						ItemFee: order.ItemFeeRes{
							PaymentMethod: model.PaymentMethodCash,
						},
						Earning: &model.Earning{
							Cash:              float64(70), // DELIVERY_FEE (50) + ADDITIONAL_SERVICE_FEE (20)
							Wallet:            float64(30), // ON_TOP (20) + DISCOUNT (10)
							OnTop:             float64(20),
							TotalOnTopFare:    float64(20),
							AdditionalService: float64(20),
							Total:             float64(100),
						},
					}},
				},
				WithholdingTax:      types.NewMoney(4),
				RevenueAgentModel:   true,
				OnTopFareCommission: 3,
			},
		}

		earning := NewEarningRes(trip, orders)
		require.Equal(tt, types.NewMoney(10), earning.WageWallet) // WALLET (30) - ON_TOP(20)
		require.Equal(tt, types.NewMoney(70), earning.WageCash)   // CASH (70)
		require.Equal(tt, types.NewMoney(60), earning.TotalWage)  // TOTAL (80) - ADDITIONAL_SERVICE_FEE (20)
		require.Equal(tt, types.NewMoney(20), earning.OnTop)
		require.Equal(tt, types.NewMoney(20), earning.AdditionalService)
		require.Equal(tt, types.NewMoney(0), earning.Tax)
		require.Equal(tt, types.NewMoney(18), earning.CommissionAmount) // COMMISSION(15) + ON_TOP_FARE_COMMISSION(3)
		require.Equal(tt, 15, earning.CommissionRate)
		require.Equal(tt, types.NewMoney(100), earning.TotalBeforeTax)
		require.Equal(tt, types.NewMoney(82).Sub(trip.DriverWageSummary.WithHoldingOnTopTax), earning.TotalAfterTax)
		require.Equal(tt, types.NewMoney(5), earning.WithHoldingOnTopTax)
		require.Equal(tt, 30, earning.WithHoldingOnTopTaxRate)
	})

	t.Run("agent model with additional service for e-payment", func(tt *testing.T) {
		trip := model.Trip{
			DriverWageSummary: model.TripDriverWageSummary{
				Commission:                10,
				AdditionServiceCommission: 5,
				CommissionRate:            0.15,
			},
		}
		orders := map[string]order.OrderDetailRes{
			"order-1": {
				PayAtStop: 1,
				Routes: []order.StopRes{
					{},
					{PriceSummary: order.PriceSummaryRes{
						DeliveryFee: order.DeliveryFeeRes{
							PaymentMethod: model.PaymentMethodCreditCard,
						},
						ItemFee: order.ItemFeeRes{
							PaymentMethod: model.PaymentMethodCreditCard,
						},
						Earning: &model.Earning{
							Cash:              float64(0),
							Wallet:            float64(100), // DELIVERY_FEE (50) + ADDITIONAL_SERVICE_FEE (20) + ON_TOP (20) + DISCOUNT (10)
							OnTop:             float64(20),
							TotalOnTopFare:    float64(20),
							AdditionalService: float64(20),
							Total:             float64(100),
						},
					}},
				},
				WithholdingTax:      types.NewMoney(4),
				RevenueAgentModel:   true,
				OnTopFareCommission: 3,
			},
		}

		earning := NewEarningRes(trip, orders)
		require.Equal(tt, types.NewMoney(80), earning.WageWallet) // WALLET (100) - ON_TOP(20)
		require.Equal(tt, types.NewMoney(0), earning.WageCash)
		require.Equal(tt, types.NewMoney(60), earning.TotalWage) // TOTAL (80) - ADDITIONAL_SERVICE_FEE (20)
		require.Equal(tt, types.NewMoney(20), earning.OnTop)
		require.Equal(tt, types.NewMoney(20), earning.TotalOnTopFare)
		require.Equal(tt, types.NewMoney(20), earning.AdditionalService)
		require.Equal(tt, types.NewMoney(0), earning.Tax)
		require.Equal(tt, types.NewMoney(18), earning.CommissionAmount) // COMMISSION(15) + ON_TOP_FARE_COMMISSION(3)
		require.Equal(tt, 15, earning.CommissionRate)
		require.Equal(tt, types.NewMoney(100), earning.TotalBeforeTax)
		require.Equal(tt, types.NewMoney(82), earning.TotalAfterTax)
	})

	t.Run("principal model with EGS on-top", func(tt *testing.T) {
		trip := model.Trip{
			RevenuePrincipalModel: true,
			DriverWageSummary: model.TripDriverWageSummary{
				TotalDriverWage: types.NewMoney(50),
				WithHoldingTax:  types.NewMoney(3),
			},
		}
		orders := map[string]order.OrderDetailRes{
			"order-1": {
				PayAtStop: 1,
				Routes: []order.StopRes{
					{},
					{PriceSummary: order.PriceSummaryRes{
						Earning: &model.Earning{
							OnTop:          float64(20),
							EGSOnTopFare:   float64(50),
							TotalOnTopFare: float64(70),
						},
					}},
				},
				OnTopWithholdingTax: types.NewMoney(4),
			},
			"order-2": {
				PayAtStop: 1,
				Routes: []order.StopRes{
					{},
					{PriceSummary: order.PriceSummaryRes{
						Earning: &model.Earning{
							OnTop:          float64(40),
							EGSOnTopFare:   float64(50),
							TotalOnTopFare: float64(90),
						},
					}},
				},
				OnTopWithholdingTax: types.NewMoney(5),
			},
		}

		earning := NewEarningRes(trip, orders)
		require.Equal(tt, types.NewMoney(60), earning.OnTop)
		require.Equal(tt, types.NewMoney(100), earning.EGSOnTop)
		require.Equal(tt, types.NewMoney(160), earning.TotalOnTopFare)
		require.Equal(tt, types.NewMoney(12), earning.Tax)
		require.Equal(tt, types.NewMoney(210), earning.TotalBeforeTax)
		require.Equal(tt, types.NewMoney(198), earning.TotalAfterTax)
	})

	t.Run("agent model with EGS on-top", func(tt *testing.T) {
		trip := model.Trip{
			DriverWageSummary: model.TripDriverWageSummary{
				Commission:                10,
				AdditionServiceCommission: 5,
				CommissionRate:            0.15,
			},
		}
		orders := map[string]order.OrderDetailRes{
			"order-1": {
				PayAtStop: 1,
				Routes: []order.StopRes{
					{},
					{
						PriceSummary: order.PriceSummaryRes{
							Earning: &model.Earning{
								Cash:           float64(50),
								Wallet:         float64(30),
								OnTop:          float64(20),
								EGSOnTopFare:   float64(100),
								TotalOnTopFare: float64(120),
							},
						}},
				},
				WithholdingTax:      types.NewMoney(4),
				RevenueAgentModel:   true,
				OnTopFareCommission: 3,
			},
		}

		earning := NewEarningRes(trip, orders)
		require.Equal(tt, types.NewMoney(20), earning.OnTop)
		require.Equal(tt, types.NewMoney(100), earning.EGSOnTop)
		require.Equal(tt, types.NewMoney(120), earning.TotalOnTopFare)
		require.Equal(tt, types.NewMoney(0), earning.Tax)
		require.Equal(tt, types.NewMoney(18), earning.CommissionAmount) // COMMISSION(15) + ON_TOP_FARE_COMMISSION(3)
		require.Equal(tt, 15, earning.CommissionRate)
		require.Equal(tt, types.NewMoney(80), earning.TotalBeforeTax)
		require.Equal(tt, types.NewMoney(62), earning.TotalAfterTax)
	})
}

func TestNewMoneySummaryRes(t *testing.T) {
	agentOrder := order.OrderDetailRes{
		PayAtStop: 1,
		Routes: []order.StopRes{
			{},
			{PriceSummary: order.PriceSummaryRes{
				DeliveryFee: order.DeliveryFeeRes{
					Total:         float64(50),
					PaymentMethod: model.PaymentMethodCash,
				},
				ItemFee: order.ItemFeeRes{
					SubTotal:      float64(60),
					Total:         float64(40),
					PaymentMethod: model.PaymentMethodCash,
				},
				Total: float64(200),
			}},
		},
		RevenueAgentModel: true,
	}
	principalOrder1 := order.OrderDetailRes{
		PayAtStop: 1,
		Routes: []order.StopRes{
			{},
			{PriceSummary: order.PriceSummaryRes{
				DeliveryFee: order.DeliveryFeeRes{
					Total:           float64(50),
					UserDeliveryFee: float64(70),
				},
				ItemFee: order.ItemFeeRes{
					SubTotal: float64(60),
					Total:    float64(40),
				},
				Total: float64(200),
			}},
		},
		WithholdingTax: types.NewMoney(4),
	}
	principalOrder2 := order.OrderDetailRes{
		PayAtStop: 1,
		Routes: []order.StopRes{
			{},
			{PriceSummary: order.PriceSummaryRes{
				DeliveryFee: order.DeliveryFeeRes{
					Total:           float64(60),
					UserDeliveryFee: float64(75),
				},
				ItemFee: order.ItemFeeRes{
					SubTotal: float64(70),
					Total:    float64(65),
				},
				Total: float64(300),
			}},
		},
		WithholdingTax: types.NewMoney(4),
	}

	t.Run("agent model", func(tt *testing.T) {
		agentOrders := map[string]order.OrderDetailRes{
			"order-1": agentOrder,
		}
		moneySummary := NewActiveMoneySummaryRes(agentOrders)
		require.Equal(tt, types.NewMoney(0), moneySummary.CashPay)
		require.Equal(tt, types.NewMoney(200), moneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(0), moneySummary.CreditDeduct)
	})

	t.Run("principal model - itemFee CASH, deliveryFee CASH", func(tt *testing.T) {
		cashPrincipalOrder1 := principalOrder1
		cashPrincipalOrder2 := principalOrder2
		cashPrincipalOrder1.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder1.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder2.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder2.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCash

		principalOrders := map[string]order.OrderDetailRes{
			"order-1": cashPrincipalOrder1,
			"order-2": cashPrincipalOrder2,
		}
		moneySummary := NewActiveMoneySummaryRes(principalOrders)
		require.Equal(tt, types.NewMoney(130), moneySummary.CashPay)
		require.Equal(tt, types.NewMoney(500), moneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(0), moneySummary.CreditDeduct)
	})

	t.Run("principal model - itemFee CASH, deliveryFee CASH but HalfHalf", func(tt *testing.T) {
		cashPrincipalOrder1 := principalOrder1
		cashPrincipalOrder2 := principalOrder2
		cashPrincipalOrder1.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder1.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder2.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder2.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder1.SpecialEvent = []string{model.SpecialEventHalfHalf}
		cashPrincipalOrder2.SpecialEvent = []string{model.SpecialEventHalfHalf}

		principalOrders := map[string]order.OrderDetailRes{
			"order-1": cashPrincipalOrder1,
			"order-2": cashPrincipalOrder2,
		}
		moneySummary := NewActiveMoneySummaryRes(principalOrders)
		require.Equal(tt, types.NewMoney(0), moneySummary.CashPay)
		// For HALF-HALF order, priceSummary.Total is overwritten by earning.cash in order detail res
		require.Equal(tt, types.NewMoney(500), moneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(0), moneySummary.CreditDeduct)
	})

	t.Run("principal model - itemFee CASH, deliveryFee E-PAYMENT but HalfHalf", func(tt *testing.T) {
		cashPrincipalOrder1 := principalOrder1
		cashPrincipalOrder2 := principalOrder2
		cashPrincipalOrder1.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCreditCard
		cashPrincipalOrder1.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder2.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCreditCard
		cashPrincipalOrder2.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder1.SpecialEvent = []string{model.SpecialEventHalfHalf}
		cashPrincipalOrder2.SpecialEvent = []string{model.SpecialEventHalfHalf}

		principalOrders := map[string]order.OrderDetailRes{
			"order-1": cashPrincipalOrder1,
			"order-2": cashPrincipalOrder2,
		}
		moneySummary := NewActiveMoneySummaryRes(principalOrders)
		require.Equal(tt, types.NewMoney(0), moneySummary.CashPay)
		require.Equal(tt, types.NewMoney(0), moneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(0), moneySummary.CreditDeduct)
	})

	t.Run("principal model - itemFee CASH, deliveryFee CASH - all cancelled", func(tt *testing.T) {
		cashPrincipalOrder1 := principalOrder1
		cashPrincipalOrder2 := principalOrder2
		cashPrincipalOrder1.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder1.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder2.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder2.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder1.Status = model.StatusCanceled
		cashPrincipalOrder2.Status = model.StatusCanceled

		principalOrders := map[string]order.OrderDetailRes{
			"order-1": cashPrincipalOrder1,
			"order-2": cashPrincipalOrder2,
		}

		activeMoneySummary := NewActiveMoneySummaryRes(principalOrders)
		require.Equal(tt, types.NewMoney(0), activeMoneySummary.CashPay)
		require.Equal(tt, types.NewMoney(0), activeMoneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(0), activeMoneySummary.CreditDeduct)

		inactiveMoneySummary := NewInactiveMoneySummaryRes(principalOrders)
		require.Equal(tt, types.NewMoney(130), inactiveMoneySummary.CashPay)
		require.Equal(tt, types.NewMoney(500), inactiveMoneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(0), inactiveMoneySummary.CreditDeduct)
	})

	t.Run("principal model - itemFee E-PAYMENT, deliveryFee CASH", func(tt *testing.T) {
		cashPrincipalOrder1 := principalOrder1
		cashPrincipalOrder2 := principalOrder2
		cashPrincipalOrder1.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder1.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCreditCard
		cashPrincipalOrder2.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCash
		cashPrincipalOrder2.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCreditCard

		principalOrders := map[string]order.OrderDetailRes{
			"order-1": cashPrincipalOrder1,
			"order-2": cashPrincipalOrder2,
		}
		moneySummary := NewActiveMoneySummaryRes(principalOrders)
		require.Equal(tt, types.NewMoney(0), moneySummary.CashPay)
		require.Equal(tt, types.NewMoney(145), moneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(0), moneySummary.CreditDeduct)
	})

	t.Run("principal model - itemFee E-PAYMENT, deliveryFee E-PAYMENT", func(tt *testing.T) {
		cashPrincipalOrder1 := principalOrder1
		cashPrincipalOrder2 := principalOrder2
		cashPrincipalOrder1.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCreditCard
		cashPrincipalOrder1.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCreditCard
		cashPrincipalOrder2.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCreditCard
		cashPrincipalOrder2.Routes[1].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCreditCard

		principalOrders := map[string]order.OrderDetailRes{
			"order-1": cashPrincipalOrder1,
			"order-2": cashPrincipalOrder2,
		}
		moneySummary := NewActiveMoneySummaryRes(principalOrders)
		require.Equal(tt, types.NewMoney(0), moneySummary.CashPay)
		require.Equal(tt, types.NewMoney(0), moneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(0), moneySummary.CreditDeduct)
	})

	t.Run("principal model - FlowCashAdvancementCoupon", func(tt *testing.T) {
		cashPrincipalOrder1 := principalOrder1
		cashPrincipalOrder2 := principalOrder2
		cashPrincipalOrder1.Options.DriverMoneyFlow = model.FlowCashAdvancementCoupon
		cashPrincipalOrder2.Options.DriverMoneyFlow = model.FlowCashAdvancementCoupon

		principalOrders := map[string]order.OrderDetailRes{
			"order-1": cashPrincipalOrder1,
			"order-2": cashPrincipalOrder2,
		}
		moneySummary := NewActiveMoneySummaryRes(principalOrders)
		require.Equal(tt, types.NewMoney(130), moneySummary.CashPay)
		require.Equal(tt, types.NewMoney(500), moneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(0), moneySummary.CreditDeduct)
	})

	t.Run("principal model - FlowCashAdvancementEpayment", func(tt *testing.T) {
		cashPrincipalOrder1 := principalOrder1
		cashPrincipalOrder2 := principalOrder2
		cashPrincipalOrder1.Options.DriverMoneyFlow = model.FlowCashAdvancementEpayment
		cashPrincipalOrder2.Options.DriverMoneyFlow = model.FlowCashAdvancementEpayment

		principalOrders := map[string]order.OrderDetailRes{
			"order-1": cashPrincipalOrder1,
			"order-2": cashPrincipalOrder2,
		}
		moneySummary := NewActiveMoneySummaryRes(principalOrders)
		require.Equal(tt, types.NewMoney(130), moneySummary.CashPay)
		require.Equal(tt, types.NewMoney(0), moneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(0), moneySummary.CreditDeduct)
	})

	t.Run("principal model - FlowCashCollection", func(tt *testing.T) {
		cashPrincipalOrder1 := principalOrder1
		cashPrincipalOrder2 := principalOrder2
		cashPrincipalOrder1.Options.DriverMoneyFlow = model.FlowCashCollection
		cashPrincipalOrder2.Options.DriverMoneyFlow = model.FlowCashCollection

		principalOrders := map[string]order.OrderDetailRes{
			"order-1": cashPrincipalOrder1,
			"order-2": cashPrincipalOrder2,
		}
		moneySummary := NewActiveMoneySummaryRes(principalOrders)
		require.Equal(tt, types.NewMoney(0), moneySummary.CashPay)
		require.Equal(tt, types.NewMoney(500), moneySummary.CashReceive)
		require.Equal(tt, types.NewMoney(130), moneySummary.CreditDeduct)
	})
}
