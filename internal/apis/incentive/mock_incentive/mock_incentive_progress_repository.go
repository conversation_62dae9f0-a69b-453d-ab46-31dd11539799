// Code generated by MockGen. DO NOT EDIT.
// Source: ./incentive_progress_repository.go

// Package mock_incentive is a generated GoMock package.
package mock_incentive

import (
	context "context"
	reflect "reflect"
	time "time"

	incentive "git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
)

// MockIncentiveProgressRepository is a mock of IncentiveProgressRepository interface.
type MockIncentiveProgressRepository struct {
	ctrl     *gomock.Controller
	recorder *MockIncentiveProgressRepositoryMockRecorder
}

// MockIncentiveProgressRepositoryMockRecorder is the mock recorder for MockIncentiveProgressRepository.
type MockIncentiveProgressRepositoryMockRecorder struct {
	mock *MockIncentiveProgressRepository
}

// NewMockIncentiveProgressRepository creates a new mock instance.
func NewMockIncentiveProgressRepository(ctrl *gomock.Controller) *MockIncentiveProgressRepository {
	mock := &MockIncentiveProgressRepository{ctrl: ctrl}
	mock.recorder = &MockIncentiveProgressRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIncentiveProgressRepository) EXPECT() *MockIncentiveProgressRepositoryMockRecorder {
	return m.recorder
}

// GetByDateRange mocks base method.
func (m *MockIncentiveProgressRepository) GetByDateRange(ctx context.Context, driverID, incentiveId string, startDate, endDate time.Time) ([]incentive.IncentiveProgress, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByDateRange", ctx, driverID, incentiveId, startDate, endDate)
	ret0, _ := ret[0].([]incentive.IncentiveProgress)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByDateRange indicates an expected call of GetByDateRange.
func (mr *MockIncentiveProgressRepositoryMockRecorder) GetByDateRange(ctx, driverID, incentiveId, startDate, endDate interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByDateRange", reflect.TypeOf((*MockIncentiveProgressRepository)(nil).GetByDateRange), ctx, driverID, incentiveId, startDate, endDate)
}

// Upsert mocks base method.
func (m *MockIncentiveProgressRepository) Upsert(ctx context.Context, progress *incentive.IncentiveProgress, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, progress}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Upsert", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockIncentiveProgressRepositoryMockRecorder) Upsert(ctx, progress interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, progress}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockIncentiveProgressRepository)(nil).Upsert), varargs...)
}
