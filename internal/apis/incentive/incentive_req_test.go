package incentive

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/twpayne/go-geom"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func Test_validData(t *testing.T) {
	t.Run("error whitelist exceed", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()
		cfg.whitelistLimit = 2

		req := Request{WhitelistIDs: []string{"a", "b", "c"}}
		err := req.validateInput(cfg)
		require.Error(t, err)
		require.Equal(t, "whitelist limit exceed [3] limit [2]", err.Error())
	})

	t.Run("error coordinate exceed", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()
		cfg.coordinateLimit = 2
		first := [][]geom.Coord{{
			{100.6270408630371, 14.735540398030539},
			{100.65845489501953, 14.719602330888959},
			{100.667724609375, 14.752141311434283},
			{100.63253402709961, 14.756457341526813},
			{100.6270408630371, 14.735540398030539},
		}}

		second := [][]geom.Coord{{
			{100.6270408630371, 14.735540398030539},
			{100.65845489501953, 14.719602330888959},
			{100.667724609375, 14.752141311434283},
			{100.63253402709961, 14.756457341526813},
			{100.6270408630371, 14.735540398030539},
		}}

		third := [][]geom.Coord{{
			{100.6270408630371, 14.735540398030539},
			{100.65845489501953, 14.719602330888959},
			{100.667724609375, 14.752141311434283},
			{100.63253402709961, 14.756457341526813},
			{100.6270408630371, 14.735540398030539},
		}}

		req := Request{
			Coordinates: Coordinates{first, second, third},
		}
		err := req.validateInput(cfg)
		require.Error(t, err)
		require.Equal(t, "coordinates limit exceed [3] limit [2]", err.Error())
	})

	t.Run("error sub polygon", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()
		cfg.supCoordinateLimit = 2
		first := [][]geom.Coord{{
			{100.6270408630371, 14.735540398030539},
			{100.65845489501953, 14.719602330888959},
			{100.667724609375, 14.752141311434283},
			{100.63253402709961, 14.756457341526813},
			{100.6270408630371, 14.735540398030539},
		}}

		req := Request{
			Coordinates: Coordinates{first},
		}
		err := req.validateInput(cfg)
		require.Error(t, err)
		require.Equal(t, errInvalidGeometry, err)
	})

	t.Run("valid limit whitelist", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()
		req := Request{WhitelistIDs: []string{"a", "b", "c"}}
		err := req.validateInput(cfg)
		require.NoError(t, err)
	})

	t.Run("auto trim whitelist with success", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()
		req := Request{WhitelistIDs: []string{"a ", " b", " c ", "d"}}
		err := req.validateInput(cfg)
		require.NoError(t, err)
		require.Equal(t, req.WhitelistIDs[0], "a")
		require.Equal(t, req.WhitelistIDs[1], "b")
		require.Equal(t, req.WhitelistIDs[2], "c")
		require.Equal(t, req.WhitelistIDs[3], "d")
	})

	t.Run("valid limit blacklist", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()
		req := Request{BlacklistIDs: []string{"a", "b", "c"}}
		err := req.validateInput(cfg)
		require.NoError(t, err)
	})

	t.Run("auto trim blacklist with success", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()
		req := Request{BlacklistIDs: []string{"a ", " b", " c ", "d"}}
		err := req.validateInput(cfg)
		require.NoError(t, err)
		require.Equal(t, req.BlacklistIDs[0], "a")
		require.Equal(t, req.BlacklistIDs[1], "b")
		require.Equal(t, req.BlacklistIDs[2], "c")
		require.Equal(t, req.BlacklistIDs[3], "d")
	})

	t.Run("valid limit coordinate", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()
		first := [][]geom.Coord{{
			{100.6270408630371, 14.735540398030539},
			{100.65845489501953, 14.719602330888959},
			{100.667724609375, 14.752141311434283},
			{100.63253402709961, 14.756457341526813},
			{100.6270408630371, 14.735540398030539},
		}}

		second := [][]geom.Coord{{
			{100.6270408630371, 14.735540398030539},
			{100.65845489501953, 14.719602330888959},
			{100.667724609375, 14.752141311434283},
			{100.63253402709961, 14.756457341526813},
			{100.6270408630371, 14.735540398030539},
		}}

		third := [][]geom.Coord{{
			{100.6270408630371, 14.735540398030539},
			{100.65845489501953, 14.719602330888959},
			{100.667724609375, 14.752141311434283},
			{100.63253402709961, 14.756457341526813},
			{100.6270408630371, 14.735540398030539},
		}}
		req := Request{
			Coordinates: Coordinates{first, second, third},
		}
		err := req.validateInput(cfg)
		require.NoError(t, err)
	})

	t.Run("valid sub polygon", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()
		first := [][]geom.Coord{{
			{100.6270408630371, 14.735540398030539},
			{100.65845489501953, 14.719602330888959},
			{100.667724609375, 14.752141311434283},
			{100.63253402709961, 14.756457341526813},
			{100.6270408630371, 14.735540398030539},
		}}

		req := Request{
			Coordinates: Coordinates{first},
		}
		err := req.validateInput(cfg)
		require.NoError(t, err)
	})

	t.Run("if period incentive and doesnt supply display name should error", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()

		req := Request{
			PaymentType: Period,
		}
		err := req.validateInput(cfg)
		require.Error(t, err)
		require.Errorf(t, err, "for period incentive, display name must be specified")
	})

	t.Run("if period incentive and supply display name should no error", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()

		req := Request{
			PaymentType: Period,
			DisplayName: "Display name incentive",
		}
		err := req.validateInput(cfg)
		require.NoError(t, err)
	})

	t.Run("if region doesnt exist should error", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()
		cfg.checkRegionExistsFn = func(ctx context.Context, region string) bool {
			return false
		}

		req := Request{
			Region: "doesn't exists",
		}
		err := req.validateInput(cfg)
		require.Error(t, err)
	})

	t.Run("CR", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()

		testCases := []struct {
			name     string
			CR       float64
			validate func(t *testing.T, err error)
		}{
			{
				name: "CR > 100",
				CR:   101,
				validate: func(t *testing.T, err error) {
					require.Error(t, err)
				},
			},
			{
				name: "CR < 0",
				CR:   -1,
				validate: func(t *testing.T, err error) {
					require.Error(t, err)
				},
			},
			{
				name: "CR == 0",
				CR:   0,
				validate: func(t *testing.T, err error) {
					require.NoError(t, err)
				},
			},
			{
				name: "CR == 100",
				CR:   100,
				validate: func(t *testing.T, err error) {
					require.NoError(t, err)
				},
			},
			{
				name: "CR == 50",
				CR:   50,
				validate: func(t *testing.T, err error) {
					require.NoError(t, err)
				},
			},
		}

		for _, v := range testCases {
			tc := v
			t.Run(tc.name, func(t *testing.T) {
				req := Request{
					CR: &v.CR,
				}
				err := req.validateInput(cfg)
				tc.validate(t, err)
			})
		}
	})

	t.Run("AR", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()

		testCases := []struct {
			name     string
			AR       float64
			validate func(t *testing.T, err error)
		}{
			{
				name: "AR > 100",
				AR:   101,
				validate: func(t *testing.T, err error) {
					require.Error(t, err)
				},
			},
			{
				name: "AR < 0",
				AR:   -1,
				validate: func(t *testing.T, err error) {
					require.Error(t, err)
				},
			},
			{
				name: "AR == 0",
				AR:   0,
				validate: func(t *testing.T, err error) {
					require.NoError(t, err)
				},
			},
			{
				name: "AR == 100",
				AR:   100,
				validate: func(t *testing.T, err error) {
					require.NoError(t, err)
				},
			},
			{
				name: "AR == 50",
				AR:   50,
				validate: func(t *testing.T, err error) {
					require.NoError(t, err)
				},
			},
		}

		for _, v := range testCases {
			tc := v
			t.Run(tc.name, func(t *testing.T) {
				req := Request{
					AR: &v.AR,
				}
				err := req.validateInput(cfg)
				tc.validate(t, err)
			})
		}
	})

	t.Run("Rating", func(t *testing.T) {
		cfg := getDefaultCfgInputValidator()

		testCases := []struct {
			name     string
			Rating   float64
			validate func(t *testing.T, err error)
		}{
			{
				name:   "Rating > 5",
				Rating: 6,
				validate: func(t *testing.T, err error) {
					require.Error(t, err)
				},
			},
			{
				name:   "Rating < 0",
				Rating: -1,
				validate: func(t *testing.T, err error) {
					require.Error(t, err)
				},
			},
			{
				name:   "Rating == 0",
				Rating: 0,
				validate: func(t *testing.T, err error) {
					require.NoError(t, err)
				},
			},
			{
				name:   "Rating == 5",
				Rating: 5,
				validate: func(t *testing.T, err error) {
					require.NoError(t, err)
				},
			},
			{
				name:   "Rating == 3",
				Rating: 3,
				validate: func(t *testing.T, err error) {
					require.NoError(t, err)
				},
			},
		}

		for _, v := range testCases {
			tc := v
			t.Run(tc.name, func(t *testing.T) {
				req := Request{
					Rating: &v.Rating,
				}
				err := req.validateInput(cfg)
				tc.validate(t, err)
			})
		}
	})

	t.Run("Streak incentive", func(t *testing.T) {
		type testCase struct {
			dateRange DateRangeReq
			streak    Streak
		}

		shouldOk := []testCase{
			{
				dateRange: DateRangeReq{
					Start: time.Date(2024, 5, 23, 0, 0, 0, 0, timeutil.BangkokLocation()),
					End:   time.Date(2024, 5, 25, 23, 59, 59, 0, timeutil.BangkokLocation()),
				},
				streak: Streak{
					NoOfDays:     1,
					OrdersPerDay: 10,
					Amount:       1000,
				},
			},
			{
				dateRange: DateRangeReq{
					Start: time.Date(2024, 5, 23, 0, 0, 0, 0, timeutil.BangkokLocation()),
					End:   time.Date(2024, 5, 24, 23, 59, 59, 0, timeutil.BangkokLocation()),
				},
				streak: Streak{
					NoOfDays:     1,
					OrdersPerDay: 10,
					Amount:       1000,
				},
			},
			{
				dateRange: DateRangeReq{
					Start: time.Date(2024, 5, 23, 0, 0, 0, 0, timeutil.BangkokLocation()),
					End:   time.Date(2024, 5, 25, 23, 59, 59, 0, timeutil.BangkokLocation()),
				},
				streak: Streak{
					NoOfDays:     3,
					OrdersPerDay: 10,
					Amount:       1000,
				},
			},
		}

		for i := range shouldOk {
			test := &shouldOk[i]
			req := Request{
				PaymentType: PeriodStreak,
				DateRange:   test.dateRange,
				Streak:      test.streak,
			}

			err := req.validateStreak()
			require.NoError(t, err, "unexpected error from streak validation")
		}
	})

}

func getDefaultCfgInputValidator() cfgInputValidator {
	return cfgInputValidator{
		coordinateLimit:    defaultLimitCoordinate,
		supCoordinateLimit: defaultLimitSubCoordinate,
		whitelistLimit:     defaultLimitWhitelist,
		blacklistLimit:     defaultLimitBlacklist,
		checkRegionExistsFn: func(ctx context.Context, region string) bool {
			return true
		},
	}
}
