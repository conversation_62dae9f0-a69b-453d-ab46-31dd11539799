package incentive

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type Response struct {
	Name           string          `json:"name" binding:"required"`
	DisplayName    string          `json:"displayName"`
	LocationName   string          `json:"locationName"`
	IncentiveID    string          `json:"incentiveID"`
	Description    string          `json:"description"`
	Region         string          `json:"region" binding:"required"`
	PaymentType    PaymentType     `json:"paymentType"`
	ServiceTypes   []model.Service `json:"serviceTypes"`
	Coordinates    Coordinates     `json:"coordinates"`
	OrderTier      []OrderTierRes  `json:"orderTier" binding:"required"`
	DateRange      DateRangeRes    `json:"dateRange"`
	Times          []TimesRes      `json:"times"`
	Active         bool            `json:"active"`
	Sources        []string        `json:"sources"`
	AR             *float64        `json:"ar"`
	CR             *float64        `json:"cr"`
	Rating         *float64        `json:"rating"`
	Tiers          []string        `json:"tiers"`
	Box            *bool           `json:"box"`
	Jacket         *bool           `json:"jacket"`
	WhitelistIDs   []string        `json:"whitelistIDs"`
	BlacklistIDs   []string        `json:"blacklistIDs"`
	OrderShiftType OrderShiftType  `json:"orderType"`
	ZoneCode       string          `json:"zoneCode"`
	Streak         Streak          `json:"streak"`
	CreatedBy      string          `json:"createdBy"`
	CreatedAt      time.Time       `json:"CreatedAt"`
	UpdatedBy      string          `json:"UpdatedBy"`
	UpdatedAt      time.Time       `json:"updatedAt"`
}

type TimesRes struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

type DateRangeRes struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

type OrderTierRes struct {
	MinOrderAmount int64 `json:"minOrderAmount"`
	MaxOrderAmount int64 `json:"maxOrderAmount"`

	IncentiveAmount types.Money `json:"incentiveAmount"`
}

func NewResponse(in *Incentive) *Response {
	// backward compatibility
	if string(in.OrderShiftType) == "" {
		in.OrderShiftType = AllOrderType
	}

	r := &Response{
		Name:         in.Name,
		DisplayName:  in.DisplayName,
		LocationName: in.LocationName,
		IncentiveID:  in.IncentiveID,
		PaymentType:  in.PaymentType,
		Description:  in.Description,
		Region:       string(in.Region),
		ServiceTypes: in.ServiceTypes,
		DateRange: DateRangeRes{
			Start: in.DateRange.Start,
			End:   in.DateRange.End,
		},
		Active:         in.Active,
		Coordinates:    in.Geometry.Coordinates,
		Sources:        in.Sources,
		AR:             in.AR,
		CR:             in.CR,
		Rating:         in.Rating,
		Tiers:          in.Tiers,
		Box:            in.Box,
		Jacket:         in.Jacket,
		WhitelistIDs:   in.WhitelistIDs,
		BlacklistIDs:   in.BlacklistIDs,
		OrderShiftType: in.OrderShiftType,
		ZoneCode:       in.ZoneCode,
		Streak:         in.Streak,
		CreatedBy:      in.CreatedBy,
		UpdatedBy:      in.UpdatedBy,
		CreatedAt:      timeutil.ToThaiTimeZone(in.CreatedAt),
		UpdatedAt:      timeutil.ToThaiTimeZone(in.UpdatedAt),
	}

	r.Times = r.ModelTimesRes(in)
	r.OrderTier = r.ModelOrderTierRes(in)

	return r
}

func (r *Response) ModelTimesRes(inc *Incentive) []TimesRes {
	trs := make([]TimesRes, len(inc.Times))

	for i, t := range inc.Times {
		trs[i] = TimesRes(t)
	}

	return trs
}

func (r *Response) ModelOrderTierRes(inc *Incentive) []OrderTierRes {
	ors := make([]OrderTierRes, len(inc.OrderTier))
	for i, t := range inc.OrderTier {
		ors[i] = OrderTierRes(t)
	}

	return ors
}
