package admin

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/go/logx/v2"
	absintheapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/account"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkprocess"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/bulk"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack"
	"git.wndv.co/lineman/fleet-distribution/internal/domain"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/eventrunner/event"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/income"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	"git.wndv.co/lineman/xgo/util"
)

// DriverAdminAPI provides api to control drivers over admin.
type DriverAdminAPI struct {
	AssignmentLogRepository  repository.AssignmentLogRepository
	BanService               service.BanService
	DriverService            service.DriverServiceInterface
	BanHistoryRepository     repository.BanHistoryRepository
	DriverRepo               repository.DriverRepository
	DriverOrderInfoRepo      repository.DriverOrderInfoRepository
	DriverCancelRepo         repository.CancelReasonRepository
	DriverRegistrationRepo   repository.DriverRegistrationRepository
	OrderRepo                repository.OrderRepository
	DedicatedZoneRepo        repository.DedicatedZoneRepository
	Cfg                      Config
	slack                    slack.Slack
	VosService               service.VOSService
	ShiftService             service.ShiftServices
	Bus                      domain.EventBus
	UpdateProfileSectionRepo repository.RequestUpdateProfileSectionRepository
	RequestUpdateProfileRepo repository.RequestUpdateProfileRepository
	TxnHelper                transaction.TxnHelper
	IncomeSummaryService     income.IncomeSummaryService
	BanEffectiveTimeRepo     repository.BanEffectiveTimeRepository
	AuditLog                 repository.AuditLogRepository
	ServiceAreaRepo          repository.ServiceAreaRepository
	FeatureFlagService       featureflag.Service
	BulkProcessInfoRepo      repository.BulkProcessInfoRepository
	GlobalConfig             config.GlobalConfig
	CancelReasonConfig       *service.AtomicCancelReasonConfig
	AtomicAdminConfig        *config.AtomicAdminConfig
	BulkConfig               *bulk.Config
}

// BanDriverWithMetadata ban driver by using information in metadata
func (api *DriverAdminAPI) BanDriverWithMetadata(gctx *gin.Context) {
	ctx := gctx.Request.Context()

	var req BanDriverWithMetadataRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		_ = gctx.Error(apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	driverID := gctx.Param("driver_id")
	driv, err := api.DriverRepo.GetProfile(ctx, driverID)
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	if driv.BanLater || driv.Status == model.StatusBanned {
		if driv.Reason == model.ReasonOfflineLater {
			apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, errors.New("driver has on-going offline later ban, please try again later or refresh your screen")))
			return
		}
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, errors.New("driver has on-going ban, please try again later or refresh your screen")))
		return
	}

	banReasons, err := api.BanService.GetBanReasons(ctx, req.ReasonsMetadata)
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	banDriverRequest := model.BanDriverDetail{
		CreatedBy:                 req.CreatedBy,
		Type:                      string(req.BanType),
		MessageToDriver:           req.MessageToDriver,
		Url:                       req.Url,
		Category:                  req.Category,
		Reason:                    req.Reason,
		RetrainingTitle:           req.RetrainingTitle,
		RetrainingMessageToDriver: req.RetrainingMessageToDriver,
	}

	if req.BannedUntil != nil {
		banDriverRequest.BannedUntil = *req.BannedUntil
	}

	if err := api.BanService.BanWithMetadata(ctx, driv, banReasons, req.BanType, req.BannedUntil, banDriverRequest); err != nil {
		_ = gctx.Error(err)
		return
	}

	if req.BanType == model.BanTypeTemporary || req.BanType == model.BanTypePermanent {
		var end time.Time
		if req.BannedUntil != nil {
			end = *req.BannedUntil
		}

		err = api.removeShift(ctx, driv.DriverID, end)
		if err != nil {
			_ = gctx.Error(err)
			return
		}
	}

	apiutil.NoContent(gctx)
}

// BulkBanDriver ban driver by given list of driver id in csv file.
func (api *DriverAdminAPI) BulkBanDriver(ctx *gin.Context) {
	var req BulkBanDriverRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	driverIDs, err := req.GetDriverIDs()
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	banReasons := api.getBanReasonsIfPossible(ctx, req)
	if !req.BanDriverRequest.EffectiveTime.IsZero() {
		req.BanDriverRequest.EffectiveTime = timeutil.TimeTruncateInHalfHour(req.BanDriverRequest.EffectiveTime)
		if timeutil.BangkokNow().After(req.BanDriverRequest.EffectiveTime) {
			apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, errors.New("time already passed")))
			return
		}
		if err := api.createBanEffectiveTime(ctx, req.BanDriverRequest, banReasons, driverIDs); err != nil {
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}

		apiutil.OK(ctx, nil)
		return
	}

	drivers, err := api.DriverRepo.FindDriverIDs(ctx.Request.Context(), driverIDs)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	resp := &BulkIDsResponse{}
	wg := sync.WaitGroup{}
	wg.Add(len(drivers))
	wk, release := safe.NewWorker(api.Cfg.BulkBanUnBanWorker)
	defer release()
	for _, drv := range drivers {
		api.banDriverInBackground(ctx.Request.Context(), drv, req.BanDriverRequest, banReasons, resp, &wg, wk)
	}
	wg.Wait()

	apiutil.OK(ctx, resp)
}

func (api *DriverAdminAPI) getBanReasonsIfPossible(ctx context.Context, req BulkBanDriverRequest) []*model.BanReason {
	var banReasons []*model.BanReason
	if req.BanDriverRequest.MetadataId != "" {
		reasonsMetadata := []*model.BanReasonMetadata{{
			MetadataID:     req.BanDriverRequest.MetadataId,
			InternalReason: req.BanDriverRequest.MetadataInternalReason,
		}}
		banReasonsResult, err := api.BanService.GetBanReasons(ctx, reasonsMetadata)
		if err != nil {
			logrus.Errorf("BulkBanDriver GetBanReasons Err: %v", err)
		}
		banReasons = banReasonsResult
	}
	return banReasons
}

func (api *DriverAdminAPI) banDriverInBackground(ctx context.Context, drv model.Driver, banDriverRequest BanDriverRequest, banReasons []*model.BanReason, resp *BulkIDsResponse, wg *sync.WaitGroup, wk *safe.Worker) {
	fn := func() {
		defer wg.Done()
		banType := model.BanType(banDriverRequest.Type)
		if banType == model.BanTypeTemporary {
			banDriverRequest.BannedUntil = banDriverRequest.NewBanUntil()
		}
		banDriverReq := model.BanDriverDetail{
			CreatedBy:                 banDriverRequest.CreatedBy,
			Type:                      banDriverRequest.Type,
			MessageToDriver:           banDriverRequest.MessageToDriver,
			BannedUntil:               banDriverRequest.BannedUntil,
			Category:                  banDriverRequest.Category,
			Reason:                    banDriverRequest.Reason,
			Url:                       banDriverRequest.Url,
			RetrainingTitle:           banDriverRequest.RetrainingTitle,
			RetrainingMessageToDriver: banDriverRequest.RetrainingMessageToDriver,
		}
		if err := api.BanService.BanWithMetadata(ctx, &drv, banReasons, banType, &banDriverRequest.BannedUntil, banDriverReq); err != nil {
			resp.AddFailure(drv.DriverID)
			logrus.Errorf("BulkBanDriver BanWithMetadata cannot ban driver: driverId=%s err=%v", drv.DriverID, err)
			return
		}
		if banType == model.BanTypeTemporary || banType == model.BanTypePermanent {
			err := api.removeShift(ctx, drv.DriverID, banDriverRequest.BannedUntil)
			if err != nil {
				resp.AddFailure(drv.DriverID)
				logrus.Errorf("BulkBanDriver BanWithMetadata removeShift driver: driverId=%s err=%v", drv.DriverID, err)
				return
			}
		}
		resp.AddSuccess(drv.DriverID)
	}
	wk.GoFuncWithPool(fn)
}

func (api *DriverAdminAPI) createBanEffectiveTime(
	ctx context.Context,
	banDriverRequest BanDriverRequest,
	banReasons []*model.BanReason,
	driverIDs []string,
) error {
	if banDriverRequest.EffectiveTime.IsZero() {
		return errors.New("invalid effective time")
	}

	banType := model.BanType(banDriverRequest.Type)
	if banType == model.BanTypeTemporary {
		banDriverRequest.BannedUntil = banDriverRequest.NewBanUntil()
	}
	banDriverDetail := model.BanDriverDetail{
		CreatedBy:                 banDriverRequest.CreatedBy,
		Type:                      banDriverRequest.Type,
		MessageToDriver:           banDriverRequest.MessageToDriver,
		BannedUntil:               banDriverRequest.BannedUntil,
		Category:                  banDriverRequest.Category,
		Reason:                    banDriverRequest.Reason,
		Url:                       banDriverRequest.Url,
		RetrainingTitle:           banDriverRequest.RetrainingTitle,
		RetrainingMessageToDriver: banDriverRequest.RetrainingMessageToDriver,
	}

	banModel := model.BanEffectiveTime{
		BanType:         banType,
		BanReasons:      banReasons,
		Detail:          banDriverDetail,
		Status:          model.BanEffectiveWaiting,
		EffectDriverIDs: driverIDs,
		CreatedAt:       timeutil.BangkokNow(),
		EffectiveTime:   timeutil.TimeTruncateInHalfHour(banDriverRequest.EffectiveTime),
	}

	if err := api.BanEffectiveTimeRepo.Create(ctx, banModel); err != nil {
		logrus.Errorf("BulkBanDriver createBanEffectiveTime cannot create ban effective time: %v - %v", banDriverRequest, err)
		return err
	}
	return nil
}

// UnbanDriver unban driver by driver id.
func (api *DriverAdminAPI) UnbanDriver(ctx *gin.Context) {
	var req UnbanDriverRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	driverID := ctx.Param("driver_id")
	drv, err := api.DriverRepo.GetProfile(ctx, driverID)
	if err != nil {
		apiutil.ErrNotFound(ctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_FOUND, err))
		return
	}

	if err := api.doUnbanDriver(ctx.Request.Context(), drv, req); err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(ctx, apiutil.EmptyBody())
}

// BulkUnbanDriver unban driver by given list of driver id in csv file.
func (api *DriverAdminAPI) BulkUnbanDriver(ctx *gin.Context) {
	var req BulkUnbanDriverRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	driverIds, err := req.GetDriverIDs()
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	drivers, err := api.DriverRepo.FindDriverIDs(ctx.Request.Context(), driverIds)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	resp := &BulkIDsResponse{}
	wg := sync.WaitGroup{}
	wg.Add(len(drivers))
	wk, release := safe.NewWorker(api.Cfg.BulkBanUnBanWorker)
	defer release()
	for _, drv := range drivers {
		api.unbanDriverInBackground(ctx.Request.Context(), drv, req.UnbanDriverRequest, resp, &wg, wk)
	}
	wg.Wait()

	apiutil.OK(ctx, resp)
}

func (api *DriverAdminAPI) unbanDriverInBackground(ctx context.Context, drv model.Driver, req UnbanDriverRequest, resp *BulkIDsResponse, wg *sync.WaitGroup, wk *safe.Worker) {
	fn := func() {
		defer wg.Done()
		if err := api.doUnbanDriver(ctx, &drv, req); err != nil {
			resp.AddFailure(drv.DriverID)
			logrus.Errorf("cannot unban driver: driverId=%s err=%v", drv.DriverID, err)
		} else {
			resp.AddSuccess(drv.DriverID)
		}
	}
	wk.GoFuncWithPool(fn)
}

func (api *DriverAdminAPI) BulkUpdateDriverTier(ctx *gin.Context) {
	var req BulkUpdateDriverTierRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	admin := auth.GetAdminEmailFromGctx(ctx)
	maxSize := api.Cfg.BulkUpdateDriverTierMaxCSVRows
	rows, err := req.GetTierDataRows(maxSize)
	if err != nil {
		if validateErr, ok := err.(ValidateErr); ok {
			apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, validateErr))
		} else {
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		}
		return
	}

	reqCtx := ctx.Request.Context()

	resp := &BulkIDsResponse{}
	wg := &sync.WaitGroup{}
	wk, release := safe.NewWorker(api.Cfg.BulkUpdateDriverTierWorker)
	defer release()
	batchSize := api.Cfg.BulkUpdateDriverTierBatchSize
	round := int(math.Ceil(float64(len(rows)) / float64(batchSize)))

	wg.Add(round)
	for i := 0; i < len(rows); i += batchSize {
		x := i + batchSize
		if x > len(rows) {
			x = len(rows)
		}
		data := getTierAndNegativeGroup(rows, i, x)
		api.updateDriverTierAndNegativeGroupInBackground(reqCtx, data, resp, wg, wk, admin)
	}
	wg.Wait()

	resp.Failures = types.NewStringSet(resp.Failures...).GetElements()
	resp.Successes = types.NewStringSet(resp.Successes...).GetElements()
	apiutil.OK(ctx, resp)
}

func constructRequestPayload(c BulkUpdateARCRCommand, req model.StatsResetEventPayload, svc model.Service, count *int) model.StatsResetEventPayload {
	switch c {
	case AR_100_CR_0:
		// ar 100
		req.AutoAssignedAccepted = count
		req.AutoAssigned = count
		req.AutoAssignedRain = types.NewInt(0)
		req.AutoAssignedAcceptedRain = types.NewInt(0)

		// cr 0
		req.CancelledNotFree = types.NewInt(0)
	case AR_100:
		req.AutoAssignedAccepted = count
		req.AutoAssigned = count
		req.AutoAssignedRain = types.NewInt(0)
		req.AutoAssignedAcceptedRain = types.NewInt(0)
	case CR_0:
		req.CancelledNotFree = types.NewInt(0)
	}
	req.Service = svc
	return req
}

func (api *DriverAdminAPI) requestResetARCR(ctx *gin.Context, s model.Service, mm model.MapDriverAndDateWithAutoAssignedRecords, v BulkUpdateDriverARCRRow) error {
	user, _ := auth.GetAdminUserFromGctx(ctx)
	req := model.StatsResetEventPayload{
		DriverID:    v.DriverId,
		Date:        v.TargetDate,
		RequestedBy: user.GetEmail(),
		Upsert:      true, // will create table in Kafka if daily count of requested date is missing
	}

	driverAutoAssignedCount, exists := mm.Get(v.TargetDate, v.DriverId, s.String())
	if !exists {
		if v.Command == AR_100_CR_0 || v.Command == AR_100 {
			return findAutoAssignCountErr
		}
	}
	request := constructRequestPayload(v.Command, req, s, driverAutoAssignedCount.AutoAssignedCompletedCount)
	err := api.doPublishResetEvent(ctx, request)
	if err != nil {
		return err
	}
	return nil
}

func (api *DriverAdminAPI) doPublishResetEvent(ctx context.Context, req model.StatsResetEventPayload) error {
	payload, err := json.Marshal(req)
	if err != nil {
		err = fmt.Errorf("unable to marshalling payload: %v", err)
		return err
	}
	orderModel := event.DriverEventOrderModel{
		Event:     string(event.EventStatsReset),
		Payload:   payload,
		EventTime: timeutil.BangkokNow(),
	}
	message, err := json.Marshal(orderModel)
	if err != nil {
		return fmt.Errorf("unable to marshalling event request: %v", err)
	}
	err = api.Bus.Publish(ctx, order.BusTopicDriverEventOrder, message)
	if err != nil {
		return fmt.Errorf("unable to publish event request: %v", err)
	}
	return nil
}

func (api *DriverAdminAPI) BulkUpdateDriverARCR(ctx *gin.Context) {
	var req BulkUpdateDriverARCRRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	_, exist := auth.GetAdminUserFromGctx(ctx)
	if !exist {
		err := fmt.Errorf("required user authorization")
		apiutil.ErrUnauthorized(ctx, apiutil.NewFromError(absintheapi.ERRCODE_UNAUTHORIZED, err))
		return
	}

	maxSize := api.Cfg.BulkUpdateDriverARCRMaxCSVRows
	rows, err := req.GetDataRows(maxSize)
	if err != nil {
		if validateErr, ok := err.(ValidateErr); ok {
			apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, validateErr))
		} else {
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		}
		return
	}

	resp := &BulkUpdateDriverARCRResponse{
		Successes: []BulkUpdateDriverARCRSuccess{},
		Failures:  []BulkUpdateDriverARCRFailure{},
	}

	m := model.MapDateCountAutoAssignedRecords{}
	for date, v := range rows.groupByDate() {
		drivers := v.uniqueDrivers()
		start := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, timeutil.BangkokLocation())
		end := start.AddDate(0, 0, 1)

		var (
			r   model.CountDriverAutoAssignedRecords
			err error
		)
		if api.FeatureFlagService.IsEnabled(ctx, featureflag.ReviseBulkARCR.Name) {
			r, err = api.OrderRepo.FindDriverOrderAutoAssigned(ctx, drivers, start, end, repository.WithReadSecondaryPreferred)
		} else {
			r, err = api.OrderRepo.FindDriverOrderAutoAssignedCompleted(ctx, drivers, start, end, repository.WithReadSecondaryPreferred)
		}
		if err != nil {
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}

		dMap := r.GroupByDriverID()
		m[date] = r.FillDriverMissingServiceType(dMap)
	}
	mm := m.MapDriverAndDateWithAutoAssignedRecords()

	for _, v := range rows {
		applyAllService := v.Service == ""

		if !applyAllService {
			err := api.requestResetARCR(ctx, v.Service, mm, v)
			if err != nil {
				resp.AddFailure(v.RowIdx, v.DriverId, err.Error())
				continue
			}
		} else {
			for _, s := range model.CurrentSupportServices {
				err := api.requestResetARCR(ctx, s, mm, v)
				if err != nil {
					resp.AddFailure(v.RowIdx, v.DriverId, err.Error())
					break
				}
			}
		}

		if !resp.HasFailure() {
			resp.AddSuccess(v.RowIdx, v.DriverId, v.TargetDate)
		}
	}

	apiutil.OK(ctx, resp)
}

func (api *DriverAdminAPI) BulkUpdateDriverDeprioritization(gctx *gin.Context) {
	var req BulkUpdateDriverDeprioritization
	if err := gctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	csvDisables, csvEnables, err := req.GetDriverDeprioritization(api.Cfg.BulkUpdateDriverDeprioritizationMaxCSVRows)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	// csvDriverIDs contains all driver IDs in the CSV
	csvDriverIDs := types.NewSetFrom(csvDisables.Slice()...)
	csvDriverIDs.Add(utils.CollectKeys(csvEnables)...)

	valids, invalids, err := api.DriverRepo.ValidateDriverIDs(gctx, csvDriverIDs.Slice(), repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	resp := new(BulkUpdateDriverDeprioritizationResponse)

	// Remove invalids driver IDs from our data
	if len(invalids) != 0 {
		resp.AddManyFailure(invalids, "INVALID_DRIVER_IDS")
		csvDriverIDs.Remove(invalids...)

		csvDisables.Remove(invalids...)
		utils.DeleteKeys(csvEnables, invalids...)
	}

	if !csvDriverIDs.ContainsExact(valids...) {
		logrus.Error("unexpected set bug: csvDriverIDs differ from valids after removing invalids", map[string]interface{}{
			"valids":       valids,
			"csvDriverIDs": csvDriverIDs.Slice(),
		})

		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, errors.New("set bug")))
		return
	}

	ctx := gctx.Request.Context()
	n, err := api.DriverRepo.BulkUpdateDeprioritization(ctx, csvDisables.Slice(), csvEnables)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	lenNormalized := len(csvDisables)
	lenDeprioritized := len(csvEnables)

	if expected := lenNormalized + lenDeprioritized; expected != n {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, fmt.Errorf("unexpected number of updated documents: expecting %d, got %d", expected, n)))
		return
	}

	resp.Count.Normalized = lenNormalized
	resp.Count.Deprioritized = lenDeprioritized

	for id := range csvEnables {
		resp.AddSuccess(id)
	}

	resp.AddManySuccess(csvDisables.Slice())

	apiutil.OK(gctx, resp)
}

func (a *DriverAdminAPI) BulkUpdateSupplyPos(gctx *gin.Context) {
	const (
		maxRows       int           = 100000          // CSV rows
		chunkSize     int           = 20000           // CSV rows per chunk
		maxWaitSecond time.Duration = 3 * time.Second // Max seconds to be spent waiting, ie if we have 2 chunks with 3 max seconds, then each chunk waits 1.5s
	)

	// I don't use BulkIDsResponse because I need failure reasons
	var req reqBulkUpdateSupplyPos

	err := gctx.ShouldBindWith(&req, binding.FormMultipart)
	if err != nil {
		err = errors.Wrapf(err, "parse body error")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	driverIDs, err := req.driverIDs(maxRows)
	if err != nil {
		err = errors.Wrapf(err, "parse csv error")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	// Don't use API context
	// Because the operation might take some time, using non-request context allows
	// the handler to continue operation even if the connection was severed
	ctx := context.Background()

	if len(driverIDs) == 0 {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromString(absintheapi.ERRCODE_INVALID_REQUEST, "empty driver IDs"))
		return
	}

	result := NewResult[string, ErrGroupIDs]()
	duplicates := utils.DuplicatesSet(driverIDs...)
	if len(duplicates) != 0 {
		result.AddFailure(ErrGroupIDs{
			Err: "duplicate driver ids",
			IDs: duplicates.Slice(),
		})
	}

	setDriverIDs := types.NewSetFrom(driverIDs...)
	chunksIDs := utils.Chunks(setDriverIDs.Slice(), chunkSize, maxRows)

	var countFailedChunks int
	invalidIDs := types.NewSet[string]()
	for _, chunkIDs := range chunksIDs {
		// ValidateDriverIDs will ONLY return error IF ALL chunkIDs are invalid.
		// otherwise it returns nil error with invalid IDs.
		_, invalids, err := a.DriverRepo.ValidateDriverIDs(ctx, chunkIDs)
		if err != nil {
			if len(invalids) != 0 {
				panic("unexpected error with non-nil invalids")
			}

			countFailedChunks++
			invalidIDs.Add(chunkIDs...)
			setDriverIDs.Remove(chunkIDs...)
			continue
		}

		// Nil error and non-nil invalids means some of chunkIDs are invalid
		if len(invalids) != 0 {
			invalidIDs.Add(invalids...)
			setDriverIDs.Remove(invalids...)
		}
	}

	if invalidIDs.Len() != 0 {
		result.AddFailure(ErrGroupIDs{
			Err: "invalid driver ids",
			IDs: invalidIDs.Slice(),
		})
	}

	// All driver IDs are invalid, abort
	if countFailedChunks == len(chunksIDs) {
		apiutil.OK(gctx, result)
		return
	}

	// Recompute chunks after invalid IDs were removed
	chunksIDs = utils.Chunks(setDriverIDs.Slice(), chunkSize, maxRows)
	sleepEachChunk := utils.SleepEachChunk(maxWaitSecond, len(chunksIDs))

	err = a.DriverRepo.GlobalToggleDisableSupplyPos(ctx)
	if err != nil {
		err = errors.Wrapf(err, "error disabling supply positioning globally")
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	for _, chunkIDs := range chunksIDs {
		err = a.DriverRepo.MultipleEnableSupplyPositioning(ctx, chunkIDs)
		if err != nil {
			result.AddFailures(ErrGroupIDs{
				Err: err.Error(),
				IDs: chunkIDs,
			})

			continue
		}

		result.AddSuccesses(chunkIDs...)
		time.Sleep(sleepEachChunk)
	}

	apiutil.OK(gctx, result)
}

func (a *DriverAdminAPI) ExportCSVSupplyPositioning(gctx *gin.Context) {
	driverIDs, err := a.DriverRepo.FindDriverIDsSupplyPositioning(gctx.Request.Context())
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	responseWriter := NewCSVDriverIDsResponseWriter(driverIDs)
	responseWriter.Write(gctx)
}

func (api *DriverAdminAPI) ListDriverTiers(ctx *gin.Context) {
	driverTiers := model.DriverTierSet
	apiutil.OKList(ctx, driverTiers, len(driverTiers))
}

func (api *DriverAdminAPI) updateDriverTierAndNegativeGroupInBackground(ctx context.Context, updateData []DriverTierAndNegativeGroup, resp *BulkIDsResponse, wg *sync.WaitGroup, wk *safe.Worker, admin string) {
	fn := func() {
		defer wg.Done()

		successIDs, failureIDs := api.updateManyDriverTier(ctx, updateData, admin)
		resp.AddManySuccess(successIDs)
		resp.AddManyFailures(failureIDs)

		successIDs, failureIDs = api.updateManyDriverNegativeGroup(ctx, updateData, admin)
		resp.AddManySuccess(successIDs)
		resp.AddManyFailures(failureIDs)
	}

	wk.GoFuncWithPool(fn)
}

func (api *DriverAdminAPI) updateManyDriverTier(ctx context.Context, updateData []DriverTierAndNegativeGroup, admin string) ([]string, []string) {
	var success []string
	var fail []string
	driverTiers := make(map[string][]string)

	for i := range updateData {
		newTierDisaplyName := updateData[i].Tier
		driverID := updateData[i].DriverID

		if newTierDisaplyName.ToString() == "" {
			continue
		}

		if !newTierDisaplyName.IsValid() {
			logx.Error().
				Context(ctx).
				Err(fmt.Errorf("invalid tier %v", newTierDisaplyName)).
				Str("driverID", driverID)

			fail = append(fail, driverID)
			continue
		}

		// Convert to DriverTier
		newDriverTier := updateData[i].Tier.ToDriverTier()

		driverTiers[newDriverTier.ToString()] = append(driverTiers[newDriverTier.ToString()], driverID)
	}

	for tier, driverIDs := range driverTiers {
		audTiers := api.getAuditLogUpdateDriverTier(ctx, driverIDs, tier)
		err := api.DriverRepo.UpdateManyByTier(ctx, driverIDs, tier)
		if err != nil {
			fail = append(fail, driverIDs...)
			logx.Error().
				Context(ctx).
				Err(err).
				Msg("fail to updateManyByTier mongo")

		} else {
			success = append(success, driverIDs...)
			api.insertAuditLogUpdateDriverTier(ctx, audTiers, tier, admin)
		}
	}

	return success, fail
}

func (api *DriverAdminAPI) getAuditLogNegativeGroup(ctx context.Context, driverIDs []string, newGroup string) map[string]auditLogDriverNegativeGroup {
	groups := make(map[string]auditLogDriverNegativeGroup)

	q := persistence.BuildDriverQuery().WithDriverIDs(driverIDs)
	sel := []string{"driver_id", "negative_balance_group"}
	sort := []string{"-driver_id"}
	drivers, err := api.DriverRepo.FindWithQuerySelectorAndSort(ctx,
		q,
		sel,
		sort,
		0, 0,
		repository.WithReadSecondaryPreferred,
	)
	if err != nil {
		logrus.Errorf("insertAuditLogUpdateTier.FindWithQuerySelectorAndSort err: %v", err)
		return groups
	}

	for _, v := range drivers {
		groups[v.DriverID] = auditLogDriverNegativeGroup{
			currentGroup: v.NegativeBalanceGroup,
			newGroup:     newGroup,
		}
	}

	return groups
}

func (api *DriverAdminAPI) getAuditLogUpdateDriverTier(ctx context.Context, driverIDs []string, newTier string) map[string]auditLogDriverTier {
	tiers := make(map[string]auditLogDriverTier)
	q := persistence.BuildDriverQuery().WithDriverIDs(driverIDs)
	sel := []string{"driver_id", "driver_tier"}
	sort := []string{"-driver_id"}
	drivers, err := api.DriverRepo.FindWithQuerySelectorAndSort(ctx,
		q,
		sel,
		sort,
		0, 0,
		repository.WithReadSecondaryPreferred,
	)
	if err != nil {
		logrus.Errorf("insertAuditLogUpdateTier.FindWithQuerySelectorAndSort err: %v", err)
		return tiers
	}

	for _, v := range drivers {
		tiers[v.DriverID] = auditLogDriverTier{
			currentTier: v.DriverTier,
			newTier:     model.DriverTier(newTier),
		}
	}

	return tiers
}

func (api *DriverAdminAPI) insertAuditLogUpdateNegativeGroup(ctx context.Context, groups map[string]auditLogDriverNegativeGroup, newGroup string, email string) {
	if len(groups) == 0 {
		return
	}

	var log []*model.AuditLog
	for driverID, v := range groups {
		log = append(log, &model.AuditLog{
			Object:    model.AuditObject{ObjectType: "driver", ID: driverID},
			Actor:     model.AuditLogActor{ID: email},
			Timestamp: timeutil.BangkokNow(),
			Event:     model.AuditEventBulkUpdateDriverNegativeBalanceGroup,
			Action:    model.UpdateAction,
			Before: model.Driver{
				NegativeBalanceGroup: v.currentGroup,
			},
			After: model.Driver{
				NegativeBalanceGroup: v.newGroup,
			},
		})
	}

	err := api.AuditLog.InsertMany(ctx, log)
	if err != nil {
		logrus.Errorf("insertAuditLogUpdateTier AuditLog.InsertMany err: %v", err)
		return
	}

	logrus.Infof("insert audit log for bulk update driver negative_balance_group name: %v success", newGroup)
}

func (api *DriverAdminAPI) insertAuditLogUpdateDriverTier(ctx context.Context, tiers map[string]auditLogDriverTier, tier string, email string) {
	logrus.Infof("inserting audit log for bulk update driver driver_tier name: %v", tier)

	if len(tiers) == 0 {
		return
	}

	var log []*model.AuditLog
	for driverID, v := range tiers {
		log = append(log, &model.AuditLog{
			Object:    model.AuditObject{ObjectType: "driver", ID: driverID},
			Actor:     model.AuditLogActor{ID: email},
			Timestamp: timeutil.BangkokNow(),
			Event:     model.AuditEventBulkUpdateDriverTier,
			Action:    "",
			Before: model.Driver{
				BaseDriver: model.BaseDriver{
					DriverTier: v.currentTier,
				},
			},
			After: model.Driver{
				BaseDriver: model.BaseDriver{
					DriverTier: v.newTier,
				},
			},
		})
	}

	err := api.AuditLog.InsertMany(ctx, log)
	if err != nil {
		logrus.Errorf("insertAuditLogUpdateTier AuditLog.InsertMany err: %v", err)
		return
	}
	logrus.Infof("insert audit log for bulk update driver driver_tier name: %v success", tier)
}

func (api *DriverAdminAPI) updateManyDriverNegativeGroup(ctx context.Context, updateData []DriverTierAndNegativeGroup, admin string) ([]string, []string) {
	var successIDs []string
	var failureIDs []string
	groups := make(map[string][]string)

	for _, v := range updateData {
		if v.NegativeGroup == "" {
			continue
		}

		// unset negative group from driver
		name := v.NegativeGroup
		if v.NegativeGroup == "CLEAR_NEG_GROUP" {
			name = ""
		}

		if _, ok := groups[name]; ok {
			groups[name] = append(groups[name], v.DriverID)
		} else {
			groups[name] = []string{v.DriverID}
		}
	}

	for name, driverIDs := range groups {
		audNeg := api.getAuditLogNegativeGroup(ctx, driverIDs, name)
		err := api.DriverRepo.UpdateManyByNegativeGroup(ctx, driverIDs, name)
		if err != nil {
			failureIDs = append(failureIDs, driverIDs...)
			logrus.Errorf("update driver negative group error: err=%v", err)
		} else {
			successIDs = append(successIDs, driverIDs...)
			api.insertAuditLogUpdateNegativeGroup(ctx, audNeg, name, admin)
		}
	}

	return successIDs, failureIDs
}

func (api *DriverAdminAPI) doUnbanDriver(ctx context.Context, driv *model.Driver, req UnbanDriverRequest) error {
	if err := api.BanService.Unban(ctx, driv, func() (model.BanHistory, error) {
		var unbanImageRefURL string
		if req.UnbanImageRef != nil {
			uir := model.UnbanImageReference{
				File:        req.UnbanImageRef,
				Name:        model.UnbanImageRefPrefixName,
				ContentType: file.GetContentType(req.UnbanImageRef.Header),
			}

			f, err := uir.File.Open()
			if err != nil {
				return model.BanHistory{}, err
			}
			defer f.Close()

			prefix := fmt.Sprintf("%s/%s", driv.DriverID, uir.Name)
			uploadedFile, err := api.VosService.UploadTOVOSForInternal(ctx, prefix, uir.ContentType, f, service.UnbanFilePath)
			if err != nil {
				return model.BanHistory{}, err
			}

			if uploadedFile != nil {
				unbanImageRefURL = uploadedFile.ID()
			}
		}

		return banHistoryFromUnbanDriverRequest(driv.DriverID, req, unbanImageRefURL), nil
	}); err != nil {
		return err
	}

	return nil
}

func (api *DriverAdminAPI) listDriverCSV(gctx *gin.Context, query repository.DriverQuery) {
	drivers, err := api.DriverRepo.FindWithQueryAndSort(gctx.Request.Context(), query, []string{"-created_at"}, 0, api.Cfg.MaxExportedCriminalCheckStatusCSVRows)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	responseWriter := NewCSVDriverResponseWriter(drivers)
	responseWriter.Write(gctx)
}

func (api *DriverAdminAPI) listDriverJSON(gctx *gin.Context, query repository.DriverQuery) {
	skip, size := utils.ParsePagination(gctx)
	drivers, err := api.DriverRepo.FindWithQueryAndSort(gctx.Request.Context(), query, []string{"-created_at"}, skip, size, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if drivers == nil {
		drivers = []model.Driver{}
	}

	responseWriter := NewJsonDriverResponseWriter(drivers)
	responseWriter.Write(gctx)
}

// ListDrivers list driver with query
func (api *DriverAdminAPI) ListDrivers(gctx *gin.Context) {
	var req ListDriverRequest

	if err := gctx.ShouldBindQuery(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	if err := gctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	query := req.Query()
	if query.IsEmpty() {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(errors.New("กรุณาเลือกอย่างน้อย 1 เงื่อนไขในการค้นหาข้อมูล")))
		return
	}

	api.listDriverJSON(gctx, query)
}

func (api *DriverAdminAPI) ExportCSVDrivers(gctx *gin.Context) {
	var req ListDriverRequest

	if err := gctx.ShouldBindQuery(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	query := req.Query()
	if query.IsEmpty() {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(errors.New("กรุณาเลือกอย่างน้อย 1 เงื่อนไขในการค้นหาข้อมูล")))
		return
	}

	api.listDriverCSV(gctx, query)
}

func (api *DriverAdminAPI) BulkUpdateServiceTypes(ctx *gin.Context) {
	resp := &BulkIDReasonResponse{}

	exists := api.getOptimizedDriverExistsFn(ctx)

	driverIDs, m, effectiveAt, err := NewBulkUpdateServiceTypes(ctx, api.Cfg.BulkUpdateServiceTypeMaxCSVRows, resp, exists)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(err))
		return
	}

	user, exist := auth.GetAdminUserFromGctx(ctx)
	if !exist {
		apiutil.ErrUnauthorized(ctx, apiutil.NewFromString(absintheapi.ERRCODE_UNAUTHORIZED, "required user authorization"))
		return
	}

	drivers, err := api.DriverRepo.FindDriverIDs(ctx, driverIDs)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiError.ErrInternal(err))
		return
	}

	validDrivers := make([]model.Driver, 0, len(driverIDs))
	for _, drv := range drivers {
		if drv.IsFullTimeDriver() &&
			!fp.SliceMustContains(
				driver.ServiceTypesForFullTimeDriver,
				m[drv.DriverID],
			) {
			resp.AddFailure(drv.DriverID, "invalid service type(s) for a full-time driver")

			continue
		}

		validDrivers = append(validDrivers, drv)
	}

	// if operation provides effective at, schedule it
	if !effectiveAt.IsZero() {
		info := bulkprocess.CreateBulkProcessInfo(effectiveAt, user.GetEmail())
		payload := map[string]model.DriverServiceInfo{}
		for _, drv := range validDrivers {
			services := m[drv.DriverID]
			payload[drv.DriverID] = model.DriverServiceInfo{
				Status:   model.BulkProcessInfoPayloadStatusPending,
				Services: services,
			}
		}
		info.SetBulkDriverServiceType(payload)

		if err := api.BulkProcessInfoRepo.Create(ctx, &info); err != nil {
			apiutil.ErrInternalError(ctx, apiError.ErrInternal(err))
			return
		}

		noti := slack.NewTaskScheduledSlackBuilder(string(model.BulkProcessDriverServiceType), effectiveAt, driverIDs, api.BulkConfig.SlackNotificationDriverLimit).
			SetWebhook(slack.WebhookFleetBulkSchedulerMonitoring).
			AddEnvDisplayOption(api.GlobalConfig.EnvName).Build()
		if err := api.slack.Notify(ctx, noti); err != nil {
			logrus.Errorf("unable to notify: %v", err)
		}

		resp.AddManySuccess(
			fp.MapSlice(
				func(drv model.Driver) string { return drv.DriverID },
				validDrivers),
		)
		apiutil.OK(ctx, resp)
		return
	}

	wg := sync.WaitGroup{}
	wg.Add(len(validDrivers))
	wk, release := safe.NewWorker(api.Cfg.BulkUpdateServiceTypeWorker)
	defer release()
	for _, drv := range validDrivers {
		api.updateServiceTypeInBackground(ctx, drv, m[drv.DriverID], resp, &wg, wk, user.GetEmail())
	}
	wg.Wait()

	apiutil.OK(ctx, resp)
}

func (api *DriverAdminAPI) updateServiceTypeInBackground(ctx context.Context, driverBefore model.Driver, service []model.Service, resp *BulkIDReasonResponse, wg *sync.WaitGroup, wk *safe.Worker, actor string) {
	fn := func() {
		defer wg.Done()

		if err := api.DriverRepo.UpdateServiceTypes(ctx, driverBefore.DriverID, service); err != nil {
			logx.Error().Err(err).Str("driver_id", driverBefore.DriverID).Msgf("update service type in background[update service types]")
			resp.AddFailure(driverBefore.DriverID, err.Error())
			return
		} else {
			logx.Info().Str("driverID", driverBefore.DriverID).Interface("service", service).Msg("updateServiceTypeInBackground success")
			resp.AddSuccess(driverBefore.DriverID)
		}

		after, err := api.DriverRepo.FindDriverID(ctx, driverBefore.DriverID)
		if err != nil {
			logx.Error().Err(err).Str("driver_id", driverBefore.DriverID).Msg("update service type in background[find driver id after]")
		}

		auditLog := model.AuditLog{
			Actor: model.AuditLogActor{
				ID: actor,
			},
			Object: model.AuditObject{
				ObjectType: model.DriverObject,
				ID:         driverBefore.DriverID,
			},
			Action:    model.UpdateAction,
			Event:     model.AuditEventBulkUpdateCSVDriverServiceType,
			Before:    driverBefore,
			After:     after,
			Timestamp: time.Now(),
		}

		if err := api.AuditLog.Insert(ctx, &auditLog); err != nil {
			logx.Error().Err(err).Str("driver_id", driverBefore.DriverID).Msg("unable to insert audit log")
		}
	}
	wk.GoFuncWithPool(fn)
}

func (api *DriverAdminAPI) BulkUpdateDriverDedicatedZones(ctx *gin.Context) {
	user, exist := auth.GetAdminUserFromGctx(ctx)
	if !exist {
		apiutil.ErrUnauthorized(ctx, apiutil.NewFromString(absintheapi.ERRCODE_UNAUTHORIZED, "required user authorization"))
		return
	}

	driverIDs, driverZonesMap, effectiveAt, err := NewBulkUpdateDriverDedicatedZones(ctx, api.Cfg.BulkUpdateDriverDedicatedZonesMaxCSVRows)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(err))
		return
	}

	if err := api.validateBulkDriverDedicatedZones(ctx, driverZonesMap); err != nil {
		switch e := err.(type) {
		case *ValidateErr:
			apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(e))
			return
		default:
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}
	}

	resp := &BulkIDReasonResponse{}
	wg := sync.WaitGroup{}
	wk, release := safe.NewWorker(api.Cfg.BulkUpdateDriverDedicatedZonesWorker)
	defer release()

	// if operation provides effective at, schedule it
	if !effectiveAt.IsZero() {
		validDriverIDs, invalidDriverIDs, err := api.DriverRepo.ValidateDriverIDs(ctx, driverIDs, repository.WithReadSecondaryPreferred)
		if err != nil {
			// if there is some validation error, stamp failure for all driver ids
			resp.AddManyFailure(driverIDs, err.Error())
			logrus.Errorf("cannot validate driver ids, driverIDs=%v, err=%v", driverIDs, err)
			apiutil.OK(ctx, resp)
			return
		}

		if len(invalidDriverIDs) > 0 {
			resp.AddManyFailure(invalidDriverIDs, "INVALID_DRIVER_ID")
		}

		if len(validDriverIDs) > 0 {
			info := bulkprocess.CreateBulkProcessInfo(effectiveAt, user.GetEmail())
			payload := map[string]model.DriverZoneInfo{}
			for rawZone, drivers := range driverZonesMap {
				driversSet := types.NewStringSet(drivers...)
				driversSet.Remove(invalidDriverIDs...)

				payload[rawZone] = model.DriverZoneInfo{
					Status:  model.BulkProcessInfoPayloadStatusPending,
					Drivers: driversSet.GetElements(),
				}
			}
			info.SetBulkDriverDedicatedZone(payload)

			if err := api.BulkProcessInfoRepo.Create(ctx, &info); err != nil {
				apiutil.ErrInternalError(ctx, apiError.ErrInternal(err))
				return
			}

			noti := slack.NewTaskScheduledSlackBuilder(string(model.BulkProcessDriverDedicatedZone), effectiveAt, driverIDs, api.BulkConfig.SlackNotificationDriverLimit).
				SetWebhook(slack.WebhookFleetBulkSchedulerMonitoring).
				AddEnvDisplayOption(api.GlobalConfig.EnvName).Build()
			if err := api.slack.Notify(ctx, noti); err != nil {
				logrus.Errorf("unable to notify: %v", err)
			}

			resp.AddManySuccess(validDriverIDs)
		}

		apiutil.OK(ctx, resp)
		return
	}

	for rawZoneLabels, driverIDs := range driverZonesMap {
		var zoneLabels []string
		if rawZoneLabels != "" {
			for _, label := range strings.Split(rawZoneLabels, "|") {
				zoneLabels = append(zoneLabels, label)
			}
		}

		batchSize := api.Cfg.BulkUpdateDriverDedicatedZonesBatchSize
		for start := 0; start < len(driverIDs); start += batchSize {
			end := start + batchSize
			if end > len(driverIDs) {
				end = len(driverIDs)
			}
			wg.Add(1)
			api.updateDriverDedicatedZonesInBackground(ctx, driverIDs[start:end], zoneLabels, resp, &wg, wk)
			time.Sleep(api.Cfg.BulkUpdateDriverDedicatedZonesDelayTime)
		}

	}
	wg.Wait()

	apiutil.OK(ctx, resp)
}

// validateBulkDriverDedicatedZones to check dedicated_zone by label is really exists
func (api *DriverAdminAPI) validateBulkDriverDedicatedZones(ctx context.Context, driverZonesMap map[string][]string) error {
	zoneLabelSet := types.NewStringSet()
	for rawZoneLabels := range driverZonesMap {
		if rawZoneLabels != "" {
			zoneLabelSet.Add(strings.Split(rawZoneLabels, "|")...)
		}
	}

	count, err := api.DedicatedZoneRepo.CountByLabels(ctx, zoneLabelSet.GetElements())
	if err != nil {
		return err
	}
	if count != zoneLabelSet.Count() {
		return errors.New("some zone label isn't exist")
	}
	return nil
}

func (api *DriverAdminAPI) updateDriverDedicatedZonesInBackground(ctx context.Context, driverIDs []string, zoneLabels []string, resp *BulkIDReasonResponse, wg *sync.WaitGroup, wk *safe.Worker) {
	fn := func() {
		defer wg.Done()

		validDriverIDs, invalidDriverIDs, err := api.DriverRepo.ValidateDriverIDs(ctx, driverIDs, repository.WithReadSecondaryPreferred)
		if err != nil {
			// if there is some validation error, stamp failure for all driver ids
			resp.AddManyFailure(driverIDs, err.Error())
			logrus.Errorf("cannot validate driver ids, driverIDs=%v, err=%v", driverIDs, err)
			return
		}

		resp.AddManyFailure(invalidDriverIDs, "INVALID_DRIVER_ID")

		if err := api.DriverRepo.UpdateManyDedicatedZoneLabels(ctx, validDriverIDs, zoneLabels); err != nil {
			resp.AddManyFailure(validDriverIDs, err.Error())
			logrus.Errorf("updateDriverDedicatedZonesInBackground err: driverIDs=%s err=%v", validDriverIDs, err)
			return
		}

		resp.AddManySuccess(validDriverIDs)
		logrus.Infof("updateDriverDedicatedZonesInBackground success: driverIDs=%s zoneLabels=%+v", validDriverIDs, zoneLabels)
	}

	wk.GoFuncWithPool(fn)
}

// SetHaveBoxStatus set options haveBox to multiple drivers
func (api *DriverAdminAPI) SetHaveBoxStatus(ctx *gin.Context) {
	var req SetHaveBoxRequst
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(ctx)
		return
	}

	err := api.DriverRepo.MultipleUpdateHaveBox(ctx.Request.Context(), req.DriverIDs, req.HaveBox)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.NoContent(ctx)
	return
}

// BulkAssignReviewer set reviewer to multiple drivers
func (api *DriverAdminAPI) BulkAssignReviewer(ctx *gin.Context) {
	var req BulkAssignReviewerRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(ctx)
		return
	}

	if len(req.DriverIDs) > 100 {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, errors.New("Bulk assign reviewer limit exceeds. allowed maximum drivers is 100.")))
		return
	}

	drivers, err := api.DriverRepo.FindDriverIDs(ctx.Request.Context(), req.DriverIDs)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	var wg sync.WaitGroup
	wg.Add(len(drivers))
	errorDriverIDs := types.NewStringList(make([]string, 0, len(drivers)))
	for _, item := range drivers {
		api.assignReviewerInBackground(ctx, item, req.Reviewer, errorDriverIDs, &wg)
	}
	wg.Wait()
	if len(errorDriverIDs.Get()) > 0 {
		logrus.Warnf("Assign reviewer to drivers error! %s", errorDriverIDs.Get())
	}

	apiutil.NoContent(ctx)
	return
}

func (api *DriverAdminAPI) assignReviewerInBackground(ctx context.Context, d model.Driver, reviewer string, errorDriverIDs *types.StringList, wg *sync.WaitGroup) {
	safe.GoFunc(func() {
		defer wg.Done()
		d.AssignedReviewer = reviewer
		err := api.DriverRepo.Update(ctx, &d)
		if err != nil {
			logrus.Errorf("driver=%s reviewer=%s Cannot update assignedReviewer: %v", d.DriverID, reviewer, err)
			errorDriverIDs.Add(d.DriverID)
		}
	})
}

func (api *DriverAdminAPI) ReactivateDriver(gctx *gin.Context) {
	var req ReactivateDriverReq
	driverID := gctx.Param("driver_id")
	ctx := gctx.Request.Context()

	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	drv, err := api.DriverRepo.GetProfile(ctx, driverID)
	if err != nil {
		apiutil.ErrNotFound(gctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_FOUND, err))
		return
	}

	if drv.Status != model.StatusDeactivated {
		err = errors.New("driver status is not DEACTIVATED")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	{
		// `LineUID` can be eiter LINE MID or LINE UID
		if api.DriverRegistrationRepo.IsExistsByLineUid(ctx, drv.LineUID) {
			err = errors.New("registration already exists.")
			apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
			return
		}
	}

	if err = api.doReactivateDriver(req, ctx, drv); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *DriverAdminAPI) GetStatistic(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")
	ctx := gctx.Request.Context()

	driverOrderInfo, err := api.DriverOrderInfoRepo.GetOrCreate(ctx, driverID)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	assignedOrder, err := api.AssignmentLogRepository.CountAssignmentLogByDriverID(ctx, driverID, 4*time.Hour, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	lastAttempt, err := api.OrderRepo.GetDriverLastAttempt(ctx, driverID)
	if err != nil {
		logrus.Warn("cannot get driver last attempt")
		apiutil.OK(gctx, NewStatisticResponse(assignedOrder, driverOrderInfo, &model.LastAttempt{}))
		return
	}

	apiutil.OK(gctx, NewStatisticResponse(assignedOrder, driverOrderInfo, lastAttempt))
}

func (api *DriverAdminAPI) doReactivateDriver(req ReactivateDriverReq, ctx context.Context, drv *model.Driver) error {
	const reason = "reactivate driver"

	if err := api.BanService.BanAndSaveHistory(ctx, drv, model.BanInfo{
		Action:    driver.ACTION_ADMIN_BAN_TAKEORDER,
		Reason:    reason,
		Type:      model.BanTypePermanent,
		Until:     time.Time{},
		CreatedBy: req.Requester,
	}); err != nil {
		return err
	}

	drv.Remarks = append(drv.Remarks, &model.DriverRemark{
		Remark:    fmt.Sprintf("Reactivate Driver, Change driver status to %s", model.StatusBanned),
		CreatedBy: req.Requester,
		CreatedAt: time.Now(),
	})

	if err := api.DriverRepo.Update(ctx, drv); err != nil {
		logrus.Errorf("cannot update driver remark: driverId=%s err=%v", drv.DriverID, err)
	}

	if err := api.DriverService.AssignUobRefToDriver(ctx, drv); err != nil {
		logrus.Warnf("doReactivateDriver, cannot assign uobRef for driverID: %v err: %v", drv.DriverID, err)
	}

	return nil
}

// ApproveRequestUpdateProfile approve request update driver profile
func (api *DriverAdminAPI) ApproveRequestUpdateProfile(gctx *gin.Context) {
	var req ApproveRequestUpdateProfileRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	ctx := gctx.Request.Context()
	driverId := gctx.Param("driver_id")

	profile, err := api.DriverRepo.GetProfile(ctx, driverId)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_FOUND, err))
		} else {
			apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		}
		return
	}

	if err := profile.ApproveRequestUpdateProfile(); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	profile.Remarks = append(profile.Remarks, &model.DriverRemark{
		Remark:    fmt.Sprintf("Approved Request Profile Update"),
		CreatedBy: req.Requester,
		CreatedAt: time.Now(),
	})

	if err := api.DriverRepo.Update(ctx, profile); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.NoContent(gctx)
}

// RejectRequestUpdateProfile reject request update driver profile and remark what the document driver need to fix
func (api *DriverAdminAPI) RejectRequestUpdateProfile(gctx *gin.Context) {
	var req RejectRequestUpdateProfileRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	ctx := gctx.Request.Context()
	driverId := gctx.Param("driver_id")

	profile, err := api.DriverRepo.GetProfile(ctx, driverId)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_FOUND, err))
		} else {
			apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		}
		return
	}

	if err := profile.RejectRequestUpdateProfile(req.Remark); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	profile.Remarks = append(profile.Remarks, &model.DriverRemark{
		Remark:    fmt.Sprintf("Rejected Request Profile Update"),
		CreatedBy: req.Requester,
		CreatedAt: time.Now(),
	})

	if err := api.DriverRepo.Update(ctx, profile); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *DriverAdminAPI) removeShift(ctx context.Context, driverID string, banUntil time.Time) error {
	start := timeutil.BangkokNow()
	end := banUntil
	sID, err := api.ShiftService.RemoveShiftByTime(ctx, driverID, start, end)
	if err != nil {
		return err
	}

	logrus.Infof("driver id %v remove shift id %v", driverID, sID)
	return nil
}

func (api *DriverAdminAPI) GetARCRStatistic(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")
	date := gctx.Query("date")
	ctx := gctx.Request.Context()

	d, err := time.Parse("02/01/2006", date)
	if err != nil {
		err = fmt.Errorf("unable to parse `date` query param: %v", err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	dpr := account.DriverPerformanceReq{
		Granularity: account.DAILY,
		Date:        d,
		Types:       []account.PerformanceType{account.AR, account.CR},
	}

	endDate, err := dpr.GetEndDate()
	if err != nil {
		apiutil.ErrBadRequest(gctx, utils.ErrorStructResponse(err))
		return
	}

	startDate := dpr.GetStartDate()

	doi, err := api.DriverOrderInfoRepo.GetDailyCounts(ctx, driverID, startDate, endDate)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			err := fmt.Errorf("driver [%s] not found in date between [%v] and [%v]): %v", driverID, startDate, endDate, err)
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_FOUND, err))
			return
		}
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if dailyCount := doi.FindDailyCountByDate(d.Month(), d.Year(), d.Day()); dailyCount == nil {
		err := fmt.Errorf("driver [%s] doesn't has daily count for date [%v])", driverID, d.Format("02/01/2006"))
		apiutil.ErrNotFound(gctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_FOUND, err))
		return
	}

	sumDailyCount, currentAR, arByServices := doi.GetAR(startDate, endDate)

	arResponse := account.NewARPerformanceResponse(sumDailyCount, 0, currentAR, arByServices)

	sumDailyCount, currentCR, crByServices := doi.GetCR(startDate, endDate)

	crResponse := account.NewCRPerformanceResponse(sumDailyCount, 0, currentCR, crByServices)

	apiutil.OK(gctx, NewARCRStatisticResponse(arResponse, crResponse))
}

func (api *DriverAdminAPI) UpdateARCRStatistic(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")

	if driverID == "" {
		err := fmt.Errorf("driver_id is required")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	var req UpdateARCRStatisticRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		err = fmt.Errorf("unable to bind json: %v", err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	date, err := time.ParseInLocation("02/01/2006", req.Date, timeutil.BangkokLocation())
	if err != nil {
		err = fmt.Errorf("unable to parse date: %v", err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if req.Service != "" && !req.Service.IsValid() {
		err = fmt.Errorf("invalid service type, service=%s", req.Service)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	user, exist := auth.GetAdminUserFromGctx(gctx)
	if !exist {
		err = fmt.Errorf("required user authorization")
		apiutil.ErrUnauthorized(gctx, apiutil.NewFromError(absintheapi.ERRCODE_UNAUTHORIZED, err))
		return
	}

	statsResetPayload := order.CreateStatsResetPayload(driverID, date, req.Service, req.AutoAssignedAccepted, req.AutoAssigned, req.CancelledNotFree, req.Accepted, user.GetEmail(), req.AutoAssignedRain, req.AutoAssignedAcceptedRain)
	payload, err := json.Marshal(statsResetPayload)
	if err != nil {
		err = fmt.Errorf("unable to marshalling statsResetPayload: %v", err)
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	orderModel := event.DriverEventOrderModel{
		Event:     string(event.EventStatsReset),
		Payload:   payload,
		EventTime: timeutil.BangkokNow(),
	}

	message, err := json.Marshal(orderModel)
	if err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	err = api.Bus.Publish(gctx, order.BusTopicDriverEventOrder, message)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func (api *DriverAdminAPI) GetDriverRequestUpdateProfileSections(gctx *gin.Context) {
	all, err := api.UpdateProfileSectionRepo.GetAll(gctx, 0, 0, repository.WithReadSecondaryPreferred)
	if err != nil {
		logx.Error().Err(err).Context(gctx).Msg("GetDriverRequestUpdateProfileSections error")
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	if api.AtomicAdminConfig == nil {
		err = errors.New("AtomicAdminConfig nil")
		logx.Error().Err(err).Context(gctx).Msg("GetDriverRequestUpdateProfileSections error")
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}
	sections := NewDriverRequestUpdateProfileSections(all, api.AtomicAdminConfig.Get().DisabledUpdateDriverProfileSection)
	apiutil.OKList(gctx, sections, len(sections))
}

func (api *DriverAdminAPI) BulkRequestUpdateProfiles(gctx *gin.Context) {
	if !api.Cfg.BulkRequsetUpdateEnabled {
		logrus.WithContext(gctx).Errorf("BulkRequestUpdateProfilesAPI is disabled")
		apiutil.ErrNotImplemented(gctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_IMPLEMENT, errors.New("not implement")))
		return
	}

	var req BulkUpdateProfileRequest
	if err := gctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	logrus.Infof("section ids %+v", req.UpdateProfileRequest.SectionIDs)

	driverIDs, err := req.GetDriverIDs()
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	requestSections := strings.Split(req.UpdateProfileRequest.SectionIDs, ",")

	logrus.Infof("request section %+v", requestSections)

	query := persistence.BuildRequestUpdateProfileSectionQuery().WithIDs(requestSections)
	sections, err := api.UpdateProfileSectionRepo.FindWithQueryAndSort(gctx.Request.Context(), query, 0, 0, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	resp := &BulkIDsResponse{}
	wg := sync.WaitGroup{}
	wg.Add(len(driverIDs))
	wk, release := safe.NewWorker(api.Cfg.BulkRequsetUpdateWorker)
	defer release()
	for _, driverID := range driverIDs {
		api.updateProfileRequestInBackground(gctx.Request.Context(), req, driverID, sections, resp, &wg, wk)
	}
	wg.Wait()
	apiutil.OK(gctx, resp)
}

func (api *DriverAdminAPI) updateProfileRequestInBackground(ctx context.Context, req BulkUpdateProfileRequest, driverID string, sections []model.DriverRequestUpdateProfileSection, resp *BulkIDsResponse, wg *sync.WaitGroup, wk *safe.Worker) {
	fn := func() {
		defer wg.Done()
		if err := api.doUpdateProfileRequest(ctx, req, driverID, sections); err != nil {
			resp.AddFailure(driverID)
			logrus.Errorf("cannot update profile request driver: driverId=%s err=%v", driverID, err)
		} else {
			resp.AddSuccess(driverID)
		}
	}
	wk.GoFuncWithPool(fn)
}

func (api *DriverAdminAPI) doUpdateProfileRequest(ctx context.Context, req BulkUpdateProfileRequest, driverID string, sections []model.DriverRequestUpdateProfileSection) error {
	_, err := api.TxnHelper.WithTxn(ctx, func(ctx context.Context) (interface{}, error) {
		driver, err := api.DriverRepo.GetProfileByIDForRequestUpdate(ctx, driverID)
		if err != nil {
			return nil, err
		}

		if driver.ProfileStatus != model.ProfileStatusCompleted {
			if driver.ProfileStatus == model.ProfileStatusUpdatePending && len(driver.RequestUpdateProfile) > 0 {
				return nil, errors.New("The driver has already requested to update the profile.")
			}

			requestSections := strings.Split(req.UpdateProfileRequest.SectionIDs, ",")
			requestUpdateDriverProfileExists, err := api.RequestUpdateProfileRepo.FindInProcessRequestByDriverIDAndSectionIDs(ctx, driver.DriverID, requestSections, repository.WithReadSecondaryPreferred)
			if err != nil {
				return nil, err
			}

			for index := range requestUpdateDriverProfileExists {
				requestUpdateDriverProfileExists[index].Status = model.RequestProfileStatusCancel
			}

			if len(requestUpdateDriverProfileExists) > 0 {
				// Update deleted status to old data
				if err := api.RequestUpdateProfileRepo.ReplaceAll(ctx, requestUpdateDriverProfileExists); err != nil {
					return nil, err
				}
			}
		}

		var requestUpdateDriverProfiles []model.RequestUpdateDriverProfile
		for _, section := range sections {
			requestUpdateDriverProfiles = append(requestUpdateDriverProfiles, model.RequestUpdateDriverProfile{
				DriverID:        driver.DriverID,
				SectionID:       section.ID.Hex(),
				SectionLabel:    section.SectionLabel,
				SectionName:     section.SectionName,
				Status:          model.RequestProfileStatusRequestUpdate,
				Fields:          section.Fields,
				MessageToDriver: req.UpdateProfileRequest.MessageToDriver,
			})
		}

		if driver.ProfileStatus != model.ProfileStatusRequestUpdate {
			if err := api.DriverRepo.UpdateProfileStatus(ctx, driver.DriverID, model.ProfileStatusRequestUpdate); err != nil {
				return nil, err
			}
		}

		if err := api.RequestUpdateProfileRepo.CreateAll(ctx, requestUpdateDriverProfiles); err != nil {
			return nil, err
		}

		return nil, nil
	}, transaction.WithLabel("DriverAdminAPI.doUpdateProfileRequest"))
	if err != nil {
		logrus.Errorf("cannot update profile request driver: driverId=%s err=%v", driverID, err)
		return err
	}

	return nil
}

// ProvideDriverOperationAdminAPI constructs DriverAdminAPI.
func ProvideDriverOperationAdminAPI(assignmentLogRepo repository.AssignmentLogRepository,
	bansvc service.BanService,
	banhistsvc repository.BanHistoryRepository,
	driverService service.DriverServiceInterface,
	driverRepo repository.DriverRepository,
	driverCancelReasonRepo repository.CancelReasonRepository,
	driverOrderInfoRepo repository.DriverOrderInfoRepository,
	orderRepository repository.OrderRepository,
	driverRegisRpo repository.DriverRegistrationRepository,
	adminConfig Config,
	slack slack.Slack,
	vosSvc service.VOSService,
	shiftService service.ShiftServices,
	bus domain.EventBus,
	updateProfileSectionRepo repository.RequestUpdateProfileSectionRepository,
	requestUpdateProfileRepo repository.RequestUpdateProfileRepository,
	txnHelper transaction.TxnHelper,
	incomeSummaryService income.IncomeSummaryService,
	dedicatedZoneRepo repository.DedicatedZoneRepository,
	banEffectiveTimeRepo repository.BanEffectiveTimeRepository,
	auditLog repository.AuditLogRepository,
	serviceAreaRepo repository.ServiceAreaRepository,
	featureFlagSvc featureflag.Service,
	bulkProcessInfoRepo repository.BulkProcessInfoRepository,
	globalConfig config.GlobalConfig,
	cancelReasonConfig *service.AtomicCancelReasonConfig,
	atomicAdminConfig *config.AtomicAdminConfig,
	bulkConfig *bulk.Config,
) *DriverAdminAPI {
	return &DriverAdminAPI{
		AssignmentLogRepository:  assignmentLogRepo,
		BanService:               bansvc,
		BanHistoryRepository:     banhistsvc,
		DriverService:            driverService,
		DriverRepo:               driverRepo,
		DriverOrderInfoRepo:      driverOrderInfoRepo,
		OrderRepo:                orderRepository,
		DriverCancelRepo:         driverCancelReasonRepo,
		DriverRegistrationRepo:   driverRegisRpo,
		Cfg:                      adminConfig,
		slack:                    slack,
		VosService:               vosSvc,
		ShiftService:             shiftService,
		Bus:                      bus,
		UpdateProfileSectionRepo: updateProfileSectionRepo,
		RequestUpdateProfileRepo: requestUpdateProfileRepo,
		TxnHelper:                txnHelper,
		IncomeSummaryService:     incomeSummaryService,
		DedicatedZoneRepo:        dedicatedZoneRepo,
		BanEffectiveTimeRepo:     banEffectiveTimeRepo,
		AuditLog:                 auditLog,
		ServiceAreaRepo:          serviceAreaRepo,
		FeatureFlagService:       featureFlagSvc,
		BulkProcessInfoRepo:      bulkProcessInfoRepo,
		GlobalConfig:             globalConfig,
		CancelReasonConfig:       cancelReasonConfig,
		AtomicAdminConfig:        atomicAdminConfig,
		BulkConfig:               bulkConfig,
	}
}

func getTierAndNegativeGroup(rows [][]string, start int, end int) []DriverTierAndNegativeGroup {
	tierData := make([]DriverTierAndNegativeGroup, 0, end-start+1)

	for _, columns := range rows[start:end] {
		driverID := strings.TrimSpace(columns[0])
		tier := model.TierDisplayName(strings.TrimSpace(columns[1]))
		negativeGroup := strings.TrimSpace(columns[2])
		tierData = append(tierData, DriverTierAndNegativeGroup{
			DriverID:      driverID,
			Tier:          tier,
			NegativeGroup: negativeGroup,
		})
	}

	return tierData
}

func banHistoryFromBanDriverRequest(d *model.Driver, req BanDriverRequest) model.BanHistory {
	reason := req.Reason
	if d.Status == model.StatusAssigned {
		reason = fmt.Sprintf("%s / Banned while status ASSIGNED", req.Reason)
	}
	return model.BanHistory{
		DriverID:        d.DriverID,
		Action:          driver.ACTION_ADMIN_BAN_TAKEORDER,
		Value:           string(model.StatusBanned),
		Reason:          reason,
		Type:            req.Type,
		Category:        req.Category,
		MessageToDriver: req.MessageToDriver,
		BannedUntil:     req.BannedUntil,
		CreatedBy:       req.CreatedBy,
	}
}

func banHistoryFromUnbanDriverRequest(driverID string, req UnbanDriverRequest, unbanImageRefURL string) model.BanHistory {
	return model.BanHistory{
		DriverID:         driverID,
		Action:           driver.ACTION_ADMIN_UNBAN_TAKEORDER,
		Reason:           req.Reason,
		Value:            string(model.StatusOffline),
		CreatedBy:        req.CreatedBy,
		UnBanImageRefURL: unbanImageRefURL,
	}
}

func (api *DriverAdminAPI) GetDriverRequestUpdateProfile(gctx *gin.Context) {
	driverId := gctx.Param("driver_id")

	driverProfile, err := api.DriverRepo.FindDriverID(gctx, driverId)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	queryBuilder := persistence.BuildRequestUpdateProfileQuery()
	queryBuilder = queryBuilder.
		WithDriverID(driverId).
		SortByUpdatedAt(true).
		SortByStatus(true)
	skip, size := utils.ParsePagination(gctx)

	results, err := api.RequestUpdateProfileRepo.FindWithQueryAndSort(gctx, queryBuilder, skip, size, repository.WithReadSecondaryPreferred)
	if err != nil && err != repository.ErrNotFound {
		apiutil.ErrInternalError(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	count, err := api.RequestUpdateProfileRepo.CountWithQuery(gctx, queryBuilder, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	response := make([]ResponseDriverRequestUpdateProfileInfo, len(results))
	for index, item := range results {
		newResponse := ResponseDriverRequestUpdateProfileInfo{}
		newResponse.ConvertFromModel(driverProfile, item)
		response[index] = newResponse
	}
	apiutil.OKList(gctx, response, count)
}

func (api *DriverAdminAPI) UpdateAllDriverRequestUpdateProfileStatus(gctx *gin.Context) {
	var request RequestToUpdateRequestUpdateProfileStatus
	if err := gctx.ShouldBindJSON(&request); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	driverID := gctx.Param("driver_id")
	targetStatus := []model.RequestProfileStatus{
		model.RequestProfileStatusUpdatePending,
	}
	if request.Status == model.RequestProfileStatusCancel {
		targetStatus = append(targetStatus, model.RequestProfileStatusRequestUpdate)
	}
	query := persistence.BuildRequestUpdateProfileQuery().
		WithStatus(targetStatus).
		WithDriverID(driverID)
	requestUpdateProfileItems, err := api.RequestUpdateProfileRepo.FindWithQueryAndSort(gctx, query, 0, 0, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if len(requestUpdateProfileItems) == 0 {
		if request.Status == model.RequestProfileStatusComplete ||
			request.Status == model.RequestProfileStatusRequestUpdate {
			err := fmt.Errorf("unable to update all request status to %s while current status is REQUESTED_UPDATE", request.Status)
			apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}
	}

	admin := auth.GetAdminEmailFromGctx(gctx)
	if len(requestUpdateProfileItems) == 0 && request.Status == model.RequestProfileStatusCancel {
		logx.Info().Context(gctx).
			Str(logutil.Method, "UpdateAllDriverRequestUpdateProfileStatus").
			Str(logutil.Module, "DriverRequestUpdateProfile").
			Str(logutil.DriverID, driverID).
			Msg("the rider have no profile request to be updated and the admin try to sync the status")

		if err := api.syncDriverProfileStatusAndRemark(gctx, driverID, admin); err != nil {
			apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}

		apiutil.OK(gctx, ResponseDriverRequestProfileUpdateStatus{
			ProfileStatus: model.ProfileStatusCompleted,
		})
		return
	}

	toUpdateObjectIDs := make([]primitive.ObjectID, len(requestUpdateProfileItems))
	for index := range requestUpdateProfileItems {
		toUpdateObjectIDs[index] = requestUpdateProfileItems[index].ID
	}

	ctx := gctx.Request.Context()
	newProfileStatus, err := api.updateDriverRequestUpdateProfileStatus(ctx, admin, request.Status, request.MessageToDriver, toUpdateObjectIDs)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}
	apiutil.OK(gctx, ResponseDriverRequestProfileUpdateStatus{
		ProfileStatus: newProfileStatus,
	})
}

func (api *DriverAdminAPI) UpdateDriverRequestUpdateProfileStatus(gctx *gin.Context) {
	var request RequestToUpdateRequestUpdateProfileStatus
	if err := gctx.ShouldBindJSON(&request); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	objectIDs := make([]primitive.ObjectID, len(request.RequestIDs))
	for index, request := range request.RequestIDs {
		objectID, err := primitive.ObjectIDFromHex(request)
		if err != nil {
			apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
			return
		}
		objectIDs[index] = objectID
	}

	admin := auth.GetAdminEmailFromGctx(gctx)
	ctx := gctx.Request.Context()
	newProfileStatus, err := api.updateDriverRequestUpdateProfileStatus(ctx, admin, request.Status, request.MessageToDriver, objectIDs)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}
	apiutil.OK(gctx, ResponseDriverRequestProfileUpdateStatus{
		ProfileStatus: newProfileStatus,
	})
}

func (api *DriverAdminAPI) updateDriverRequestUpdateProfileStatus(ctx context.Context, admin string, nextStatus model.RequestProfileStatus, messageToDriver string, objectIDs []primitive.ObjectID) (model.ProfileStatus, error) {
	if len(objectIDs) == 0 {
		return model.ProfileStatus(""), nil
	}
	driverProfile, txnError := api.TxnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		queryBuilder := persistence.BuildRequestUpdateProfileQuery().
			WithIDs(objectIDs...)
		targetStatus := []model.RequestProfileStatus{model.RequestProfileStatusUpdatePending}
		if nextStatus == model.RequestProfileStatusCancel {
			targetStatus = append(targetStatus, model.RequestProfileStatusRequestUpdate)
		}
		queryBuilder = queryBuilder.WithStatus(targetStatus)
		updateProfileRequests, err := api.RequestUpdateProfileRepo.FindWithQueryAndSort(sessCtx, queryBuilder, 0, 0)
		if err != nil {
			return nil, err
		}
		if len(updateProfileRequests) == 0 {
			return nil, nil
		}

		driverIDMapper := make(map[string]struct{})
		for _, updateProfileRequest := range updateProfileRequests {
			driverIDMapper[updateProfileRequest.DriverID] = struct{}{}
		}
		if len(driverIDMapper) > 1 {
			return nil, errors.New("unable to update profiles for multiple driver at once")
		}

		targetDriverID := updateProfileRequests[0].DriverID
		driverProfile, err := api.DriverRepo.FindDriverID(sessCtx, targetDriverID)
		if err != nil {
			return nil, err
		}

		toCompleteIDs := make([]primitive.ObjectID, 0)
		// update Driver profile
		if nextStatus == model.RequestProfileStatusComplete {
			for _, updateProfileRequest := range updateProfileRequests {
				if err := updateDriverFromRequestUpdateProfile(
					updateProfileRequest.UpdateDriverProfile,
					driverProfile,
					updateProfileRequest.Fields,
				); err != nil {
					logrus.Warnf("[RIS] Update Profile: %v", err)
					continue
				}
				toCompleteIDs = append(toCompleteIDs, updateProfileRequest.ID)
			}
		}

		// update Driver's remark
		var remarkText string
		switch nextStatus {
		case model.RequestProfileStatusComplete:
			remarkText = "Approved Request Profile Update"
		case model.RequestProfileStatusCancel:
			remarkText = "Rejected Update Profile Request"
		case model.RequestProfileStatusRequestReUpdate:
			remarkText = "Request Reupdate Profile"
		}
		driverProfile.Remarks = append(driverProfile.Remarks, &model.DriverRemark{
			Remark:    remarkText,
			CreatedBy: admin,
			CreatedAt: time.Now(),
		})

		if nextStatus != model.RequestProfileStatusRequestUpdate {
			messageToDriver = ""
		}

		if nextStatus == model.RequestProfileStatusComplete {
			objectIDs = toCompleteIDs
		}
		if len(objectIDs) > 0 {
			if err := api.RequestUpdateProfileRepo.UpdateStatus(sessCtx, nextStatus, messageToDriver, objectIDs...); err != nil {
				return nil, err
			}
		}

		// update profile status
		newProfileStatus, err := api.getNewProfileStatus(sessCtx, targetDriverID)
		if err != nil {
			return nil, err
		}

		driverProfile.ProfileStatus = newProfileStatus
		if err := api.DriverRepo.Update(sessCtx, driverProfile); err != nil {
			return nil, err
		}
		return driverProfile, nil
	}, transaction.WithLabel("DriverAdminAPI.updateDriverRequestUpdateProfileStatus"))

	var profileStatus model.ProfileStatus
	if driverProfile != nil {
		if driver, ok := driverProfile.(*model.Driver); ok {
			profileStatus = driver.ProfileStatus
		}
	}
	return profileStatus, txnError
}

func updateDriverFromRequestUpdateProfile(src model.UpdateDriverProfile, srcDriver *model.Driver, requireList []model.DriverRequestUpdateProfileField) error {
	fieldSetters := make([]model.RequestUpdateFieldSetter, 0)
	for _, requestItem := range requireList {
		newRequestUpdateFieldSetter, fieldType, err := model.NewRequestUpdateFieldSetterByField(requestItem.Field, srcDriver, &src)
		if err != nil {
			return err
		}
		if fieldType == model.ProfileFieldFlag {
			continue
		}
		if requestItem.Required && newRequestUpdateFieldSetter.IsSourceEmpty() {
			return errors.New("source is empty")
		}
		fieldSetters = append(fieldSetters, newRequestUpdateFieldSetter)
	}
	for _, item := range fieldSetters {
		item.Set()
	}
	return nil
}

func (api *DriverAdminAPI) syncDriverProfileStatusAndRemark(ctx context.Context, driverID string, adminEmail string) error {
	_, txnError := api.TxnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		driverProfile, err := api.DriverRepo.FindDriverID(sessCtx, driverID)
		if err != nil {
			return nil, err
		}

		// update profile status
		newProfileStatus, err := api.getNewProfileStatus(sessCtx, driverID)
		if err != nil {
			return nil, err
		}

		if driverProfile == nil {
			return nil, errors.New("rider not found")
		}

		if newProfileStatus == driverProfile.ProfileStatus {
			return nil, nil
		}

		if err := api.DriverRepo.UpdateProfileStatus(sessCtx, driverID, newProfileStatus); err != nil {
			logx.Error().Err(err).Context(sessCtx).
				Str(logutil.Method, "UpdateAllDriverRequestUpdateProfileStatus").
				Str(logutil.Module, "DriverRequestUpdateProfile").
				Str(logutil.DriverID, driverID).
				Msgf("unable to update the rider's profile status to [%s]", newProfileStatus)
			return nil, err
		}

		if err := api.DriverRepo.AddRemark(sessCtx, driverID, model.DriverRemark{
			Remark:    fmt.Sprintf("Update Profile Status to %s", newProfileStatus),
			CreatedBy: adminEmail,
			CreatedAt: timeutil.BangkokNow(),
		}); err != nil {
			logx.Error().Err(err).Context(ctx).
				Str(logutil.Method, "UpdateAllDriverRequestUpdateProfileStatus").
				Str(logutil.Module, "DriverRequestUpdateProfile").
				Str(logutil.DriverID, driverID).
				Msgf("unable to update the rider's remark with updating a profile status to [%s]", newProfileStatus)
		}

		return nil, nil
	})

	return txnError
}

func (api *DriverAdminAPI) getNewProfileStatus(ctx context.Context, targetDriverID string) (model.ProfileStatus, error) {
	queryBuilder := persistence.BuildRequestUpdateProfileQuery().
		WithDriverID(targetDriverID).
		WithStatus(model.GetInProgressRequestProfileStatus())
	remainRequestUpdateProfiles, err := api.RequestUpdateProfileRepo.FindWithQueryAndSort(ctx, queryBuilder, 0, 0)
	if err != nil {
		return model.ProfileStatus(""), err
	}

	newProfileStatus := model.ProfileStatusCompleted
	for _, item := range remainRequestUpdateProfiles {
		switch item.Status {
		case model.RequestProfileStatusRequestUpdate:
			return model.ProfileStatusRequestUpdate, nil
		case model.RequestProfileStatusUpdatePending:
			newProfileStatus = model.ProfileStatusUpdatePending
		}
	}
	return newProfileStatus, nil
}

func (api *DriverAdminAPI) GetIncomeSummary(gctx *gin.Context) {
	driverID := gctx.Param("driver_id")
	if driverID == "" {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, fmt.Errorf("driver_id is required")))
		return
	}

	var req IncomeSummaryReq
	if err := gctx.ShouldBindQuery(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	if err := req.validate(); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	var granularity income.Granularity
	switch req.Granularity {
	case account.DAILY:
		granularity = income.DAILY
	case account.WEEKLY:
		granularity = income.WEEKLY
	case account.MONTHLY:
		granularity = income.MONTHLY
	}

	summary, err := api.IncomeSummaryService.Query(gctx.Request.Context(), income.IncomeSummaryRequest{
		StartDate:   req.Date,
		Granularity: granularity,
		DriverId:    driverID,
	})
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	resp := newIncomeSummaryResponse(gctx, summary)
	apiutil.OK(gctx, resp)
}

func (api *DriverAdminAPI) GetBanEffectiveTimeList(gctx *gin.Context) {
	skip, size := utils.ParsePagination(gctx)

	emptyQuery := persistence.BuildBanEffectiveTimeQuery()
	items, err := api.BanEffectiveTimeRepo.FindWithQueryAndSort(gctx, emptyQuery, skip, size, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	total, err := api.BanEffectiveTimeRepo.CountWithQuery(gctx, emptyQuery)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	responseItems := make([]BanEffectiveTimeResponse, len(items))
	for index, item := range items {
		resItem := BanEffectiveTimeResponse{
			BanEffectiveTime: item,
		}
		resItem.TotalDriverIDs = len(resItem.EffectDriverIDs)
		resItem.RequestedBy = resItem.Detail.CreatedBy
		resItem.EffectDriverIDs = []string{}
		if resItem.ExecutedAt != nil && resItem.ExecutedAt.IsZero() {
			resItem.ExecutedAt = nil
		}
		responseItems[index] = resItem
	}
	apiutil.OKList(gctx, responseItems, total)
}

func (api *DriverAdminAPI) CancelBanEffectiveTime(gctx *gin.Context) {
	effId := gctx.Param("effId")

	oId, err := primitive.ObjectIDFromHex(effId)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if err := api.BanEffectiveTimeRepo.UpdateStatusWithExecutedTime(gctx, oId, model.BanEffectiveCancelled, time.Time{}); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(gctx, nil)
}

func (api *DriverAdminAPI) BulkGoodness(gctx *gin.Context) {
	var req BulkUpdateDriverGoodness
	if err := gctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	reader, err := req.ReadFile()
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	rows, err := req.GetDriverGoodness(reader, api.Cfg.BulkUpdateDriverGoodnessMaxCSVRows)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	optimizedDriverExistsFunc := api.getOptimizedDriverExistsFn(gctx)

	uniqueDriverID := func() func(driverID string) bool {
		memoizer := map[string]any{}
		return func(driverID string) bool {
			if _, exists := memoizer[driverID]; !exists {
				memoizer[driverID] = struct{}{}
				return true
			}
			return false
		}
	}()

	now := timeutil.GetTimeFromContext(gctx)

	resp := &BulkUpdateDriverGoodnessResponse{}

	driverIds := types.NewStringSet()
	for _, v := range rows {
		driverIds.Add(v.DriverID)
	}

	driverExistsMemoize, err := optimizedDriverExistsFunc(driverIds)
	if err != nil {
		err := fmt.Errorf("unable to create driver exists memoize: %v", err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	var candidates []BulkUpdateDriverGoodnessCSVRow
	for idx, row := range rows {
		validRow, err := row.validate(idx, driverExistsMemoize, uniqueDriverID, now)
		if err != nil {
			reason := err.Error()
			if errs, is := err.(interface{ Unwrap() []error }); is {
				errs := errs.Unwrap()
				ss := []string{}
				for _, v := range errs {
					ss = append(ss, v.Error())
				}
				reason = strings.Join(ss, ",")
			}
			resp.AddFailure(row.DriverID, reason)
			continue
		}

		if validRow.RiderLevel != "" && validRow.ExpiredAt == "" {
			eod := timeutil.DateCeilingBKK(now.AddDate(0, 0, 7))
			validRow.ExpiredAtTime = eod
		}

		candidates = append(candidates, validRow)
	}

	bulk := func(ctx context.Context, rows []BulkUpdateDriverGoodnessCSVRow, resp *BulkUpdateDriverGoodnessResponse, wg *sync.WaitGroup) {
		defer wg.Done()
		for _, row := range rows {
			err = api.DriverRepo.SetGoodness(ctx, row.DriverID, model.Goodness{
				Level:          prediction.RiderLevel(row.RiderLevel),
				LevelExpiredAt: &row.ExpiredAtTime,
			})
			if err != nil {
				resp.AddFailure(row.DriverID, fmt.Sprintf("unable to set goodness: %v", err))
				continue
			}
			resp.AddSuccess(row.DriverID)
		}
	}

	batchSize := api.Cfg.BulkUpdateDriverGoodnessBatchSize
	workerPool := api.Cfg.BulkUpdateDriverGoodnessWorker
	partitions := util.Partition(candidates, batchSize)
	round := len(partitions)
	wg := &sync.WaitGroup{}
	wk, release := safe.NewWorker(workerPool)
	defer release()

	wg.Add(round)
	ctx := safe.NewContextWithSameWaitGroup(gctx)
	for _, v := range partitions {
		copy := v
		wk.GoFuncWithPool(func() {
			bulk(ctx, copy, resp, wg)
		})
	}
	wg.Wait()

	apiutil.OK(gctx, resp)
}

type optimizedDriverIdsExistsFn func(driverIds types.StringSet) (func(driverId string) bool, error)

func (api *DriverAdminAPI) getOptimizedDriverExistsFn(ctx context.Context) optimizedDriverIdsExistsFn {
	return func(set types.StringSet) (func(driverId string) bool, error) {
		q := persistence.BuildDriverQuery().WithDriverIDs(set.GetElements())
		drivers, err := api.DriverRepo.FindWithQuerySelectorAndSort(ctx, q, []string{"driver_id"}, []string{}, 0, 0, repository.WithReadSecondaryPreferred)
		if err != nil {
			return nil, err
		}

		m := utils.SliceToMapKeepFirst(func(d model.Driver) string {
			return d.DriverID
		}, func(d model.Driver) struct{} {
			return struct{}{}
		}, drivers)

		return func(driverId string) bool {
			if _, exists := m[driverId]; !exists {
				return false
			}
			return true
		}, nil
	}
}

func (api *DriverAdminAPI) GetFullTimeDriverVendorList(gctx *gin.Context) {
	vendorsResp := []string{}

	adminCfg := api.AtomicAdminConfig.Get()
	if adminCfg.FullTimeDriverVendorList != nil {
		vendorsResp = adminCfg.FullTimeDriverVendorList
	}

	vendorsResp = fp.MapSlice[string, string](func(s string) string {
		return strings.TrimSpace(s)
	}, vendorsResp)

	resp := FullTimeDriverVendorList{
		Vendors: vendorsResp,
	}
	apiutil.OK(gctx, resp)
}
