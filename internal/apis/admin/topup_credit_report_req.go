package admin

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

// ExportTopupCreditReportRequest is export topup credit report request query
type ExportTopupCreditReportRequest struct {
	Bank                      string    `json:"bank" form:"bank"`
	GTEAmount                 float64   `json:"gteAmount" form:"gteAmount"`
	LTEAmount                 float64   `json:"lteAmount" form:"lteAmount"`
	BeginTransactionUpdatedAt time.Time `json:"beginTransactionUpdatedAt" form:"beginTransactionUpdatedAt"`
	EndTransactionUpdatedAt   time.Time `json:"endTransactionUpdatedAt" form:"endTransactionUpdatedAt"`
	Limit                     int       `json:"limit" form:"limit"`
}

func (r *ExportTopupCreditReportRequest) toQuery() repository.TopupCreditReportQuery {

	if r.Limit <= 0 {
		r.Limit = 100
	}

	bt := r.BeginTransactionUpdatedAt
	if !bt.<PERSON><PERSON><PERSON>() {
		bt = bt.Add(time.Hour * 7) // need to be utc and + 7
	}

	et := r.EndTransactionUpdatedAt
	if !et.Is<PERSON>ero() {
		et = et.Add(time.Hour * 7) // need to be utc and + 7
	}

	return repository.TopupCreditReportQuery{
		Bank:                      r.Bank,
		LTEAmount:                 r.LTEAmount,
		GTEAmount:                 r.GTEAmount,
		BeginTransactionUpdatedAt: bt,
		EndTransactionUpdatedAt:   et,
		Limit:                     r.Limit,
	}
}
