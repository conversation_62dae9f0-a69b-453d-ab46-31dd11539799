package admin_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/admin"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestTopupCreditReportAPI_ExportCSV(t *testing.T) {
	makeReq := func(req admin.ExportTopupCreditReportRequest) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/topup-credit-report-export")
		gctx.Body().JSON(req).Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	t.Run("no date range set from the request", func(tt *testing.T) {
		svc, _ := newTestTopupCreditReportAPIDeps(tt)

		req, rec := makeReq(admin.ExportTopupCreditReportRequest{
			Bank:                      "CITI",
			GTEAmount:                 0,
			LTEAmount:                 1000,
			BeginTransactionUpdatedAt: time.Time{},
			EndTransactionUpdatedAt:   time.Time{},
		})

		svc.ExportCSV(req)

		var actual *api.Error
		testutil.DecodeJSON(tt, rec.Body, &actual)
		require.Equal(tt, http.StatusBadRequest, rec.Code)
		require.Equal(tt, "both start and end date must be set", actual.Message)
	})

	t.Run("only end date set from the request", func(tt *testing.T) {
		svc, _ := newTestTopupCreditReportAPIDeps(tt)

		req, rec := makeReq(admin.ExportTopupCreditReportRequest{
			Bank:                      "CITI",
			GTEAmount:                 0,
			LTEAmount:                 1000,
			BeginTransactionUpdatedAt: time.Time{},
			EndTransactionUpdatedAt:   timeutils.Now(),
		})

		svc.ExportCSV(req)

		var actual *api.Error
		testutil.DecodeJSON(tt, rec.Body, &actual)
		require.Equal(tt, http.StatusBadRequest, rec.Code)
		require.Equal(tt, "both start and end date must be set", actual.Message)
	})

	t.Run("date range exceed a limit", func(tt *testing.T) {
		svc, _ := newTestTopupCreditReportAPIDeps(tt, func(cfg *admin.Config) {
			cfg.TopupReportSearchDayLimit = 7
		})

		src := admin.ExportTopupCreditReportRequest{
			Bank:                      "CITI",
			GTEAmount:                 0,
			LTEAmount:                 1000,
			BeginTransactionUpdatedAt: timeutils.Now().AddDate(0, 0, -8),
			EndTransactionUpdatedAt:   timeutils.Now(),
		}
		req, rec := makeReq(src)

		svc.ExportCSV(req)

		var actual *api.Error
		testutil.DecodeJSON(tt, rec.Body, &actual)
		require.Equal(tt, http.StatusBadRequest, rec.Code)
		require.Equal(tt, "date range is more than limit", actual.Message)
	})

	t.Run("happy flow with 0 data", func(tt *testing.T) {
		timeutils.Freeze()
		defer timeutils.Unfreeze()

		svc, deps := newTestTopupCreditReportAPIDeps(tt, func(cfg *admin.Config) {
			cfg.TopupReportWorkerPoolLimit = 1
			cfg.TopupReportDownloadLimitPerFile = 1000
			cfg.TopupReportSearchDayLimit = 7
		})

		currentFreezedTime := timeutils.Now()
		src := admin.ExportTopupCreditReportRequest{
			Bank:                      "CITI",
			GTEAmount:                 0,
			LTEAmount:                 1000,
			BeginTransactionUpdatedAt: currentFreezedTime.AddDate(0, 0, -3),
			EndTransactionUpdatedAt:   currentFreezedTime,
		}
		req, rec := makeReq(src)

		expectedQuery := repository.TopupCreditReportQuery{
			Bank:                      "CITI",
			LTEAmount:                 1000,
			GTEAmount:                 0,
			BeginTransactionUpdatedAt: currentFreezedTime.AddDate(0, 0, -3).Add(time.Hour * 7),
			EndTransactionUpdatedAt:   currentFreezedTime.Add(time.Hour * 7),
			Limit:                     100,
		}
		deps.topupCreditReportRepo.EXPECT().CountByQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, q repository.TopupCreditReportQuery, opt ...repository.Option) (int, error) {

				assert.Equal(tt, expectedQuery.BeginTransactionUpdatedAt.Format(timeutil.RFC3339ClientFormat), q.BeginTransactionUpdatedAt.Format(timeutil.RFC3339ClientFormat))
				assert.Equal(tt, expectedQuery.EndTransactionUpdatedAt.Format(timeutil.RFC3339ClientFormat), q.EndTransactionUpdatedAt.Format(timeutil.RFC3339ClientFormat))

				expectedQuery.BeginTransactionUpdatedAt = q.BeginTransactionUpdatedAt
				expectedQuery.EndTransactionUpdatedAt = q.EndTransactionUpdatedAt

				assert.Equal(tt, expectedQuery, q)
				return 0, nil
			})

		svc.ExportCSV(req)

		var actual struct {
			Links []admin.CSVLink `json:"links"`
		}
		testutil.DecodeJSON(tt, rec.Body, &actual)
		require.Equal(tt, http.StatusOK, rec.Code)
		require.Equal(tt, []admin.CSVLink{}, actual.Links)
	})
}

type testTopupCreditReportAPIDeps struct {
	topupCreditReportRepo *mock_repository.MockTopupCreditReportRepository
	vosService            *mock_service.MockVOSService
}

func newTestTopupCreditReportAPIDeps(t *testing.T, configModifiers ...func(cfg *admin.Config)) (*admin.TopupCreditReportAPI, testTopupCreditReportAPIDeps) {
	mockctrl := gomock.NewController(t)
	topupCreditReportRepo := mock_repository.NewMockTopupCreditReportRepository(mockctrl)
	vosService := mock_service.NewMockVOSService(mockctrl)

	cfg := admin.Config{}
	for _, mod := range configModifiers {
		mod(&cfg)
	}

	deps := testTopupCreditReportAPIDeps{
		topupCreditReportRepo: topupCreditReportRepo,
		vosService:            vosService,
	}

	return admin.ProvideTopupCreditReportAPI(topupCreditReportRepo, vosService, cfg), deps
}
