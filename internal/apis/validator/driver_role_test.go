package validator_test

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
	"gopkg.in/go-playground/validator.v9"
	"gopkg.in/guregu/null.v4"

	driverValidator "git.wndv.co/lineman/fleet-distribution/internal/apis/validator"
)

func Test_DriverRole_Validator(t *testing.T) {
	type DriverRoleTestStruct struct {
		DriverRole string `validate:"driver-role"`
	}

	testcases := []struct {
		driverRole string
		error      bool
	}{
		{driverRole: "NORMAL", error: false},
		{driverRole: "NON_EXISTS_ROLE", error: true},
		{driverRole: "GOD", error: true},
		{driverRole: "normal", error: true},
	}

	driverRoleValidator := driverValidator.NewDriverRoleValidator()

	validate := validator.New()
	if err := validate.RegisterValidation(driverRoleValidator.Name(), driverRoleValidator.Validator(), true); err != nil {
		panic(fmt.Sprintf("[DriverRole Validator] Unexpected error occured: %v", err.Error()))
	}

	for _, tc := range testcases {
		err := validate.Struct(DriverRoleTestStruct{
			DriverRole: tc.driverRole,
		})

		if tc.error {
			require.Error(t, err, fmt.Sprintf("Expect error at driverRole"+
				"=%s", tc.driverRole,
			))
		} else {
			require.NoError(t, err, fmt.Sprintf("Expect no error atdriverRole"+
				"=%s", tc.driverRole,
			))
		}
	}
}

func Test_DriverRole_Nullable_String_Validator(t *testing.T) {
	type DriverRoleNullableTestStruct struct {
		DriverRole null.String `validate:"driver-role"`
	}

	testcases := []struct {
		driverRole null.String
		error      bool
	}{
		{driverRole: null.StringFrom("NORMAL"), error: false},
		{driverRole: null.StringFrom("NON_EXISTS_TIER"), error: true},
		{driverRole: null.StringFrom("GOD"), error: true},
		{driverRole: null.StringFrom("normal"), error: true},
		{driverRole: null.StringFromPtr(nil), error: true},
	}

	driverRoleValidator := driverValidator.NewDriverRoleValidator()

	validate := validator.New()
	validate.RegisterCustomTypeFunc(driverValidator.NullableValueValidator, null.String{})
	if err := validate.RegisterValidation(driverRoleValidator.Name(), driverRoleValidator.Validator(), true); err != nil {
		panic(fmt.Sprintf("[DriverRole Validator] Unexpected error occured: %v", err.Error()))
	}

	for _, tc := range testcases {
		err := validate.Struct(DriverRoleNullableTestStruct{
			DriverRole: tc.driverRole,
		})

		if tc.error {
			require.Error(t, err, fmt.Sprintf("Expect error at driverRole=%s", tc.driverRole.ValueOrZero()))
		} else {
			require.NoError(t, err, fmt.Sprintf("Expect no error at driverRole=%s", tc.driverRole.ValueOrZero()))
		}
	}
}
