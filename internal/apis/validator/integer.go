package validator

import (
	"regexp"

	ut "github.com/go-playground/universal-translator"
	"gopkg.in/go-playground/validator.v9"
)

type Integer struct {
	integerRegx  *regexp.Regexp
	errorMessage map[string]string
}

func (p *Integer) Validator() func(fl validator.FieldLevel) bool {
	return func(fl validator.FieldLevel) bool {
		return p.integerRegx.MatchString(fl.Field().String())
	}
}

func (p *Integer) Name() string {
	return "integer"
}

func (p *Integer) RegisterTranslation(v *validator.Validate, uni *ut.UniversalTranslator) {
	for _, locale := range AvailableLocales {
		tran, found := uni.GetTranslator(locale)
		msg, ok := p.errorMessage[locale]
		if !ok {
			msg = p.errorMessage[DefaultLocale]
		}
		if found {
			err := v.RegisterTranslation(p.Name(), tran, func(tr ut.Translator) error {
				return tr.Add(p.Name(), msg, false)
			}, func(tr ut.Translator, fe validator.FieldError) string {
				msg, _ := tr.T(p.Name(), fe.Field())
				return msg
			})
			if err != nil {
				panic(err)
			}
		}
	}
	err := v.RegisterValidation(p.Name(), p.Validator())
	if err != nil {
		panic(err)
	}
}

func NewInteger() *Integer {
	regx, err := regexp.Compile(`^[\d]*$`)
	if err != nil {
		panic(err)
	}
	return &Integer{
		integerRegx: regx,
		errorMessage: map[string]string{
			LocaleEN: "{0} must be integer",
			LocaleTH: "{0} ต้องเป็นตัวเลขเท่านั้น",
		},
	}
}
