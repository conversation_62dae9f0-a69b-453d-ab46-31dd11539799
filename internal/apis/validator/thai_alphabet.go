package validator

import (
	"regexp"

	ut "github.com/go-playground/universal-translator"
	"gopkg.in/go-playground/validator.v9"
)

type ThaiAlphabet struct {
	thaiRegx     *regexp.Regexp
	errorMessage map[string]string
}

func (p *ThaiAlphabet) Validator() func(fl validator.FieldLevel) bool {
	return func(fl validator.FieldLevel) bool {
		return p.thaiRegx.MatchString(fl.Field().String())
	}
}

func (p *ThaiAlphabet) Name() string {
	return "alphabet-thai"
}

func (p *ThaiAlphabet) RegisterTranslation(v *validator.Validate, uni *ut.UniversalTranslator) {
	for _, locale := range AvailableLocales {
		tran, found := uni.GetTranslator(locale)
		msg, ok := p.errorMessage[locale]
		if !ok {
			msg = p.errorMessage[DefaultLocale]
		}
		if found {
			err := v.RegisterTranslation(p.Name(), tran, func(tr ut.Translator) error {
				return tr.Add(p.Name(), msg, false)
			}, func(tr ut.Translator, fe validator.FieldError) string {
				msg, _ := tr.T(p.Name(), fe.Field())
				return msg
			})
			if err != nil {
				panic(err)
			}
		}
	}
	err := v.RegisterValidation(p.Name(), p.Validator())
	if err != nil {
		panic(err)
	}
}

func NewThaiAlphabet() *ThaiAlphabet {
	regx, err := regexp.Compile(`^[\p{Thai}|\s]+$`)
	if err != nil {
		panic(err)
	}
	return &ThaiAlphabet{
		thaiRegx: regx,
		errorMessage: map[string]string{
			LocaleEN: "{0} must be thai character",
			LocaleTH: "{0} ต้องเป็นตัวอักษรไทยเท่านั้น",
		},
	}
}
