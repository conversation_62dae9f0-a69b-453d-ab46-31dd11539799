package validator

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
	"gopkg.in/go-playground/validator.v9"
)

func TestThaiAlphabet_Validator(t *testing.T) {
	type ThaiAlphabetStruct struct {
		ThaiAlphabet string `validate:"alphabet-thai"`
	}

	testcases := []struct {
		text  string
		error bool
	}{
		{text: "ฉันคือคนไทย", error: false},
		{text: "และคนไทยแปลว่า อิสระ", error: false},
		{text: "ABCD", error: true},
		{text: "ฉันชื่อ A", error: true},
	}

	thaiAlphabetValidator := NewThaiAlphabet()

	validate := validator.New()
	validate.RegisterValidation(thaiAlphabetValidator.Name(), thaiAlphabetValidator.Validator(), true)
	for id, tc := range testcases {
		err := validate.Struct(ThaiAlphabetStruct{
			ThaiAlphabet: tc.text,
		})

		if tc.error {
			require.Error(t, err, fmt.Sprintf("row : %d", id+1))
		} else {
			require.NoError(t, err, fmt.Sprintf("row : %d", id+1))
		}
	}
}
