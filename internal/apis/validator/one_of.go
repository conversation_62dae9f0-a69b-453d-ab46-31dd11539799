package validator

import (
	ut "github.com/go-playground/universal-translator"
	stdValidator "gopkg.in/go-playground/validator.v9"

	"git.wndv.co/go/logx/v2"
)

type OneOfValidator struct {
	possibleValueList map[string]bool
	name              string
}

func (o *OneOfValidator) Validator() func(fl stdValidator.FieldLevel) bool {
	return func(fl stdValidator.FieldLevel) bool {
		return o.possibleValueList[fl.Field().String()]
	}
}

func (o *OneOfValidator) RegisterTranslation(v *stdValidator.Validate, _ *ut.UniversalTranslator) {
	err := v.RegisterValidation(o.Name(), o.Validator())
	if err != nil {
		logx.Error().Err(err).Msg("Can't register OneOfValidator to the validator")
		panic(err)
	}
}

func (o *OneOfValidator) Name() string {
	return o.name
}

func NewOneOfValidator(name string, possibleValueList []string) Validator {
	oneOfMap := make(map[string]bool)
	for _, i := range possibleValueList {
		oneOfMap[i] = true
	}
	return &OneOfValidator{
		name:              name,
		possibleValueList: oneOfMap,
	}
}
