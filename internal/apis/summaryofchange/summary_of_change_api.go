package summaryofchange

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"git.wndv.co/go/logx/v2"
	absintheapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

type SummaryOfChangeAPI struct {
	repo      repository.SummaryOfChangeRepository
	auditRepo repository.AuditLogRepository
}

func ProvideSummaryOfChangeAPI(repo repository.SummaryOfChangeRepository, auditRepo repository.AuditLogRepository) *SummaryOfChangeAPI {
	return &SummaryOfChangeAPI{
		repo:      repo,
		auditRepo: auditRepo,
	}
}
func (api *SummaryOfChangeAPI) GetSummaryOfChanges(ctx *gin.Context) {
	driverID := driver.DriverIDFromGinContext(ctx)

	limit, _ := strconv.Atoi(ctx.Query("limit"))
	if limit <= 0 {
		limit = 0
	}

	query := repository.NewSummaryOfChangeQuery().SetStatus(model.SummaryOfChangeStatusActive).Query()
	summaries, err := api.repo.FindWithQueryAndSortSummaryOfChange(ctx, query, 0, limit, repository.WithReadSecondaryPreferred)
	if err != nil {
		logx.Error().Err(err).Msgf("GetSummaryOfChanges FindWithQueryAndSortSummaryOfChange driver: %s", driverID)
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	apiutil.OK(ctx, NewClientSummaryOfChangeListRes(summaries))
}
