package rain_situation

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	absintheapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type RainSituationAdminAPI struct {
	cfg                     Config
	rainSituationRepo       repository.RainSituationRepository
	rainSituationService    service.RainSituationService
	TxnHelper               transaction.TxnHelper
	slack                   slack.Slack
	rainSituationServiceCfg *service.AtomicRainSituationConfig
	globalConfig            config.GlobalConfig
}

func ProvideRainSituationAdminAPI(
	cfg Config,
	rainSituationRepo repository.RainSituationRepository,
	rainSituationService service.RainSituationService,
	txnHelper transaction.TxnHelper,
	slack slack.Slack,
	rainSituationServiceCfg *service.AtomicRainSituationConfig,
	globalConfig config.GlobalConfig,
) *RainSituationAdminAPI {
	return &RainSituationAdminAPI{
		cfg:                     cfg,
		rainSituationRepo:       rainSituationRepo,
		rainSituationService:    rainSituationService,
		TxnHelper:               txnHelper,
		slack:                   slack,
		rainSituationServiceCfg: rainSituationServiceCfg,
		globalConfig:            globalConfig,
	}
}

// ListRainSituations @Summary List rain situations
// @Description Get a paginated list of rain situations.
// @tags RAIN SITUATION
// @Accept json
// @Produce json
// @Param page query integer false "Page number for pagination"
// @Param per_page query integer false "Number of items per page"
// @Success 200 {object} []RainSituationRes
// @Router /v1/admin/rain-situations [get]
func (rsi *RainSituationAdminAPI) ListRainSituations(ctx *gin.Context) {
	var req ListRainSituationReq

	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(err))
		return
	}

	skip, size := utils.ParsePagination(ctx)
	query := req.Query()

	sortBy := []string{"updated_at"}

	var list []model.RainSituation
	{
		var err error
		if !req.IsDownload {
			list, err = rsi.rainSituationRepo.FindWithQueryAndSort(ctx, query, skip, size, sortBy)
		} else {
			list, err = rsi.rainSituationRepo.FindWithQueryAndSortSelector(ctx, query, skip, size, sortBy)
		}

		if err != nil {
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}
	}

	count, err := rsi.rainSituationRepo.CountWithQuery(ctx, query, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}
	if list == nil {
		list = []model.RainSituation{}
	}
	response := make([]RainSituationRes, len(list))

	for i, r := range list {
		response[i] = NewRainSituationRes(r)
	}

	apiutil.OKList(ctx, response, count)
}

// CreateRainSituation @Summary Create a new rain situation
// @Description Create a new rain situation with the given data.
// @tags RAIN SITUATION
// @Accept json
// @Produce json
// @Param rainSituation body CreateRainSituationReq true "Rain situation object to create"
// @Success 204 "No Content"
// @Router /v1/admin/rain-situations [post]
func (rsi *RainSituationAdminAPI) CreateRainSituation(ctx *gin.Context) {
	var req CreateRainSituationReq

	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(err))
		return
	}
	adminEmail := auth.GetAdminEmailFromGctx(ctx)
	req.CreatedBy = adminEmail
	req.UpdatedBy = adminEmail

	rainSituation := req.toRainSituationModel()

	if err := rsi.rainSituationRepo.Create(ctx, &rainSituation, rsi.rainSituationServiceCfg.Get().RainingStatuses); err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.NoContent(ctx)
}

// GetRainSituation @Summary Get a rain situation by ID
// @Description Retrieve a rain situation by its unique ID.
// @tags RAIN SITUATION
// @Accept json
// @Produce json
// @Param id path string true "ID of the rain situation to retrieve"
// @Success 200 {object} RainSituationRes
// @Router /v1/admin/rain-situations/{id} [get]
func (rsi *RainSituationAdminAPI) GetRainSituation(ctx *gin.Context) {
	id := ctx.Param("id")

	rainSituation, err := rsi.rainSituationRepo.Get(ctx, id)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if rainSituation == nil {
		apiutil.ErrNotFound(ctx, apiutil.NewFromString("RAIN_SITUATION_NOT_EXISTS", "RainSituation is not exists"))
		return
	}

	response := NewRainSituationRes(*rainSituation)
	apiutil.OK(ctx, response)
}

// DeleteRainSituation @Summary Delete a rain situation
// @Description Delete a rain situation by its unique ID.
// @tags RAIN SITUATION
// @Accept json
// @Produce json
// @Param id path string true "ID of the rain situation to delete"
// @Success 204 "No Content"
// @Router /v1/admin/rain-situations/{id} [delete]
func (rsi *RainSituationAdminAPI) DeleteRainSituation(ctx *gin.Context) {
	id := ctx.Param("id")

	err := rsi.rainSituationRepo.Delete(ctx, id)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.NoContent(ctx)
}

// UpdateRainSituation @Summary Update a rain situation
// @Description Update a rain situation with the given data.
// @tags RAIN SITUATION
// @Accept json
// @Produce json
// @Param id path string true "ID of the rain situation to update"
// @Param rainSituation body UpdateRainSituationReq true "Rain situation object to update"
// @Success 200 {object} RainSituationRes
// @Router /v1/admin/rain-situations/{id} [put]
func (rsi *RainSituationAdminAPI) UpdateRainSituation(ctx *gin.Context) {
	var req UpdateRainSituationReq

	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(err))
		return
	}
	adminEmail := auth.GetAdminEmailFromGctx(ctx)
	req.UpdatedBy = adminEmail

	id := ctx.Param("id")

	existingRainSituation, err := rsi.rainSituationRepo.Get(ctx, id)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if existingRainSituation == nil {
		apiutil.ErrNotFound(ctx, apiutil.NewFromString("RAIN_SITUATION_NOT_EXISTS", "RainSituation does not exist"))
		return
	}

	req.updateExistingRainSituation(existingRainSituation)

	if err := rsi.rainSituationRepo.Update(ctx, id, existingRainSituation, rsi.rainSituationServiceCfg.Get().RainingStatuses); err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(ctx, existingRainSituation)
}

// BulkUpdateRainSituation @Summary Bulk update Rain Situation status by Admin
// @Description Read a file in CSV format to update Rain Situation status
// @tags RAIN SITUATION
// @Accept json
// @Produce json
// @Param file body BulkUpdateRainSituationReq true "A file to update Rain Situation"
// @Success 200
// @Failure 200 {object} BulkUpdateRainSituationRes
// @Router /v1/admin/bulk/rain-situations [put]
func (rsi *RainSituationAdminAPI) BulkUpdateRainSituation(ctx *gin.Context) {
	var req BulkUpdateRainSituationReq
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(err))
		return
	}

	adminEmail := auth.GetAdminEmailFromGctx(ctx)

	bulkData, failures, err := req.GetData()
	if err != nil {
		apiutil.ErrInternalError(ctx, apiError.ErrInternal(err))
		return
	}

	success := make([]BulkUpdateRainSituationRes, 0)
	if failures == nil {
		failures = make([]BulkUpdateRainSituationRes, 0)
	}
	updatedRecords := []model.RainSituation{}
	undetectedRecords := []model.RainSituation{}

	if _, err := rsi.TxnHelper.WithTxn(ctx, func(ctx context.Context) (interface{}, error) {
		for _, data := range bulkData {
			res := BulkUpdateRainSituationRes{
				Name:            data.Name,
				RainStatus:      data.RainStatus.String(),
				RainStatusEndAt: data.OverridePeriod.End.Format(time.RFC3339Nano),
			}
			if err := rsi.updateRainSituation(ctx, data, adminEmail); err != nil {
				res.Error = err.Error()
				failures = append(failures, res)
				undetectedRecords = append(undetectedRecords, data)
				continue
			}
			success = append(success, res)
			updatedRecords = append(updatedRecords, data)
		}

		return nil, nil
	}, transaction.WithNoRetry(), transaction.WithLabel("RainSituationAdminAPI.BulkUpdateRainSituation")); err != nil {
		apiutil.ErrInternalError(ctx, apiError.ErrInternal(err))
		return
	}

	// Display all override rain situations after bulk update
	rainSituations, err := rsi.rainSituationRepo.FindWithQueryAndSort(ctx, repository.RainSituationQuery{}, 0, 0, []string{"region"})
	if err != nil {
		logrus.WithField("method", "UpdateRainStatus").WithError(err).Error("unable to get rain situation")
		apiutil.ErrInternalError(ctx, apiError.ErrInternal(err))
		return
	}
	overrideRecords := []model.RainSituation{}
	for _, rainSituation := range rainSituations {
		if rainSituation.IsOverrideRainStatus() {
			overrideRecords = append(overrideRecords, rainSituation)
		}
	}

	if rsi.cfg.RainSituationSlackNotificationEnabled && len(updatedRecords) > 0 {
		notification := slack.NewRainSituationSlackBuilder(slack.RainSlackBuilderParams{
			RainSituations:           overrideRecords,
			UndetectedRainSituations: undetectedRecords,
			RainingStatuses:          rsi.rainSituationServiceCfg.Get().RainingStatuses,
			Limit:                    rsi.cfg.RainSituationSlackNotificationLimit,
			AdminPageUrl:             rsi.cfg.RainSituationSlackNotificationAdminPageUrl,
			WikiPageUrl:              rsi.cfg.RainSituationSlackNotificationWikiPageUrl,
		}).
			AddRequestBy(adminEmail).
			AddEnvDisplayOption(rsi.globalConfig.EnvName).
			SetWebhook(slack.WebhookFleetRainPODStatusMonitoring).
			Build()
		if err := rsi.slack.Notify(ctx, notification); err != nil {
			logrus.WithField("method", "UpdateRainStatus").Errorf("unable to notify pod status: %v", err)
		}
	}

	apiutil.OK(ctx, map[string]any{
		"success": success,
		"failure": failures,
	})
}

func (rsi *RainSituationAdminAPI) updateRainSituation(ctx context.Context, updateData model.RainSituation, adminEmail string) error {
	existedItem, err := rsi.rainSituationRepo.GetByName(ctx, updateData.Name, repository.WithReadSecondaryPreferred)
	if err != nil {
		return err
	}

	bkkCurrentTime := timeutil.BangkokNow()
	existedItem.OverrideRainStatus = updateData.RainStatus
	existedItem.OverrideAt = bkkCurrentTime
	existedItem.OverridePeriod = model.OverridePeriod{
		Start: bkkCurrentTime,
		End:   updateData.OverridePeriod.End,
	}
	existedItem.UpdatedBy = adminEmail

	if err := rsi.rainSituationRepo.Update(ctx, existedItem.ID.Hex(), existedItem, rsi.rainSituationServiceCfg.Get().RainingStatuses); err != nil {
		return err
	}

	return nil
}
