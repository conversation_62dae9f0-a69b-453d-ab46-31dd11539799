package rain_situation

import (
	"github.com/kelseyhightower/envconfig"
)

type Config struct {
	UpdateRainSituationEnabled                 bool   `envconfig:"UPDATE_RAIN_SITUATION_ENABLED" default:"true"`
	RainSituationSlackNotificationEnabled      bool   `envconfig:"RAIN_SITUATION_SLACK_NOTIFICATION_ENABLED" default:"true"`
	RainSituationSlackNotificationLimit        int    `envconfig:"RAIN_SITUATION_SLACK_NOTIFICATION_LIMIT" default:"30"`
	RainSituationSlackNotificationAdminPageUrl string `envconfig:"RAIN_SITUATION_SLACK_NOTIFICATION_ADMIN_PAGE_URL" default:"https://lm-admin.line-apps-beta.com/rain-situations"`
	RainSituationSlackNotificationWikiPageUrl  string `envconfig:"RAIN_SITUATION_SLACK_NOTIFICATION_WIKI_PAGE_URL" default:"https://linemanwongnai.atlassian.net/wiki/spaces/LMFleet/pages/853608726/How+to+-+Rain+notification+handbook#Message"`
}

func ProvideConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}
