package middlewares

import (
	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/riderapp"
)

func (b *Builder) HandlerOnSemver(major, minor, patch int, hnd gin.HandlerFunc) gin.HandlerFunc {
	return func(gctx *gin.Context) {
		ctx := gctx.Request.Context()
		info := riderapp.FromContext(ctx)

		if info.ClientVersionSatisfy(major, minor, patch) {
			hnd(gctx)
		} else {
			gctx.Next()
		}
	}
}

func (b *Builder) MinVersion(major, minor, patch int) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if !b.Config.UseMinApiVersion {
			ctx.Next()
			return
		}

		appinfo := riderapp.FromContext(ctx.Request.Context())
		if !appinfo.ClientVersionSatisfy(major, minor, patch) {
			apiutil.ErrBadRequest(ctx, errors.ErrAppVersionOutdated(appinfo))
		} else {
			ctx.Next()
		}
	}
}
