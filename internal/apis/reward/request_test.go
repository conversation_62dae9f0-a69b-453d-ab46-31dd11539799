package reward

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func Test_validateDuplicatePriorityConditions(t *testing.T) {
	testcases := []struct {
		name       string
		conditions []PriorityConversionRateConditionReq
		isErr      bool
		errMessage string
	}{
		{
			name:       "should not return error for empty conditions",
			conditions: []PriorityConversionRateConditionReq{},
			isErr:      false,
		},
		{
			name: "should not return error for unique priority conditions",
			conditions: []PriorityConversionRateConditionReq{
				{
					DriverIds: []string{"driver1"},
					Days:      []model.Days{model.Monday, model.Tuesday},
				},
				{
					DriverIds: []string{"driver2"},
					Days:      []model.Days{model.Monday},
				},
				{
					DriverIds: []string{"driver1"},
					Days:      []model.Days{model.Wednesday},
				},
			},
			isErr: false,
		},
		{
			name: "should return error for duplicate priority conditions",
			conditions: []PriorityConversionRateConditionReq{
				{
					DriverIds: []string{"driver1"},
					Days:      []model.Days{model.Monday, model.Tuesday},
				},
				{ // duplicate with the first condition
					DriverIds: []string{"driver1"},
					Days:      []model.Days{model.Monday},
				},
			},
			isErr:      true,
			errMessage: "found duplicate priority condition on driver id driver1, day MON: duplicate priority condition",
		},
		{
			name: "should return error for duplicate priority conditions (more than 2)",
			conditions: []PriorityConversionRateConditionReq{
				{
					DriverIds: []string{"driver1"},
					Days:      []model.Days{model.Monday, model.Tuesday},
				},
				{
					DriverIds: []string{"driver2"},
					Days:      []model.Days{model.Monday},
				},
				{ // duplicate with the first condition
					DriverIds: []string{"driver1"},
					Days:      []model.Days{model.Sunday, model.Monday},
				},
			},
			isErr:      true,
			errMessage: "found duplicate priority condition on driver id driver1, day MON: duplicate priority condition",
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			err := validateDuplicatePriorityConditions(tc.conditions)
			if !tc.isErr {
				require.NoError(tt, err)
				return
			}
			require.Error(tt, err)
			require.ErrorContains(tt, err, tc.errMessage)
		})
	}
}
