package reward

import (
	"github.com/gin-gonic/gin"

	absintheError "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type RewardAdminAPI struct {
	RewardBalanceRepository      repository.RewardBalanceRepository
	DailyRewardBalanceRepository repository.DailyRewardRepository
	RewardTransactionProcessor   service.RewardTransactionProcessor
	CoinCashConversionRateRepo   repository.CoinCashConversionRateRepository
	RewardTransactionRepo        repository.RewardTransactionRepository
	Config                       Config
}

func (api *RewardAdminAPI) GetDriverCoinBalance(gctx *gin.Context) {
	var query GetDriverCoinBalanceQuery
	if err := gctx.ShouldBindQuery(&query); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	if query.Date.IsZero() {
		query.Date = timeutil.BangkokNow().UTC()
	}

	driverID := gctx.Param("driver_id")

	balance, err := api.RewardTransactionRepo.SumCoinBalance(gctx, driverID, query.Date, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, ToGetDriverCoinBalanceResponse(query.Date, balance))
}

func (api *RewardAdminAPI) BulkUploadCoinTransaction(gctx *gin.Context) {
	var req BulkUploadCoinTransactionRequest
	adminEmail := auth.GetAdminEmailFromGctx(gctx)
	if err := gctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(gctx, utils.ErrorStructResponse(err))
		return
	}
	rewardTxns, err := req.ToRewardTransaction(adminEmail)
	if err != nil {
		apierror, ok := err.(*absintheError.Error)
		if ok {
			apiutil.ErrBadRequest(gctx, apierror)
			return
		}
		apiutil.ErrBadRequest(gctx, utils.ErrorStructResponse(err))
		return
	}

	resp := BulkUploadRewardTransactionResponse{
		Successes: []BulkUploadRewardTransactionSuccessResponse{},
		Failures:  []BulkUploadRewardTransactionFailResponse{},
	}

	for i, txn := range rewardTxns {
		if i >= bulkUploadCoinTransactionRecordSize {
			resp.Failures = append(resp.Failures, BulkUploadRewardTransactionFailResponse{Row: i + 1, Error: ErrRowExceedRecordLimit.Error()})
			continue
		}
		err = api.RewardTransactionProcessor.ProcessRewardTransaction(gctx, txn.DriverID, service.ToRewardTransactionBuilder([]model.RewardTransaction{txn}))
		if err != nil {
			resp.Failures = append(resp.Failures, BulkUploadRewardTransactionFailResponse{Row: i + 1, Error: err.Error()})
			continue
		}
		resp.Successes = append(resp.Successes, BulkUploadRewardTransactionSuccessResponse{Row: i + 1, DriverID: txn.DriverID})
	}
	apiutil.OK(gctx, resp)
}

func (api *RewardAdminAPI) ListConversionRate(gctx *gin.Context) {
	var query ListConversionRateReq
	if err := gctx.ShouldBindQuery(&query); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}
	coinCashConversionRates, err := api.CoinCashConversionRateRepo.List(gctx, query.ToQuery(), repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}
	apiutil.OK(gctx, ToListCoinCashConversionRateResponse(coinCashConversionRates))
}

func (api *RewardAdminAPI) GetCoinCashConversionRateByID(gctx *gin.Context) {
	id := gctx.Param("id")
	coinCashConversionRate, err := api.CoinCashConversionRateRepo.GetByID(gctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiError.ErrCoinCashConversionRateNotExists())
			return
		}
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}
	apiutil.OK(gctx, ToCoinCashConversionRateResponse(coinCashConversionRate))
}

func (api *RewardAdminAPI) CreateCoinCashConversionRate(gctx *gin.Context) {
	var req CreateConversionRateReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}
	adminEmail := auth.GetAdminEmailFromGctx(gctx)
	coinCashConversionRate, err := req.ToModel(adminEmail, api.Config)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}
	if err = api.CoinCashConversionRateRepo.Create(gctx, coinCashConversionRate); err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}
	apiutil.Created(gctx, ToCoinCashConversionRateResponse(coinCashConversionRate))
}

func (api *RewardAdminAPI) UpdateCoinCashConversionRate(gctx *gin.Context) {
	var req UpdateConversionRateReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}
	id := gctx.Param("id")
	adminEmail := auth.GetAdminEmailFromGctx(gctx)
	coinCashConversionRate, err := api.CoinCashConversionRateRepo.GetByID(gctx, id)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiError.ErrCoinCashConversionRateNotExists())
			return
		}
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}
	newCoinCashConversionRate, err := req.ToModel(&coinCashConversionRate, adminEmail, api.Config)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}
	if err = api.CoinCashConversionRateRepo.Update(gctx, id, newCoinCashConversionRate); err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}
	apiutil.OK(gctx, ToCoinCashConversionRateResponse(newCoinCashConversionRate))
}

func ProvideRewardAdminAPI(
	rewardBalanceRepository repository.RewardBalanceRepository,
	dailyRewardRepository repository.DailyRewardRepository,
	rewardTransactionProcessor service.RewardTransactionProcessor,
	coinCashConversionRateRepo repository.CoinCashConversionRateRepository,
	rewardTransactionRepo repository.RewardTransactionRepository,
	config Config,
) *RewardAdminAPI {
	return &RewardAdminAPI{
		RewardBalanceRepository:      rewardBalanceRepository,
		DailyRewardBalanceRepository: dailyRewardRepository,
		RewardTransactionProcessor:   rewardTransactionProcessor,
		CoinCashConversionRateRepo:   coinCashConversionRateRepo,
		RewardTransactionRepo:        rewardTransactionRepo,
		Config:                       config,
	}
}
