package throttled_order

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type ThrottledOrderQueryReq struct {
	IsProcessed          *bool    `json:"isProcessed"`
	ZoneID               string   `json:"zoneId"`
	ObjectIDs            []string `json:"objectIds"`
	OrderIDs             []string `json:"orderIds"`
	IgnoreShouldPickupAt bool     `json:"ignoreShouldPickupAt"`
}

func (r ThrottledOrderQueryReq) ToThrottledOrderQuery() model.ThrottledOrderQuery {
	var zoneID *primitive.ObjectID
	var objectIDs []primitive.ObjectID

	if r.ZoneID != "" {
		oid, err := primitive.ObjectIDFromHex(r.ZoneID)
		if err == nil {
			zoneID = &oid
		}
	}

	for _, id := range r.ObjectIDs {
		if oid, err := primitive.ObjectIDFromHex(id); err == nil {
			objectIDs = append(objectIDs, oid)
		}
	}

	return model.ThrottledOrderQuery{
		IsProcessed:          r.IsProcessed,
		ZoneID:               zoneID,
		ObjectIDs:            objectIDs,
		OrderIDs:             r.OrderIDs,
		IgnoreShouldPickupAt: r.IgnoreShouldPickupAt,
	}
}

type FindRequest struct {
	Query ThrottledOrderQueryReq `json:"query"`
	Skip  int                    `json:"skip"`
	Limit int                    `json:"limit"`
}

type FindResponse struct {
	ThrottledOrders []ThrottledOrderRes `json:"throttledOrders"`
}

type UpdateMpShouldPickupAtRequest struct {
	MpID           string    `json:"mpId"`
	ShouldPickupAt time.Time `json:"shouldPickupAt"`
}

type UpdateMpShouldPickupAtResponse struct {
}

type UpdateMpShouldPickupAtAndInvalidateRequest struct {
	MpID           string
	ShouldPickupAt time.Time
}

type UpdateMpShouldPickupAtAndInvalidateResponse struct {
}

type ThrottledOrderRes struct {
	ID              primitive.ObjectID `json:"_id,omitempty"`
	OrderID         string             `json:"orderId"`
	DeliveringRound int                `json:"deliveringRound"`
	ZoneID          primitive.ObjectID `json:"zoneId"`
	MpID            string             `json:"mpId,omitempty"`
	FromLat         float64            `json:"fromLat"`
	FromLng         float64            `json:"fromLng"`
	Invalidated     bool               `json:"invalidated"`
	ShouldPickupAt  time.Time          `bson:"shouldPickupAt,omitempty"`
	ProcessedAt     time.Time          `json:"processedAt,omitempty"`
	CreatedAt       time.Time          `json:"createdAt"`
	UpdatedAt       time.Time          `json:"updatedAt"`
}

func toThrottledOrderResList(in []model.ThrottledOrder) []ThrottledOrderRes {

	res := make([]ThrottledOrderRes, len(in))
	for i, order := range in {
		res[i] = toThrottledOrderRes(order)
	}
	return res
}

func toThrottledOrderRes(order model.ThrottledOrder) ThrottledOrderRes {
	return ThrottledOrderRes{
		ProcessedAt:     order.ProcessedAt,
		CreatedAt:       order.CreatedAt,
		UpdatedAt:       order.UpdatedAt,
		ID:              order.ID,
		OrderID:         order.OrderID,
		DeliveringRound: order.DeliveringRound,
		ZoneID:          order.ZoneID,
		MpID:            order.MpID,
		FromLat:         order.FromLat,
		FromLng:         order.FromLng,
		Invalidated:     order.Invalidated,
		ShouldPickupAt:  order.ShouldPickupAt,
	}
}
