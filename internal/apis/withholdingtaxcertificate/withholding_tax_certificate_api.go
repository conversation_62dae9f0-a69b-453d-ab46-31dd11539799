package withholdingtaxcertificate

import (
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
)

type Config struct {
	MaxTawi50            int           `envconfig:"MAX_TAWI50" default:"1"`
	Tawi50Expiration     time.Duration `envconfig:"TAWI50_EXPIRATION" default:"15m"`
	VOSEndpoint          string        `envconfig:"VOS_ENDPOINT" required:"true"`
	VOSPublicCdnEndpoint string        `envconfig:"VOS_PUBLIC_CDN_ENDPOINT" default:""`
}

func ProvideConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)

	return
}

type WithholdingTaxCertificateAPI struct {
	wtcRepo    repository.WithholdingTaxCertificateRepository
	vosService service.VOSService
	cfg        Config
}

func (tawi WithholdingTaxCertificateAPI) Get(gCtx *gin.Context) {
	driverID := driver.DriverIDFromGinContext(gCtx)
	tawi50, err := tawi.wtcRepo.FindByDriverIdOrderByYearDesc(gCtx, driverID, 0, tawi.cfg.MaxTawi50)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	items := make([]WithholdingTaxCertificateItem, 0)

	for _, certificate := range tawi50 {
		fileLocation, err := tawi.vosService.GetCDNPreSignedUrl(gCtx, certificate.Tawi50ID, tawi.cfg.Tawi50Expiration)
		if err == nil {
			if tawi.cfg.VOSPublicCdnEndpoint != "" {
				fileLocation = strings.Replace(fileLocation, tawi.cfg.VOSEndpoint, tawi.cfg.VOSPublicCdnEndpoint, 1)
			}
			items = append(items, WithholdingTaxCertificateItem{certificate.Year, fileLocation})
		} else {
			logrus.Warnf("WithholdingTaxCertificateApi GetPreSignedUrl driver id : %v, error: %v", certificate.DriverID, err)
		}
	}

	apiutil.OK(gCtx, WithholdingTaxCertificateRes{
		Items: items,
	})
}

func ProvideWithholdingTaxCertificateAPI(wtcRepo repository.WithholdingTaxCertificateRepository, vosService service.VOSService, cfg Config) *WithholdingTaxCertificateAPI {
	return &WithholdingTaxCertificateAPI{wtcRepo: wtcRepo, vosService: vosService, cfg: cfg}
}
