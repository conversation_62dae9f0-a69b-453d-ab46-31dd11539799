package order

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/datastore/mock_redis"
)

func TestMemLocker(t *testing.T) {
	t.Parallel()

	ctx := context.Background()

	const orderID = "LMFT-1231231"
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	cache := mock_redis.NewMockUniversalClient(ctrl)

	c1 := cache.EXPECT().
		SetNX(ctx, "lock_order:"+orderID, 1, 5*time.Minute).
		Return(redis.NewBoolResult(true, nil))
	cache.EXPECT().
		Del(ctx, "lock_order:"+orderID).
		Return(redis.NewIntResult(1, nil)).
		After(c1)

	locker := ProvideMemLocker(cache)

	ok, err := locker.Lock(ctx, orderID)
	require.True(t, ok)
	require.NoError(t, err)

	require.NoError(t, locker.Unlock(ctx, orderID))
}
