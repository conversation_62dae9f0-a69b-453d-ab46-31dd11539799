package distribution_test

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive/mock_incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution/mock_assigning_state"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment/mock_payment"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery/mock_delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher/mock_dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/experimentplatform/mock_experimentplatform"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fleetorder/mock_fleetorder"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker/mock_locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice/mock_mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep/mock_rep"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore/mock_redis"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/mock_event_bus"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/user/mock_grpc"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/cleanup"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/sim/mock_sim"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric/mock_metrics"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric/testmetric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type mocks struct {
	mockOrderRepository                      *mock_repository.MockOrderRepository
	mockDriverRepository                     *mock_repository.MockDriverRepository
	mockLocationManager                      *mock_service.MockLocationManager
	mockNotifier                             *mock_service.MockNotifier
	mockAssigmentLogRepo                     *mock_repository.MockAssignmentLogRepository
	mockDriverTransaction                    *mock_payment.MockDriverTransactionService
	mockStatisticRepo                        *mock_repository.MockDriverStatisticRepository
	mockStatisticService                     *mock_service.MockStatisticService
	mockDriverOrderInfoRepo                  *mock_repository.MockDriverOrderInfoRepository
	mockIncentiveRepo                        *mock_incentive.MockIncentiveRepository
	mockLocker                               *mock_locker.MockLocker
	stubMeter                                *testmetric.StubMeter
	rep                                      *mock_rep.MockREPService
	mockEventBus                             *mock_event_bus.MockEventBus
	mockPredictionService                    *mock_service.MockPredictionService
	mockTxnHelper                            *mock_transaction.MockTxnHelper
	mockDriverService                        *mock_service.MockDriverServiceInterface
	mapService                               *mock_mapservice.MockMapService
	dedicatedZoneRepo                        *mock_repository.MockDedicatedZoneRepository
	mockDriverLocationRepository             *mock_repository.MockDriverLocationRepository
	mockTripRepository                       *mock_repository.MockTripRepository
	mockOnTopFareService                     *mock_service.MockOnTopFareService
	mockThrottledDispatchDetailRepository    *mock_repository.MockThrottledDispatchDetailRepository
	mockThrottledOrderRepository             *mock_repository.MockThrottledOrderRepository
	mockDispatcher                           *mock_dispatcher.MockDispatcher
	mockDeferredOrderRepository              *mock_repository.MockDeferredOrderRepository
	mockAssignmentRepository                 *mock_repository.MockAssignmentRepository
	mockRainSituationService                 *mock_service.MockRainSituationService
	mockServiceAreaRepository                *mock_repository.MockServiceAreaRepository
	mockZoneRepository                       *mock_repository.MockZoneRepository
	mockUserClient                           *mock_grpc.MockUserServiceClient
	mockRedisClient                          *mock_redis.MockUniversalClient
	mockDelivery                             *mock_delivery.MockDelivery
	mockEnvironment                          *mock_sim.MockEnvironment
	mockServicePreferenceService             *mock_service.MockServicePreferenceService
	mockDistributionLogManager               *mock_service.MockDistributionLogManager
	mockHearthBeatService                    *mock_service.MockOrderHeartbeatService
	mockOrderDistributionEventManager        *mock_service.MockOrderDistributionEventManager
	mockDistributionExperimentPlatformClient *mock_experimentplatform.MockDistributionExperimentPlatformClient
	mockDistributionService                  *mock_service.MockDistributionService
	mockFeatureFlagService                   *mock_featureflag.MockService
	mockMetricsRegistry                      *mock_metrics.MockMetricsRegistry
	mockFleetOrderClient                     *mock_fleetorder.MockFleetOrderClient
	mockIllegalDriverRepo                    *mock_repository.MockIllegalDriverRepository
	mockAssigningStateManager                *mock_assigning_state.MockAssigningStateManager
}

func initDriverLocations(ctx context.Context, m *mocks, location model.Location, km float64, driverWithLocations []service.DriverWithLocation, searchRiderStrategy model.SearchRiderStrategy) {
	m.mockLocationManager.EXPECT().SearchDriversLimit(gomock.Any(), location, km*1000, 5000, gomock.Any(), gomock.Any(), gomock.Any()).Return(service.SearchResult{
		Results:  driverWithLocations,
		Strategy: searchRiderStrategy,
	}, nil)

	for _, dl := range driverWithLocations {
		driverID := dl.Driver.DriverID
		driver := dl.Driver
		m.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).
			Return(map[string]*model.DriverMinimal{
				driverID: &driver,
			}, nil).AnyTimes()
	}
}

func setDriverTransaction(ctx context.Context, m *mocks, txn model.DriverTransaction) {
	m.mockDriverTransaction.EXPECT().GetDriverTransaction(gomock.Any(), txn.DriverID, gomock.Any()).Return(txn, nil).AnyTimes()
}

func createDriverTransaction(driverID string) model.DriverTransaction {
	txn := model.NewDriverTransaction(driverID)
	txn.Cash = types.Money(1000)

	return *txn
}

func createBanWithdrawDriverTransaction(driverID string) model.DriverTransaction {
	txn := model.NewDriverTransaction(driverID)
	txn.Cash = types.Money(1000)
	txn.BanWithdraw("test", "test", "test")

	return *txn
}

func initDriverStatistics(ctx context.Context, mocks *mocks, driverIDs []string) {
	mocks.mockDriverOrderInfoRepo.EXPECT().GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DriverOrderInfo{}, nil)
}

func createMinimalZone() model.Zone {
	return model.Zone{ // minimal version
		ID:       primitive.NewObjectID(),
		ZoneCode: "zone-code-1",
		Region:   "AYUTTHAYA",
		Geometry: model.ZoneGeometry{},
	}
}

func createMinimalOrder(orderID string) model.Order {
	o := model.Order{
		OrderID: orderID,
		Status:  model.StatusAssigningDriver,
		Quote: model.Quote{
			Metadata: make(map[string]string),
			Routes: []model.Stop{
				{
					Location: model.Location{Lat: 1, Lng: 2},
					Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{
						Service:        "food",
						PriceScheme:    "RMS",
						RestaurantType: "RMS",
					}},
				},
				{
					Phones: []string{"0891234567"},
				},
			},
			DistributeRegions: model.DistributeRegions{"AYUTTHAYA"},
		},
	}
	o.Quote.ServiceType = model.ServiceFood

	// pay at stop 1 for food
	o.Routes[1].PriceSummary = model.PriceSummary{
		ItemFee: model.ItemFeeSummary{
			PaymentMethod: model.PaymentMethodCash,
		},
	}

	o.ExpireAt = time.Now().Add(time.Hour)

	return o
}

func createPredictedOrder(orderID string) model.Order {
	o := createMinimalOrder(orderID)
	o.Prediction = &model.PredictionFeatures{
		EstimatedCookingTimeSecond:     11,
		EstimatedUserWaitingTimeSecond: 12,
	}
	return o
}

func createBikeStandingData() (context.Context, *sync.WaitGroup, model.Order, time.Time, model.Location, []model.RegionCode) {
	ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
	o.ServiceType = model.ServiceBike
	o.Routes[0].Phones, o.Routes[1].Phones = o.Routes[1].Phones, o.Routes[0].Phones
	return ctx, wg, o, expireAt, businessLocation, regions
}

func createStandingData() (context.Context, *sync.WaitGroup, model.Order, time.Time, model.Location, []model.RegionCode) {
	ctx, wg := safe.CreateCtxWithWaitGroup()

	o := createMinimalOrder("LM1234")

	expireAt := time.Now().Add(time.Hour)
	businessLocation := o.Routes[0].Location
	regions := []model.RegionCode{"AYUTTHAYA"}
	return ctx, wg, o, expireAt, businessLocation, regions
}

func newDriverRecords(driverIDs ...string) []model.Record {
	logs := make([]model.Record, len(driverIDs))
	for i, driverID := range driverIDs {
		logs[i] = model.Record{DriverID: driverID}
	}
	return logs
}

func createDriver(id string, status model.DriverStatus, options model.Options) model.DriverMinimal {
	d := model.DriverMinimal{
		DriverID: id,
		Status:   status,
		Options:  options,
		Phone:    crypt.NewLazyEncryptedString("0891234566"),
	}
	d.Region = "AYUTTHAYA"

	return d
}

func createTestDriver(id string, status model.DriverStatus, options model.Options) model.DriverMinimal {
	d := model.DriverMinimal{
		DriverID:   id,
		Status:     status,
		Options:    options,
		DriverType: crypt.NewLazyEncryptedString("TESTER"),
		Phone:      crypt.NewLazyEncryptedString("0891234569"),
	}
	d.Region = "AYUTTHAYA"

	return d
}

func createFraudDriver(id string, status model.DriverStatus, options model.Options) model.DriverMinimal {
	d := model.DriverMinimal{
		DriverID: id,
		Status:   status,
		Options:  options,
		Phone:    crypt.NewLazyEncryptedString("0891234567"),
	}
	d.Region = "AYUTTHAYA"

	return d
}

func createDriverWithLocation(d model.DriverMinimal, location model.Location, distanceInMeter float64) service.DriverWithLocation {
	return service.DriverWithLocation{
		Driver:        d,
		Location:      location,
		DistanceMeter: distanceInMeter,
	}
}

func createAutoAssignOrderDistributorWithAutoAssignCfg(t *testing.T, autoAssignCfg dispatcherconfig.AutoAssignDbConfig) (*distribution.AutoAssignOrderDistributor, *mocks, model.AutoAssignDistribution, func()) {
	cfg := distribution.ProvideAutoAssignConfig(
		&config.AtomicBackToBackConfig{},
		&dispatcherconfig.AtomicAutoAcceptConfig{},
		&dispatcherconfig.AtomicAutoAssignDbConfig{
			Config: autoAssignCfg,
		},
		&config.AtomicSupplyPositioningConfig{},
		&config.AtomicDedicatedPriorityScorerConfig{},
	)
	return createAutoAssignOrderDistributor(t, order.ContingencyConfig{}, *cfg)
}

func createAutoAssignOrderDistributorWithContingencyCfg(t *testing.T, contingencyCfg order.ContingencyConfig) (*distribution.AutoAssignOrderDistributor, *mocks, model.AutoAssignDistribution, func()) {
	cfg := distribution.ProvideAutoAssignConfig(
		&config.AtomicBackToBackConfig{},
		&dispatcherconfig.AtomicAutoAcceptConfig{},
		&dispatcherconfig.AtomicAutoAssignDbConfig{
			Config: dispatcherconfig.AutoAssignDbConfig{
				DisableThrottledDispatch:         true,
				DisableRedistributeByDispatcher:  true,
				RedistributionLimit:              10,
				DeferredOrderRedistributionLimit: 360,
				SwitchFlowRedistributionLimit:    360,
			},
		},
		&config.AtomicSupplyPositioningConfig{},
		&config.AtomicDedicatedPriorityScorerConfig{},
	)
	return createAutoAssignOrderDistributor(t, contingencyCfg, *cfg)
}

func createAutoAssignOrderDistributor(t *testing.T, contingencyCfg order.ContingencyConfig, autoAssignCfg distribution.AutoAssignConfig) (*distribution.AutoAssignOrderDistributor, *mocks, model.AutoAssignDistribution, func()) {
	autoAssignCfg.LimitDriverFetch = 5000
	ctrl := gomock.NewController(t)

	wgCtx := safe.ProvideWorkerContext(nil, cleanup.ProvideCleanupPriority())

	m := mocks{
		mockOrderRepository:                      mock_repository.NewMockOrderRepository(ctrl),
		mockDriverRepository:                     mock_repository.NewMockDriverRepository(ctrl),
		mockLocationManager:                      mock_service.NewMockLocationManager(ctrl),
		mockNotifier:                             mock_service.NewMockNotifier(ctrl),
		mockAssigmentLogRepo:                     mock_repository.NewMockAssignmentLogRepository(ctrl),
		mockDriverTransaction:                    mock_payment.NewMockDriverTransactionService(ctrl),
		mockStatisticRepo:                        mock_repository.NewMockDriverStatisticRepository(ctrl),
		mockIncentiveRepo:                        mock_incentive.NewMockIncentiveRepository(ctrl),
		mockStatisticService:                     mock_service.NewMockStatisticService(ctrl),
		mockDriverOrderInfoRepo:                  mock_repository.NewMockDriverOrderInfoRepository(ctrl),
		mockLocker:                               mock_locker.NewMockLocker(ctrl),
		stubMeter:                                testmetric.NewStubMeter(),
		rep:                                      mock_rep.NewMockREPService(ctrl),
		mockEventBus:                             mock_event_bus.NewMockEventBus(ctrl),
		mockPredictionService:                    mock_service.NewMockPredictionService(ctrl),
		mockTxnHelper:                            mock_transaction.NewMockTxnHelper(ctrl),
		mockDriverService:                        mock_service.NewMockDriverServiceInterface(ctrl),
		mapService:                               mock_mapservice.NewMockMapService(ctrl),
		dedicatedZoneRepo:                        mock_repository.NewMockDedicatedZoneRepository(ctrl),
		mockTripRepository:                       mock_repository.NewMockTripRepository(ctrl),
		mockDriverLocationRepository:             mock_repository.NewMockDriverLocationRepository(ctrl),
		mockOnTopFareService:                     mock_service.NewMockOnTopFareService(ctrl),
		mockThrottledDispatchDetailRepository:    mock_repository.NewMockThrottledDispatchDetailRepository(ctrl),
		mockThrottledOrderRepository:             mock_repository.NewMockThrottledOrderRepository(ctrl),
		mockDispatcher:                           mock_dispatcher.NewMockDispatcher(ctrl),
		mockDeferredOrderRepository:              mock_repository.NewMockDeferredOrderRepository(ctrl),
		mockAssignmentRepository:                 mock_repository.NewMockAssignmentRepository(ctrl),
		mockRainSituationService:                 mock_service.NewMockRainSituationService(ctrl),
		mockServiceAreaRepository:                mock_repository.NewMockServiceAreaRepository(ctrl),
		mockZoneRepository:                       mock_repository.NewMockZoneRepository(ctrl),
		mockUserClient:                           mock_grpc.NewMockUserServiceClient(ctrl),
		mockRedisClient:                          mock_redis.NewMockUniversalClient(ctrl),
		mockDelivery:                             mock_delivery.NewMockDelivery(ctrl),
		mockEnvironment:                          mock_sim.NewMockEnvironment(ctrl),
		mockServicePreferenceService:             mock_service.NewMockServicePreferenceService(ctrl),
		mockDistributionLogManager:               mock_service.NewMockDistributionLogManager(ctrl),
		mockHearthBeatService:                    mock_service.NewMockOrderHeartbeatService(ctrl),
		mockOrderDistributionEventManager:        mock_service.NewMockOrderDistributionEventManager(ctrl),
		mockDistributionExperimentPlatformClient: mock_experimentplatform.NewMockDistributionExperimentPlatformClient(ctrl),
		mockDistributionService:                  mock_service.NewMockDistributionService(ctrl),
		mockFeatureFlagService:                   mock_featureflag.NewMockService(ctrl),
		mockMetricsRegistry:                      mock_metrics.NewMockMetricsRegistry(ctrl),
		mockFleetOrderClient:                     mock_fleetorder.NewMockFleetOrderClient(ctrl),
		mockIllegalDriverRepo:                    mock_repository.NewMockIllegalDriverRepository(ctrl),
		mockAssigningStateManager:                mock_assigning_state.NewMockAssigningStateManager(ctrl),
	}

	autoAssignOrderDistributor, cleanUpFn := distribution.ProvideAutoAssignOrderDistributor(
		distribution.ProvideAutoAssignOrderDistributorDeps(
			m.mockLocationManager,
			m.mockOrderRepository,
			m.mockDriverRepository,
			m.mockNotifier,
			m.mockAssigmentLogRepo,
			m.mockDriverTransaction,
			m.mockStatisticRepo,
			m.mockStatisticService,
			m.mockDriverOrderInfoRepo,
			m.mockIncentiveRepo,
			m.mockLocker,
			order.OrderAPIConfig{CashCollectionEnabled: true, AtomicOrderDBConfig: order.NewAtomicOrderDBConfig(order.OrderDBConfig{Commisson: 5, WithHoldingTax: 0.5})},
			m.stubMeter,
			m.rep,
			order.NewAtomicContingencyConfig(contingencyCfg),
			m.mockEventBus,
			dispatcherconfig.NewAtomicDistributionConfig(dispatcherconfig.DistributionConfig{
				RetryAttemptsLockingDriver: 3,
				// Shouldn't affect auto assign regions since the config should only be applied to broadcast B2B
				LastAcceptWithin: 3 * time.Hour,
			}),
			m.mockPredictionService,
			m.mockTxnHelper,
			&distribution.AutoAssignConfig{},
			nil,
			m.mockDriverService,
			m.mapService,
			m.dedicatedZoneRepo,
			m.mockDriverLocationRepository,
			m.mockTripRepository,
			m.mockOnTopFareService,
			m.mockThrottledDispatchDetailRepository,
			m.mockThrottledOrderRepository,
			m.mockDispatcher,
			m.mockDeferredOrderRepository,
			m.mockAssignmentRepository,
			m.mockRainSituationService,
			m.mockServiceAreaRepository,
			m.mockZoneRepository,
			m.mockUserClient,
			m.mockRedisClient,
			m.mockDelivery,
			wgCtx,
			m.mockServicePreferenceService,
			m.mockDistributionLogManager,
			m.mockHearthBeatService,
			m.mockOrderDistributionEventManager,
			m.mockDistributionExperimentPlatformClient,
			m.mockDistributionService,
			m.mockFeatureFlagService,
			m.mockMetricsRegistry,
			m.mockFleetOrderClient,
			m.mockIllegalDriverRepo,
			m.mockAssigningStateManager,
			config.ThrottledOrderDBConfig{},
		),
	)
	autoAssignOrderDistributor.Config = autoAssignCfg
	autoAssignOrderDistributor.OrderConfig.OrderExpirationDuration = 10 * time.Minute

	distCfg := createAutoAssignDistributionConfig()
	m.mockIllegalDriverRepo.EXPECT().GetIllegalDrivers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	m.mockServicePreferenceService.EXPECT().OptOutAllowedServicesFromPreferenceWithWhitelist(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, false, nil).AnyTimes()
	m.mockServicePreferenceService.EXPECT().OptOutAllowedServicesFromPreference(gomock.Any(), gomock.Any()).Return(nil, false, nil).AnyTimes()
	m.mockServicePreferenceService.EXPECT().GetExperimentWhitelistedDriverIDs().Return(nil).AnyTimes()

	m.mockDistributionLogManager.EXPECT().CaptureSearchEvent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	m.mockDistributionLogManager.EXPECT().CapturePreFilterEvent(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	m.mockDistributionLogManager.EXPECT().CapturePreFilterWithTargetOrdersEvent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	m.mockDistributionLogManager.EXPECT().CapturePreOptimizeFilterEvent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	m.mockDistributionLogManager.EXPECT().CapturePostFilterEvent(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	m.mockDistributionLogManager.EXPECT().CapturePostFilterWithTargetOrdersEvent(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

	m.mockOrderDistributionEventManager.EXPECT().PublishCreated(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	m.mockOrderDistributionEventManager.EXPECT().PublishSearchingOrAssigningDriver(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	m.mockOrderDistributionEventManager.EXPECT().PublishDriverMatched(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	return autoAssignOrderDistributor,
		&m,
		*distCfg,
		func() {
			ctrl.Finish()
			cleanUpFn()
		}
}

// stubDefaultBehavior stub mock for default behavior expectation, this must be called as last mocking step before execute target test function
func stubDefaultBehavior(m *mocks) {
	m.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	m.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(false).AnyTimes()
}

func createAutoAssignDistributionConfig() *model.AutoAssignDistribution {
	cfg := model.InitDefaultAutoAssignDistribution()

	cfg.MinRadiusInKm = 0
	cfg.MaxRadiusInKm = 5
	cfg.DistanceScoreWeight = 1
	cfg.AcceptingDurationInSecond = 5
	cfg.AcceptanceScoreWeight = 1
	cfg.IncentiveScoreWeight = 0
	cfg.IdleScoreWeight = 0
	cfg.RedistributionDelay = 50 * time.Millisecond

	return &cfg
}

func predictCallWithCookingTime(cookingTime int) func(prediction *model.PredictionFeatures) {
	return func(prediction *model.PredictionFeatures) {
		prediction.EstimatedCookingTimeSecond = cookingTime
	}
}
