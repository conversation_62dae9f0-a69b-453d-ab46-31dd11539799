package distribution

//go:generate mockgen -source=./distribution.go -destination=./mock_distribution.go -package=distribution

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/sync/singleflight"
	"google.golang.org/grpc/metadata"

	"git.wndv.co/go/logx/v2"
	userPb "git.wndv.co/go/proto/lineman/auth/v1"
	"git.wndv.co/lineman/absinthe/api/middleware"
	orderapi "git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/sets"
)

const thailandPhoneNumberPrefix = "+66"

type driverSelector func(ctx context.Context, driver model.DriverMinimal) (passed bool, filterName model.RiderFilterName, msg string)

func and(selectors ...driverSelector) driverSelector {
	return func(ctx context.Context, driver model.DriverMinimal) (bool, model.RiderFilterName, string) {
		for _, s := range selectors {
			if p, filterName, m := s(ctx, driver); !p {
				return false, filterName, m
			}
		}
		return true, "", ""
	}
}

func nonExDriver(orderID string, orderRepo repository.OrderRepository) driverSelector {
	ids, err := orderRepo.GetExDrivers(context.Background(), orderID)
	if err != nil {
		logrus.Errorf("can't get order's ex-drivers. orderID: %s, err: %v", orderID, err)
	}
	return func(ctx context.Context, driv model.DriverMinimal) (bool, model.RiderFilterName, string) {
		return !(err == nil && ids.Has(driv.DriverID)), model.ExDriver, "the driver must not be in the ex-driver list"
	}
}

func nonIllegalDriver(illegalDriverIDs types.StringSet, orderID string, deliveringRound int, illegalDriverRepo repository.IllegalDriverRepository, autoAssignDBConfig dispatcherconfig.AutoAssignDbConfig) driverSelector {
	var illegalDriversData *model.RidersIllegalData
	if !autoAssignDBConfig.DisableIllegalDriverFilterByRedis {
		var err error
		illegalDriversData, err = illegalDriverRepo.GetIllegalDrivers(context.Background(), orderID, deliveringRound)
		if err != nil {
			logrus.Errorf("can't get order's illegal-drivers from Redis. orderID: %s, err: %v", orderID, err)
		}
	}
	return func(ctx context.Context, driv model.DriverMinimal) (bool, model.RiderFilterName, string) {
		if illegalDriversData != nil && illegalDriversData.IsInitialized() {
			if isIllegalDriver, illegalData := illegalDriversData.Get(driv.DriverID); isIllegalDriver {
				return false, model.IllegalDriver.WithSuffix(illegalData.Reason), "the driver must not be in the illegal-driver list"
			}
		}
		// For backward compatibility, only supports cases where driver is permanently illegal to assigning orders
		if !autoAssignDBConfig.DisableIllegalDriverFilter && illegalDriverIDs.IsInitialized() && illegalDriverIDs.Has(driv.DriverID) {
			return false, model.IllegalDriver, "the driver must not be in the illegal-driver list"
		}
		return true, "", ""
	}
}

func isMockDriver(mockDriverID string) driverSelector {
	return func(ctx context.Context, driv model.DriverMinimal) (bool, model.RiderFilterName, string) {
		return driv.DriverID == mockDriverID, model.RiderNotMocked, "the driver must be mock driver"
	}
}

// calculateCreditWalletIsEnough is function that checks driver credit and wallet is enough for accepting ord.
func calculateCreditWalletIsEnough(drivtrans payment.DriverTransactionService, ord *model.Order, repSvc rep.REPService,
	enableCheckDriverBalanceEnough bool, contingencyCfg orderapi.ContingencyConfig, negativeBalanceGroups []model.NegativeBalanceGroupConfig,
	tierNegBalCfg model.TierNegativeBalanceConfig, isCashCollectionEnabled bool, memoizer *selectorMemoizer) driverSelector {
	return func(ctx context.Context, driv model.DriverMinimal) (bool, model.RiderFilterName, string) {
		drivTrans, err := memoizer.getDriverTransaction(ctx, &driv, drivtrans)
		if err != nil {
			logrus.Errorf("distribute: cannot get driver transaction: %v", err)
			return false, model.DriverTransactionNotFound, fmt.Sprintf("cannot get driver transaction for credit wallet selector (err=%v)", err)
		}

		var isCreditWalletEnough bool
		if ord.RevenuePrincipalModel || (isCashCollectionEnabled && ord.IsCashCollection()) {
			enough, _, outstanding := model.ValidateDriverBalanceEnough(drivTrans, ord)

			if !enough {
				negativeCfg := model.GetNegativeGroupByName(negativeBalanceGroups, driv.NegativeBalanceGroup)

				if isOutstandingWithinMinimumCreditBalance(driv.DriverID, outstanding, contingencyCfg, negativeCfg,
					tierNegBalCfg, driv.DriverTier) || !enableCheckDriverBalanceEnough {
					enough = true
				}
			}

			isCreditWalletEnough = enough
		} else {
			isCreditWalletEnough, _ = model.ValidateDriverCreditEnough(drivTrans.CreditBalance().Float64(), ord)
		}

		return isCreditWalletEnough, model.CreditWalletNotEnough, "the driver's credit wallet balance must be enough"
	}
}

func isOutstandingWithinMinimumCreditBalance(driverId string, outstanding types.Money, contingencyConfig orderapi.ContingencyConfig,
	negativeBalanceConfig model.NegativeBalanceGroupConfig, tierNegBalCfg model.TierNegativeBalanceConfig, tier model.DriverTier) bool {
	limitMinusCredit := types.NewMoney(0)
	if contingencyConfig.ContingencyModeEnabled {
		limitMinusCredit = types.NewMoney(contingencyConfig.LimitMinusCredit)
	}

	if negativeBalanceConfig.Enabled {
		negativeBalanceLimit := types.NewMoney(negativeBalanceConfig.MinimumCreditBalance)

		if negativeBalanceLimit.LT(limitMinusCredit) {
			limitMinusCredit = negativeBalanceLimit
		}

	} else if tierNegBalCfg.Enabled && !tierNegBalCfg.IsDriverBlacklisted(driverId) {
		if balance, exist := tierNegBalCfg.NegativeBalanceByTier[tier]; exist {
			negativeBalanceLimit := types.NewMoney(balance)
			if negativeBalanceLimit.LT(limitMinusCredit) {
				limitMinusCredit = negativeBalanceLimit
			}
		}
	}

	if limitMinusCredit.NE(0) {
		return outstanding.LTE(limitMinusCredit.Abs())
	}

	return false
}

type memoizerField[T any] struct {
	group      *singleflight.Group
	valueTable map[string]T
	valueLock  *sync.RWMutex
	errTable   map[string]error
	errLock    *sync.RWMutex
}

func (mf *memoizerField[T]) init() memoizerField[T] {
	return memoizerField[T]{
		group:      new(singleflight.Group),
		valueTable: make(map[string]T),
		errTable:   make(map[string]error),
		valueLock:  new(sync.RWMutex),
		errLock:    new(sync.RWMutex),
	}
}

func (mf *memoizerField[T]) getValue(key string) (T, bool) {
	mf.valueLock.RLock()
	defer mf.valueLock.RUnlock()
	v, e := mf.valueTable[key]
	return v, e
}

func (mf *memoizerField[T]) setValue(key string, value T) {
	mf.valueLock.Lock()
	defer mf.valueLock.Unlock()
	mf.valueTable[key] = value
}

func (mf *memoizerField[T]) getError(key string) (error, bool) {
	mf.errLock.RLock()
	defer mf.errLock.RUnlock()
	v, e := mf.errTable[key]
	return v, e
}

func (mf *memoizerField[T]) setError(key string, err error) {
	mf.errLock.Lock()
	defer mf.errLock.Unlock()
	mf.errTable[key] = err
}

func (mf *memoizerField[T]) memoizeValueErr(f func() (T, error), key string) (T, error) {
	var zero T
	value, err := f()
	if err != nil {
		mf.setError(key, err)
		return zero, err
	}
	mf.setValue(key, value)
	return value, nil
}

func (mf *memoizerField[T]) memoizeSingleFlight(f func() (T, error), key string) (T, error) {
	var zero T
	if err, exists := mf.getError(key); exists {
		return zero, err
	}
	if value, exists := mf.getValue(key); exists {
		return value, nil
	}
	tmp, err, _ := mf.group.Do(key, func() (interface{}, error) {
		return mf.memoizeValueErr(f, key)
	})
	if err != nil {
		return zero, err
	}
	return tmp.(T), err
}

type selectorMemoizer struct {
	driverTransMem      memoizerField[model.DriverTransaction]
	driverUidMem        memoizerField[[]string]
	isTesterMem         memoizerField[bool]
	driverPhoneMem      memoizerField[string]
	deprioritizationMem memoizerField[bool]
	orderZoneCodesMem   memoizerField[[]string]
}

func newSelectorMemoizer() *selectorMemoizer {
	sm := &selectorMemoizer{}
	sm.driverTransMem = sm.driverTransMem.init()
	sm.driverUidMem = sm.driverUidMem.init()
	sm.isTesterMem = sm.isTesterMem.init()
	sm.driverPhoneMem = sm.driverPhoneMem.init()
	sm.deprioritizationMem = sm.deprioritizationMem.init()
	sm.orderZoneCodesMem = sm.orderZoneCodesMem.init()
	return sm
}

func (sm *selectorMemoizer) resetDriverTransactions() {
	sm.driverTransMem.valueTable = make(map[string]model.DriverTransaction)
	sm.driverTransMem.errTable = make(map[string]error)
}

func (sm *selectorMemoizer) getDriverTransaction(ctx context.Context, driver *model.DriverMinimal, svc payment.DriverTransactionService) (model.DriverTransaction, error) {
	return sm.driverTransMem.memoizeSingleFlight(func() (model.DriverTransaction, error) {
		return svc.GetDriverTransaction(ctx, driver.DriverID, repository.WithReadSecondaryPreferred)
	}, driver.DriverID)
}

func (sm *selectorMemoizer) GetDriverUid(ctx context.Context, driver *model.DriverMinimal, redisClient datastore.RedisClient, userClient userPb.UserServiceClient, isLINEUserIDUsed bool) ([]string, error) {
	return sm.driverUidMem.memoizeSingleFlight(func() ([]string, error) {
		dataInCache, err := cache.GetDriverUid(ctx, redisClient, driver.DriverID)
		if err == nil {
			return dataInCache, nil
		}

		userIds, err := sm.getUserIDsFromLINEID(ctx, userClient, driver, isLINEUserIDUsed)
		if err != nil {
			return nil, err
		}

		if err := cache.SetDriverUid(context.Background(), redisClient, driver.DriverID, userIds); err != nil {
			logrus.Errorf("[uid-selector] Unable to set uid of driver %s in redis", driver.DriverID)
		}

		if len(userIds) == 0 {
			logrus.Errorf("[uid-selector] GetUserIDsFromMID but got empty of driver %s", driver.DriverID)
			return nil, nil
		}
		return userIds, nil
	}, driver.DriverID)
}

func (sm *selectorMemoizer) getUserIDsFromLINEID(ctx context.Context, userClient userPb.UserServiceClient, driver *model.DriverMinimal, isLINEUserIDUsed bool) ([]string, error) {
	if isLINEUserIDUsed {
		resp, err := userClient.GetUserIDsFromLineUID(sm.addUserIDMetadataToContext(ctx, driver.LINEUserID), &userPb.GetUserIDsFromLineUIDRequest{
			LineUid:  driver.LINEUserID,
			UseCache: true,
		})
		if err != nil {
			logx.Error().Err(err).Msgf("[uid-selector] Unable to GetUserIDsFromLineUID of driver %s", driver.DriverID)
			return nil, err
		}

		return resp.UserIds, nil
	}

	resp, err := userClient.GetUserIDsFromMID(sm.addUserIDMetadataToContext(ctx, driver.LineUID.String()), &userPb.GetUserIDsFromMIDRequest{
		Mid: driver.LineUID.String(),
	})
	if err != nil {
		logrus.Errorf("[uid-selector] Unable to GetUserIDsFromMID of driver %s err: %v", driver.DriverID, err.Error())
		return nil, err
	}
	return resp.UserIds, nil
}

func (sm *selectorMemoizer) addUserIDMetadataToContext(inCtx context.Context, lineUid string) context.Context {
	outCtx := metadata.NewOutgoingContext(inCtx, metadata.New(
		map[string]string{middleware.LM_USER_ID: lineUid},
	))
	return outCtx
}

func (sm *selectorMemoizer) isTester(driver *model.DriverMinimal) bool {
	v, _ := sm.isTesterMem.memoizeSingleFlight(func() (bool, error) {
		return isTester(*driver), nil
	}, driver.DriverID)
	return v
}

func (sm *selectorMemoizer) driverPhone(driver *model.DriverMinimal) string {
	v, _ := sm.driverPhoneMem.memoizeSingleFlight(func() (string, error) {
		return driver.Phone.String(), nil
	}, driver.DriverID)
	return v
}

func deprioritizationMemKey(driver *model.DriverMinimal, order *model.Order) string {
	return driver.DriverID + ":" + order.OrderID
}

func (sm *selectorMemoizer) isDriverAlreadyDeprioritized(driver *model.DriverMinimal, order *model.Order) bool {
	done, exists := sm.deprioritizationMem.getValue(deprioritizationMemKey(driver, order))

	if exists && done {
		return true
	}

	sm.deprioritizationMem.setValue(deprioritizationMemKey(driver, order), true)
	return false
}

func (sm *selectorMemoizer) getOrderZoneCodes(ctx context.Context, order *model.Order, zoneRepo repository.ZoneRepository) ([]string, error) {
	return sm.orderZoneCodesMem.memoizeSingleFlight(func() ([]string, error) {
		loc := order.Routes[0].Location
		return zoneRepo.FindActiveZoneCodesByLocation(ctx, loc.Lat, loc.Lng, repository.WithReadSecondaryPreferred)
	}, order.OrderID)
}

func calculateCashEnough(drivtrans payment.DriverTransactionService, ord *model.Order, memoizer *selectorMemoizer) driverSelector {
	return func(ctx context.Context, driv model.DriverMinimal) (bool, model.RiderFilterName, string) {
		if !ord.IsCashCollection() && (ord.PriceSummary().ItemFee.PaymentMethod == model.PaymentMethodCash || ord.IsCashAdvancementEpayment()) {
			drivTrans, err := memoizer.getDriverTransaction(ctx, &driv, drivtrans)
			if err != nil {
				logrus.Errorf("distribute: cannot get driver transaction: %v", err)
				return false, model.DriverTransactionNotFound, fmt.Sprintf("cannot get driver transaction for cash selector (err=%v)", err)
			}

			if drivTrans.Cash.LT(types.NewMoney(ord.PriceSummary().ItemFee.SubTotal)) {
				return false, model.NotEnoughCash, "the driver's cash balance must be enough"
			}
		}
		return true, "", ""
	}
}

func checkBanWithdraw(drivtrans payment.DriverTransactionService, memoizer *selectorMemoizer) driverSelector {
	return func(ctx context.Context, driv model.DriverMinimal) (bool, model.RiderFilterName, string) {
		drivTrans, err := memoizer.getDriverTransaction(ctx, &driv, drivtrans)
		if err != nil {
			logrus.Errorf("distribute: cannot get driver transaction: %v", err)
			return false, model.DriverTransactionNotFound, fmt.Sprintf("cannot get driver transaction for ban withdrawal selector (err=%v)", err)
		}

		return !drivTrans.IsBanWithdraw(), model.BannedForWithdrawal, "the driver must not be banned for withdrawal"
	}
}

func inRegion(regions []model.RegionCode) driverSelector {
	checker := model.RegionSetFromList(regions)

	return func(ctx context.Context, driv model.DriverMinimal) (bool, model.RiderFilterName, string) {
		return checker.Contains(driv.Region), model.NotInRegion, fmt.Sprintf("the driver must be in the specific list of region (regions=%v)", regions)
	}
}

func formatPhoneNumber(phoneNumber, phoneNumberPrefix string) string {
	if strings.HasPrefix(phoneNumber, phoneNumberPrefix) {
		return strings.Replace(phoneNumber, phoneNumberPrefix, "0", 1)
	}
	return phoneNumber
}

func hasDistinctPhoneNumberFromUser(userPhoneNumbers types.StringSet, memoizer *selectorMemoizer) driverSelector {
	return func(ctx context.Context, driv model.DriverMinimal) (bool, model.RiderFilterName, string) {
		if memoizer.isTester(&driv) {
			return true, "", ""
		}
		driverPhoneNumber := formatPhoneNumber(memoizer.driverPhone(&driv), thailandPhoneNumberPrefix)
		return !userPhoneNumbers.Has(driverPhoneNumber), model.SamePhoneNumberFromUser, "the driver's phone number must be distinct from the user"
	}
}

func hasDistinctUidFromUser(redisClient datastore.RedisClient, userClient userPb.UserServiceClient, usrUid string, memoizer *selectorMemoizer, isLINEUserIDUsed bool) driverSelector {
	return func(ctx context.Context, driv model.DriverMinimal) (bool, model.RiderFilterName, string) {
		if memoizer.isTester(&driv) {
			return true, "", ""
		}

		DriverUidList, err := memoizer.GetDriverUid(ctx, &driv, redisClient, userClient, isLINEUserIDUsed)
		if err != nil {
			// benefit to driver when user grpc is down or list is empty
			return true, "", ""
		}

		for _, uid := range DriverUidList {
			if uid == usrUid {
				return false, model.SameUIDFromUser, "the driver's uid must be distinct from the user"
			}
		}

		return true, "", ""
	}
}

func isNotTesterSelector(_ context.Context, driv model.DriverMinimal) (bool, model.RiderFilterName, string) {
	return !isTester(driv), model.NotTester, "the driver must not be a tester driver"
}

func isTester(driv model.DriverMinimal) bool {
	return driv.DriverType.String() == string(model.DriverTypeTester) || driv.DriverRole == model.DriverRoleTester
}

func matchDriverServiceTypes(ordSvcType model.Service, disableDedicatedZone bool) driverSelector {
	return func(ctx context.Context, dm model.DriverMinimal) (bool, model.RiderFilterName, string) {
		// For Bike service, we required to have the service in driver profile.
		if (len(dm.ServiceTypes) == 0 && ordSvcType != model.ServiceBike) || (!disableDedicatedZone && len(dm.DedicatedZones) > 0) {
			return true, "", ""
		}

		driverServiceTypes := sets.New(dm.ServiceTypes...)
		return driverServiceTypes.Has(ordSvcType), model.ServiceTypesUnmatched, "the driver must match the service types in the normal round"
	}
}

func checkIfRiderEligibleForMp1(ctx context.Context, ord *model.Order, orderRepository repository.OrderRepository) driverSelector {
	errorGettingOtherActiveMP := false

	var otherMP *model.Order
	if ord.IsMP1ATR() {
		if ord.IsHasAnotherMPOrder() {
			var err error
			otherMP, err = orderRepository.GetOtherActiveMP(ctx, *ord)
			if err != nil && !errors.Is(err, repository.ErrNotFound) {
				errorGettingOtherActiveMP = true
				logrus.Errorf("unable to get other active mp to validate if rider is eilgible for MP1 ATR due to %v", err)
			}
		}
	}

	return func(_ context.Context, dm model.DriverMinimal) (bool, model.RiderFilterName, string) {
		return !errorGettingOtherActiveMP && (otherMP == nil || otherMP.Driver == "" || otherMP.Driver != dm.DriverID), model.Mp1ATRCannotBeAssigned, "MP1 ATR cannot be assigned to a rider holding MP2"
	}
}

func checkIfRiderOptedOut(ordSvcType model.Service, prefService service.ServicePreferenceService, ctx context.Context, pref model.ServicePreference) driverSelector {
	// default to not allowing opt-out (nil/empty slice)
	//optOutAllowedServices, _ := prefService.OptOutAllowedServicesFromPreference(ctx, pref)
	//optOutAllowed := util.InSlice(optOutAllowedServices, ordSvcType)
	driverOptAllowedServices := service.OverrideOptOutAllowedServicesWithWhitelist(prefService.OptOutAllowedServicesFromPreference(ctx, pref))(prefService.GetExperimentWhitelistedDriverIDs())

	return func(ctx context.Context, dm model.DriverMinimal) (bool, model.RiderFilterName, string) {
		optOutAllowedServices, isMergeFoodMartEnabled, _ := driverOptAllowedServices(dm.DriverID)

		optedOut := service.DriverMinimalOptOutSetWithMerge(&dm, optOutAllowedServices, isMergeFoodMartEnabled).Has(ordSvcType)
		return !optedOut, model.RiderOptedOut, "the driver opted out for this order's service type"
	}
}

func checkSilentBanned(ordSvcType model.Service) driverSelector {
	return func(ctx context.Context, dm model.DriverMinimal) (bool, model.RiderFilterName, string) {
		isSilentBanned := false
		for _, serviceType := range dm.ServiceTypesSilentBanned {
			if ordSvcType == serviceType {
				isSilentBanned = true
				break
			}
		}
		return !isSilentBanned, model.RiderSilentBanned, "the driver is silent banned"
	}
}

func isNotOfflineLater(_ context.Context, dm model.DriverMinimal) (bool, model.RiderFilterName, string) {
	isOfflineLater := false
	if dm.OfflineLater {
		if dm.Status == model.StatusAssigned {
			isOfflineLater = true
		} else {
			safe.SentryErrorMessage("while check driver is not turn on offline later, found rider marked offline-later but status isn't ASSIGNED. will not filter out driver", safe.WithDriverID(dm.DriverID))
		}
	}
	return !isOfflineLater, model.OfflineLater, "the driver turn on offline later"
}

func isOnlineOnly(_ context.Context, dm model.DriverMinimal) (bool, model.RiderFilterName, string) {
	return dm.Status == model.StatusOnline, model.RiderNotOnline, "the driver must be online only"
}

func isOnlineOrAssigned(_ context.Context, dm model.DriverMinimal) (bool, model.RiderFilterName, string) {
	return dm.Status == model.StatusOnline || dm.Status == model.StatusAssigned, model.RiderNotOnlineOrAssigned, "the driver must be online or assigned"
}

func isNotLockedFromQueueing(_ context.Context, dm model.DriverMinimal) (bool, model.RiderFilterName, string) {
	return !dm.IsLockedFromQueueing(), model.RiderLockedFromQueueing, "the driver must not be locked from queueing"
}

func isNotLockedForAcknowledgement(_ context.Context, dm model.DriverMinimal) (bool, model.RiderFilterName, string) {
	isLockedForAcknowledgement := false
	if dm.IsAcknowledgementRequired {
		if dm.Status == model.StatusAssigned {
			isLockedForAcknowledgement = true
		} else {
			safe.SentryErrorMessage("while distributing, found rider marked acknowledgement-required but status isn't ASSIGNED. will distribute anyway", safe.WithDriverID(dm.DriverID))
		}
	}
	return !isLockedForAcknowledgement, model.RiderLockedForAcknowledgement, "the driver must acknowledge before accepting new orders"
}

func deprioritize(ratio float64, excludeZoneCodes []string, atomicSupplyPositioningConfig *config.AtomicSupplyPositioningConfig, zoneRepo repository.ZoneRepository, order *model.Order, memoizer *selectorMemoizer) driverSelector {
	excludeZoneSet := types.NewStringSet(excludeZoneCodes...)

	return func(ctx context.Context, dm model.DriverMinimal) (bool, model.RiderFilterName, string) {
		if isSupplyPositioning(atomicSupplyPositioningConfig, dm, order) {
			return true, "", ""
		}

		// for throttled order, we use throttled round to detect the redistribution
		if order.IsThrottled {
			if (order.IsDeferred && order.ThrottledRound > 2) ||
				(!order.IsDeferred && order.ThrottledRound > 1) ||
				order.ProcessedBySingleDistribution {
				return true, "", ""
			}
		}

		// skip the dedicated zone driver
		if len(dm.DedicatedZones) > 0 {
			return true, "", ""
		}

		// skip non-deprioritized driver
		if !dm.IsDeprioritized {
			return true, "", ""
		}

		// skip if order service is not match deprioritized services
		if !eligibleDeprioritizedServices(dm.ServiceTypesDeprioritized, order.ServiceType) {
			return true, "", ""
		}

		// skip if the order is in exclude zones
		orderZoneCodes, err := memoizer.getOrderZoneCodes(ctx, order, zoneRepo)
		if err != nil {
			logrus.Errorf("can't get order's zone codes. orderID: %s, err: %v", order.OrderID, err)
		}
		if err == nil && len(orderZoneCodes) > 0 && excludeZoneSet.Count() > 0 {
			if excludeZoneSet.HasAny(orderZoneCodes...) {
				return true, "", ""
			}
		}

		// deprioritize the rider only at the first distribution round
		if memoizer.isDriverAlreadyDeprioritized(&dm, order) || rand.Float64() >= ratio {
			return true, "", ""
		}

		return false, model.RiderDeprioritized, "the driver's deprioritized"
	}
}

func eligibleDeprioritizedServices(svcs model.Services, svc model.Service) bool {
	if len(svcs) == 0 {
		return true
	}
	return types.NewStringSet(svcs.String()...).Has(svc.String())
}

func isSupplyPositioning(cfg *config.AtomicSupplyPositioningConfig, dm model.DriverMinimal, order *model.Order) bool {
	return dm.IsSupplyPositioning && cfg.Config.RegionsMap[order.Region.String()]
}

func isTestOrder(ord *model.Order) bool {
	if len(ord.Routes) < 2 {
		return false
	}

	return ord.Routes[1].Name == "testorder"
}

func getUserPhoneNumberFromOrder(order *model.Order) types.StringSet {
	phones := types.NewStringSet()
	if len(order.Routes) < 2 {
		return phones
	}

	for _, phoneNumber := range order.UserPhones() {
		phones.Add(formatPhoneNumber(phoneNumber, thailandPhoneNumberPrefix))
	}
	return phones
}

type OrderDistributor interface {
	Distribute(ctx context.Context, order *model.Order, expireAt time.Time, loc model.Location) (wait func(), err error)
	BatchDistribute(ctx context.Context, throttledOrders []model.ThrottledOrder, zoneID primitive.ObjectID) (wait func(), err error)

	CheckIfCandidatesEnough(ctx context.Context, order *model.Order, loc model.Location, regions []model.RegionCode, distCfg *model.AutoAssignDistribution, areaDistCfg model.AreaDistributionConfig, candidatesRequired int) bool

	Stop()
}
