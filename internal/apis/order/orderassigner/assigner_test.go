package orderassigner_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/orderassigner"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
)

var _ orderassigner.PostFilterLogPublisher = &stubPostFilterLogPublisherRecorder{}

type stubPostFilterLogPublisherRecorder struct {
	FilterData *model.RidersFilterData
	Orders     []model.Order
	Metadata   model.DistributionLogMetadata
}

func (s *stubPostFilterLogPublisherRecorder) CapturePostFilterWithTargetOrdersEvent(_ context.Context, ridersFilterData *model.RidersFilterData, orders []model.Order, metadata model.DistributionLogMetadata) {
	s.FilterData = ridersFilterData
	s.Orders = orders
	s.Metadata = metadata
}

var _ orderassigner.OptOutAllowedLoader = &stubOptOutAllowedLoader{}

type stubOptOutAllowedLoader struct{}

func (s stubOptOutAllowedLoader) OptOutAllowedServicesFromPreferenceWithWhitelist(ctx context.Context, preference model.ServicePreference, driverID string) ([]model.Service, bool, error) {
	return []model.Service{model.ServiceFood}, true, nil
}

func TestCheckAssignmentConstraint(t *testing.T) {
	stubPublisher := &stubPostFilterLogPublisherRecorder{}
	var stubChecker orderassigner.AssignmentConstraintCheckerFunc = func(ctx context.Context, dl *service.DriverWithLocation, newOrders []*model.Order,
		planRoute model.PlanRoute, constraint *orderassigner.AssignmentConstraint) (bool, model.RiderFilterName, string) {
		if dl.Driver.DriverID != "drv001" {
			return false, model.RiderNotMocked, ""
		}
		return true, "", ""
	}

	deps := orderassigner.OrderAssignerTaskDeps{
		PostFilterLogPublisher: stubPublisher,
	}
	state := orderassigner.OrderAssignerTaskState{}
	task := &orderassigner.OrderAssignerTask{OrderAssignerTaskDeps: deps, OrderAssignerTaskState: state}

	orderPtr := new(*model.Order)
	*orderPtr = &model.Order{}
	tests := []struct {
		name       string
		dl         *service.DriverWithLocation
		os         []**model.Order
		planRoute  model.PlanRoute
		constraint orderassigner.AssignmentConstraint
		expected   model.RiderFilterName
	}{
		{
			name:       "filter out when driver location is nil",
			dl:         nil,
			os:         make([]**model.Order, 0),
			planRoute:  model.PlanRoute{},
			constraint: orderassigner.AssignmentConstraint{},
			expected:   model.RiderProfileNotFound,
		},
		{
			name: "filter out when new order is empty",
			dl: &service.DriverWithLocation{
				Driver: model.DriverMinimal{DriverID: "drv001"},
			},
			os:         make([]**model.Order, 0),
			planRoute:  model.PlanRoute{},
			constraint: orderassigner.AssignmentConstraint{},
			expected:   model.OrderNotFound,
		},
		{
			name: "filter out when checker error",
			dl: &service.DriverWithLocation{
				Driver: model.DriverMinimal{DriverID: "invalid_drv"},
			},
			os:         []**model.Order{orderPtr},
			planRoute:  model.PlanRoute{},
			constraint: orderassigner.AssignmentConstraint{},
			expected:   model.ErrorRouting,
		},
		{
			name: "validation pass",
			dl: &service.DriverWithLocation{
				Driver: model.DriverMinimal{DriverID: "drv001"},
			},
			os:         []**model.Order{orderPtr},
			planRoute:  model.PlanRoute{},
			constraint: orderassigner.AssignmentConstraint{},
			expected:   "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ok := task.CheckAssignmentConstraint(context.Background(), tt.dl, tt.os, tt.planRoute, &tt.constraint, []orderassigner.AssignmentConstraintChecker{stubChecker})

			if isEqual := assert.Equal(t, tt.expected == "", ok); !isEqual {
				assert.False(t, ok)
				driverID := ""
				if tt.dl != nil {
					driverID = tt.dl.Driver.DriverID
				}

				filterDataContains(t, stubPublisher.FilterData, tt.expected, driverID)
			}
		})
	}
}

func filterDataContains(t *testing.T, filterData *model.RidersFilterData, filterName model.RiderFilterName, driverID string) bool {
	t.Helper()
	for k, v := range filterData.Result() {
		if k == filterName {
			return assert.Contains(t, v, driverID, "RiderFilterName %s doesn't contain driver id %s", k, driverID)
		}
	}

	return assert.Fail(t, fmt.Sprintf("RidersFilterData doesn't contain filter name %s", filterName))
}
