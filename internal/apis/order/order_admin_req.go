package order

import (
	"encoding/csv"
	"io"
	"mime/multipart"
	"strings"

	"github.com/pkg/errors"

	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
)

var (
	colOrderId = "OrderId"
	colStatus  = "Status"
)

var (
	ErrWrongCSVHeader     = errors.New("wrong csv header")
	ErrDuplicateCSVColumn = errors.New("duplicate csv column")
)

type OrderFraudStatus struct {
	OrderId string
	Status  model.FraudStatus
}

type BulkUpdateOrderFraudStatusReq struct {
	File        *multipart.FileHeader `form:"file" binding:"required"`
	RequestedBy string                `form:"requestedBy" binding:"required"`
}

func rejectWithdrawQuery(driverID string) repository.Query {
	return persistence.TransactionQuery{
		DriverID: driverID,
		Category: model.WalletTransactionCategory,
		Types: []model.TransactionType{
			model.WithdrawTransactionType,
		},
		Statuses: []model.TransactionStatus{
			model.PendingTransactionStatus,
		},
	}
}

func (r *BulkUpdateOrderFraudStatusReq) BulkUpdateOrderFraudStatus() ([]OrderFraudStatus, error) {
	file, err := r.File.Open()
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	columnMapper := make(map[int]string)
	columnSet := make(map[string]bool)

	headers, err := reader.Read()
	if err != nil {
		return nil, err
	}

	if len(headers) < 2 {
		return nil, ErrWrongCSVHeader
	}

	for i, col := range headers {
		switch col {
		case colOrderId, colStatus:
			if _, ok := columnSet[col]; ok {
				return nil, ErrDuplicateCSVColumn
			}
			columnMapper[i] = col
			columnSet[col] = true
		}
	}

	if len(columnSet) != 2 {
		return nil, ErrWrongCSVHeader
	}

	orderFraudStatuses := make([]OrderFraudStatus, 0)
	mulErrors := apiErrors.NewMultipleError()

	for row := 1; true; row++ {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			mulErrors.AddError(apiErrors.NewRowError(row, "entire row", "Cannot read row"))
			continue
		}
		oResult := OrderFraudStatus{}
		for i, val := range record {
			switch columnMapper[i] {
			case colOrderId:
				oResult.OrderId = strings.TrimSpace(val)
			case colStatus:
				oResult.Status = model.FraudStatus(strings.TrimSpace(val))
			}
		}

		orderFraudStatuses = append(orderFraudStatuses, oResult)
	}

	return orderFraudStatuses, nil
}
